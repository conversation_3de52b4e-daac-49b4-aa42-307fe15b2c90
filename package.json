{"name": "k28", "version": "0.0.1", "productName": "K28", "author": "<PERSON>", "scripts": {"lint": "eslint --ext .js,.ts,.vue ./", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build"}, "dependencies": {"@faker-js/faker": "^9.7.0", "@microsoft/signalr": "^8.0.7", "@quasar/extras": "^1.0.0", "@types/qrcode": "^1.5.0", "axios": "^1.2.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.8", "demok100": "file:", "events": "^3.3.0", "localforage": "^1.10.0", "numbro": "^2.4.0", "pinia": "^2.0.11", "qr-scanner": "^1.4.2", "qrcode": "^1.5.3", "quasar": "^2.6.0", "vue": "^3.0.0", "vue-i18n": "^9.2.2", "vue-request": "^2.0.0", "vue-router": "^4.0.0", "vue3-storage": "^0.1.11"}, "devDependencies": {"@intlify/vite-plugin-vue-i18n": "^3.3.1", "@quasar/app-vite": "^1.0.0", "@types/crypto-js": "^4.2.2", "@types/node": "^12.20.21", "@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "autoprefixer": "^10.4.2", "eslint": "^8.10.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-vue": "^9.0.0", "prettier": "^2.5.1", "typescript": "^4.5.4"}, "engines": {"node": "^18 || ^16 || ^14.19", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}