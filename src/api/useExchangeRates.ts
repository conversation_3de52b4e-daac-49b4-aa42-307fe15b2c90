import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

interface ExchangeRate {
  cryptoSymbol: string;
  fiatCode: string;
  spotPrice: number;
  askPrice: number;
  bidPrice: number;
}

type ExchangeRatesRes = Array<ExchangeRate>;

interface UseProps {
  manual?: boolean; // prevent fetching on agent's sidebar navigation
}

const useExchangeRates = (useProps?: UseProps) => {
  const { manual = false } = useProps || {};
  const vueRequest = requestProvider<ExchangeRatesRes>(
    () => {
      const request = axiosProvider.get('/rate').then(({ data }) => data);

      return request;
    },
    {
      manual,
    },
    {
      noFeedback: true,
      noErrorNotify: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export { useExchangeRates };
