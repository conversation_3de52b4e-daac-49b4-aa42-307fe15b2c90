import { defineStore } from 'pinia';
import { type LoginRes } from 'src/pages/auth/api/useLogin';
import { forage } from 'src/utils/foragePkg';

export const useAuthStore = defineStore('auth', {
  state: (): { loginRes: LoginRes | null; proxyToken: string | null } => ({
    loginRes: null,
    proxyToken: null,
  }),
  getters: {
    isLoggedIn: (state) => !!state.loginRes?.token,
    isAgent: (state) => state.loginRes?.roles?.includes('agent'),
  },
  actions: {
    async setLoginRes(data: LoginRes) {
      this.loginRes = data;
      await forage<string>().removeItem('proxyToken');
      await forage<LoginRes>().setItem('loginRes', data);
    },
    async setProxyToken(token: string) {
      this.proxyToken = token;
      await forage<string>().removeItem('loginRes');
      await forage<string>().setItem('proxyToken', token);
    },
    async loadFromStorage() {
      const data = await forage<LoginRes>().getItem('loginRes');
      if (data) this.loginRes = data;
    },
    async logout() {
      this.loginRes = null;
      await forage<LoginRes>().removeItem('loginRes');
    },
  },
});
