import { defineStore } from 'pinia';
import { onMounted, ref, watch } from 'vue';
import { storageHelper } from 'src/utils/foragePkg';

export const useThemeStore = defineStore('theme', () => {
  const defaultIsDark = storageHelper<boolean>('isDark').getItem() ?? false;
  const isDark = ref<boolean>(defaultIsDark);

  onMounted(() => {
    document.body.classList.toggle('isDark', defaultIsDark);
  });

  const toggleTheme = () => {
    isDark.value = !isDark.value;
  };

  // set themes
  watch(isDark, (val) => {
    storageHelper<boolean>('isDark').setItem(val);
    document.body.classList.toggle('isDark', val);
  });

  return {
    isDark,
    toggleTheme,
  };
});
