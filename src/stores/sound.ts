import { defineStore } from 'pinia';

export const useSoundStore = defineStore('sound', {
  state: () => ({
    enabled: true as boolean,
    audio: null as HTMLAudioElement | null,
    currentSound: '',
  }),

  actions: {
    toggle() {
      this.enabled = !this.enabled;
      if (!this.enabled) this.stopSound();
    },

    enable() {
      this.enabled = true;
    },

    disable() {
      this.enabled = false;
      this.stopSound();
    },

    playLoopedSound(src: string) {
      if (!this.enabled) return;

      this.stopSound();

      this.audio = new Audio(src);
      this.audio.loop = true;
      this.audio.play().catch((e) => console.warn('Sound error:', e));
      this.currentSound = src;
    },

    playSound(src: string) {
      if (!this.enabled) return;

      const sound = new Audio(src);
      sound.play().catch((e) => console.warn('Sound error:', e));
    },

    stopSound() {
      if (this.audio) {
        this.audio.pause();
        this.audio.currentTime = 0;
        this.audio = null;
        this.currentSound = '';
      }
    },
  },
});
