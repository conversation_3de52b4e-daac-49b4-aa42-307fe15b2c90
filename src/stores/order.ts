import { defineStore } from 'pinia';
import { OrderPending } from 'src/pages/home/<USER>/usePendingOrder';
import { TradeStatusEnum } from 'src/utils/enums';

export const useOrderStore = defineStore('orderStore', {
  state: (): {
    pendingOrders: OrderPending | null;
    inProgressOrders: OrderPending | null;
    acceptedOrder: number;
    doneOrder: OrderPending | null;
    cancelledOrder: OrderPending | null;
    expiredOrder: OrderPending | null;
    userWaitingOrders: OrderPending | null;
    pendingCount: number;
    inProgressCount: number;
    userOrderCount: number;
  } => ({
    pendingOrders: null,
    inProgressOrders: null,
    acceptedOrder: 0,
    doneOrder: null,
    cancelledOrder: null,
    expiredOrder: null,
    userWaitingOrders: null,
    pendingCount: 0,
    inProgressCount: 0,
    userOrderCount: 0,
  }),

  getters: {
    getPendingOrders: (state) => state.pendingOrders,
    getInprogressOrders: (state) => state.inProgressOrders,
    getUserWaitingOrders: (state) => state.userWaitingOrders,
    getPendingCount: (state) => state.pendingCount,
    getInProgressCount: (state) => state.inProgressCount,
    getUserOrderCount: (state) => state.userOrderCount,
  },

  actions: {
    setOrderRealTime(order: OrderPending) {
      if (
        [TradeStatusEnum.Pending, TradeStatusEnum.Expired].includes(
          order.status
        )
      ) {
        this.pendingOrders = order;
        return 'pending';
      }
      if (
        [
          TradeStatusEnum.Accepted,
          TradeStatusEnum.BankAccountAssigned,
        ].includes(order.status)
      ) {
        this.inProgressOrders = order;
        return 'inProgress';
      }

      if (order.status === TradeStatusEnum.PaymentMade) {
        this.inProgressOrders = order;
        return 'payMade';
      }
      if (order.status === TradeStatusEnum.Completed) {
        this.doneOrder = order;
        return 'done';
      }
      if (order.status === TradeStatusEnum.Cancelled) {
        this.cancelledOrder = order;
        return 'cancel';
      }
      if (order.status === TradeStatusEnum.Expired) {
        this.expiredOrder = order;
        return 'expired';
      }

      return null;
    },
    setUserWaitingOrder(order: OrderPending) {
      if (
        [
          TradeStatusEnum.Expired,
          TradeStatusEnum.BankAccountAssigned,
          TradeStatusEnum.Accepted,
          TradeStatusEnum.Pending,
          TradeStatusEnum.PaymentMade,
          TradeStatusEnum.Completed,
          TradeStatusEnum.Cancelled,
          TradeStatusEnum.Expired,
        ].includes(order.status)
      ) {
        this.userWaitingOrders = order;
      }
    },
    acceptedOrderNotify() {
      this.acceptedOrder++;
    },
    setPendingCount(count: number) {
      this.pendingCount = count;
    },
    setInProgressCount(count: number) {
      this.inProgressCount = count;
    },
    setUserOrderCount(count: number) {
      this.userOrderCount = count;
    },
  },
});
