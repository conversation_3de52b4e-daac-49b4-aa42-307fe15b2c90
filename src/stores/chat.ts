import { defineStore } from 'pinia';
import { Message } from 'src/pages/trade/api';
import { ChatMessageType } from 'src/utils/enums';
import { useAuthStore } from './useAuthStore';

export type MessagePayload = {
  tradeId: number;
  payload: string;
  messageType?: ChatMessageType;
  caption?: string;
  file?: File | null;
};

export const useChatStore = defineStore('chat', {
  state: (): {
    message: Message | null;
    sendMessage: (payload: MessagePayload) => Promise<void>;
    unreadMessageCount: Record<number, number>;
  } => ({
    message: null,
    sendMessage: async (_payload: MessagePayload): Promise<void> => {
      //
    },
    unreadMessageCount: {},
  }),
  actions: {
    setMessage(data: Message) {
      this.message = data;

      const auth = useAuthStore();
      if (auth.loginRes?.userId === data.senderId) return;

      const id = data.tradeId;
      if (this.unreadMessageCount[id]) {
        this.unreadMessageCount[id]++;
      } else {
        this.unreadMessageCount[id] = 1;
      }
    },
    setSendMessage(data: (payload: MessagePayload) => Promise<void>) {
      this.sendMessage = data;
    },
    readMessage(tradeId: number) {
      delete this.unreadMessageCount[tradeId];
    },
  },
});
