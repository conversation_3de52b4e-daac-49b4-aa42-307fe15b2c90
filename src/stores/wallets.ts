import { defineStore } from 'pinia';
import { Wallet } from 'src/pages/wallets/api';
import { DEFAULT_CRYPTO } from 'src/utils/constants';

export const useWalletsStore = defineStore('wallets', {
  state: (): {
    wallets: Array<Wallet>;
    availableAmount: number;
    frozenAmount: number;
    totalAmount: number;
  } => ({
    wallets: [],
    availableAmount: 0,
    frozenAmount: 0,
    totalAmount: 0,
  }),
  actions: {
    setWallets(data: Array<Wallet>) {
      this.wallets = data;

      this.availableAmount = data.reduce(
        (acc, current) =>
          (acc +=
            current.balances.find(
              (balance) => balance.symbol === DEFAULT_CRYPTO
            )?.amount || 0),
        0
      );
      this.frozenAmount = data.reduce(
        (acc, current) =>
          (acc +=
            current.balances.find(
              (balance) => balance.symbol === DEFAULT_CRYPTO
            )?.frozenAmount || 0),
        0
      );
      this.totalAmount = data.reduce(
        (acc, current) =>
          (acc +=
            current.balances.find(
              (balance) => balance.symbol === DEFAULT_CRYPTO
            )?.totalAmount || 0),
        0
      );
    },
  },
});
