<template>
  <q-layout view="hHr LpR lff">
    <public-header />
    <q-page-container>
      <router-view />
      <span v-if="devEnv" class="version">{{ appVersion }}</span>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import PublicHeader from 'src/layouts/components/PublicHeader.vue';
import { devEnv } from 'src/router/routes';

const appVersion = import.meta.env.VITE_VERSION;
</script>

<style scoped>
.version {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 9999;
  pointer-events: none;
  display: inline-block;
  padding: 0 0.25rem;
  color: white;
  background-color: var(--accent-color);
  opacity: 0.5;
}
</style>
