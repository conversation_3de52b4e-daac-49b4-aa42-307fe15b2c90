<template>
  <q-layout view="lHh Lpr lFf" class="external-layout">
    <q-header :elevated="!themeStore.isDark" class="header bg-gradient-tr">
      <q-toolbar class="toolbar">
        <q-toolbar-title class="flex items-center">
          <q-img
            loading="eager"
            no-spinner
            no-transition
            fetchpriority="high"
            src="/images/logo.png"
            class="header-logo q-mr-sm"
          />
          <span>K28U</span>
        </q-toolbar-title>

        <div class="flex-center q-gutter-x-sm">
          <theme-toggle-bar />
          <i18n-btn />
        </div>
      </q-toolbar>
    </q-header>

    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<style scoped>
.external-layout {
  background-color: #f5f5f5;
  min-height: 100vh;
}

body.isDark .external-layout {
  background-color: #121212;
}

.header-logo {
  width: 32px;
  height: 32px;
}

.header {
  background-color: transparent;
  backdrop-filter: blur(12px);
}

.toolbar {
  height: 3.5rem;
}

.bg-gradient-tr {
  background: linear-gradient(to right, #1a237e, #4a148c);
}

body.isDark .bg-gradient-tr {
  background: linear-gradient(to right, #0d1442, #2e0b52);
}
</style>

<script setup lang="ts">
import { useThemeStore } from 'src/stores';
import ThemeToggleBar from 'src/components/ThemeToggleBar.vue';
import I18nBtn from 'src/components/I18nBtn.vue';

// Use the theme store to ensure consistent theming
const themeStore = useThemeStore();
</script>
