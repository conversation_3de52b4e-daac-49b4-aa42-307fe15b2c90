<template>
  <q-layout view="hHr LpR lff">
    <PrivateHeader
      :toggle-sidebar-menu="toggleMenuDrawer"
      :privateNfyOpen="togglePrivateNfyOpen"
    />

    <SidebarNavigation />

    <OrderDrawer v-model:orders-drawer-open="privateNfyOpen" />
    <MenuDrawer v-model:menu-drawer-open="menuDrawerOpen" />

    <q-page-container>
      <suspense>
        <template #default>
          <router-view />
        </template>
        <template #fallback>
          <q-spinner-gears color="primary" size="50px" />
        </template>
      </suspense>
      <span v-if="devEnv" class="version">{{ appVersion }}</span>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { devEnv } from 'src/router/routes';
import { useClientHub } from 'src/hooks/useClientHub';
import { useAuthStore } from 'src/stores';
import MenuDrawer from './components/MenuDrawer.vue';
import PrivateHeader from './components/PrivateHeader.vue';
import SidebarNavigation from './components/SidebarNavigation.vue';
import OrderDrawer from './components/OrderDrawer.vue';

const auth = useAuthStore();
useClientHub(auth.loginRes?.token || '');

const privateNfyOpen = ref(false);
const menuDrawerOpen = ref(false);
const appVersion = import.meta.env.VITE_VERSION;

const togglePrivateNfyOpen = () => {
  privateNfyOpen.value = !privateNfyOpen.value;
};

const toggleMenuDrawer = () => {
  menuDrawerOpen.value = !menuDrawerOpen.value;
};
</script>

<style scoped>
.version {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 9999;
  pointer-events: none;
  display: inline-block;
  padding: 0 0.25rem;
  color: white;
  background-color: var(--accent-color);
  opacity: 0.5;
}
</style>
