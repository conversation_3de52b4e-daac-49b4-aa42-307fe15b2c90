<template>
  <q-btn flat round icon="account_circle">
    <q-menu :offset="[0, 8]" anchor="bottom middle" self="top middle">
      <q-list>
        <q-item class="q-py-md">
          <q-item-section avatar>
            <q-avatar>
              <img src="src/assets/images/avatar.png" />
            </q-avatar>
          </q-item-section>

          <q-item-section>
            <q-item-label>{{ auth.loginRes?.email }}</q-item-label>
            <q-item-label>{{ auth.loginRes?.phoneNumber }}</q-item-label>
          </q-item-section>
        </q-item>

        <q-separator />

        <!-- <q-item clickable v-close-popup> -->
        <!--   <q-item-section -->
        <!--     @click=" -->
        <!--       () => { -->
        <!--         router.push({ path: '/auth/change' }); -->
        <!--       } -->
        <!--     " -->
        <!--   > -->
        <!--     {{ t('dropDownUser.changePassword') }} -->
        <!--   </q-item-section> -->
        <!-- </q-item> -->
        <!-- <q-item clickable v-close-popup> -->
        <!--   <q-item-section>{{ t('dropDownUser.setup2FA') }}</q-item-section> -->
        <!-- </q-item> -->
        <!---->
        <!-- <q-separator /> -->

        <q-item clickable v-close-popup>
          <q-item-section @click="handleLogout">
            {{ t('dropDownUser.logout') }}
          </q-item-section>
        </q-item>
      </q-list>
    </q-menu>
  </q-btn>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useAuthStore } from 'src/stores/useAuthStore';

const { t } = useI18n();
const auth = useAuthStore();
const router = useRouter();

const handleLogout = async () => {
  await auth.logout();
  router.replace({ name: 'login' });
};
</script>
