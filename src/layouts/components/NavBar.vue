<template>
  <div v-if="isOnDrawer" class="nav-root">
    <template v-if="!auth.isAgent">
      <q-btn
        v-for="(userItem, index) in userItems"
        :key="index"
        flat
        no-caps
        align="left"
        :label="t(userItem.label)"
        :to="{ name: userItem.name, query: userItem.query }"
        class="nav-item full-width text-weight-regular"
      >
        <span
          v-if="isOnDrawer && userItem.rate && userItem.query"
          :class="[
            'rate-label',
            userItem.query.type === 'buy' ? 'text-info' : 'text-warning',
          ]"
          >{{ userItem.rate }}</span
        >
      </q-btn>
    </template>

    <q-btn
      v-for="(sharedItem, index) in sharedItems"
      :key="index"
      flat
      no-caps
      align="left"
      :label="t(sharedItem.label)"
      :to="{ name: sharedItem.name }"
      class="nav-item full-width text-weight-regular"
    />
  </div>

  <div v-else class="nav-root">
    <template v-if="!auth.isAgent">
      <q-btn
        v-for="(userItem, index) in userItems"
        :key="index"
        flat
        no-caps
        align="left"
        :icon="userItem.icon"
        :label="t(userItem.label)"
        :to="{ name: userItem.name, query: userItem.query }"
        class="nav-item full-width text-weight-regular"
      />
    </template>

    <q-btn
      v-for="(sharedItem, index) in sharedItems"
      :key="index"
      flat
      no-caps
      align="left"
      :icon="sharedItem.icon"
      :label="t(sharedItem.label)"
      :to="{ name: sharedItem.name }"
      class="nav-item full-width text-weight-regular"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeMount } from 'vue';
import { useI18n } from 'vue-i18n';
import { useExchangeRates } from 'src/api';
import { DEFAULT_CURRENCY } from 'src/utils/constants';
import { useAuthStore } from 'src/stores/useAuthStore';

const { isOnDrawer = false } = defineProps<{ isOnDrawer?: boolean }>();

// setup funcs
const { t } = useI18n();
const auth = useAuthStore();
const { data: exchangeRates, run } = useExchangeRates({ manual: true });

const defaultCurrencyRate = computed(() =>
  exchangeRates.value?.find(
    (exchangeRate) => exchangeRate.fiatCode === DEFAULT_CURRENCY
  )
);

onBeforeMount(() => {
  if (isOnDrawer && !auth.isAgent) run({});
});

// default nav items
const userItems = computed(() => [
  {
    name: 'trade',
    icon: 'arrow_circle_up',
    label: 'navBar.buy',
    query: {
      type: 'buy',
      action: 'create',
    },
    rate: defaultCurrencyRate.value?.askPrice,
  },
  {
    name: 'trade',
    icon: 'arrow_circle_down',
    label: 'navBar.sell',
    query: {
      type: 'sell',
      action: 'create',
    },
    rate: defaultCurrencyRate.value?.bidPrice,
  },
]);
const sharedItems = computed(() => [
  { name: 'transfer', icon: 'move_up', label: 'navBar.transfer' },
  {
    name: 'wallets',
    icon: 'account_balance_wallet',
    label: 'navBar.wallets',
  },
  {
    name: 'history',
    icon: 'receipt_long',
    label: 'navBar.history',
  },
  {
    name: 'accounts',
    icon: 'payments',
    label: 'navBar.accounts',
  },
]);
</script>

<style scoped>
.nav-item:deep(.q-btn__content) {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 0.3rem;
  padding: 0.5rem 0;
}
.rate-label {
  padding: 0.125rem 0.5rem;
  line-height: 1;
  border: 1px solid currentColor;
  border-radius: 4px;
}
</style>
