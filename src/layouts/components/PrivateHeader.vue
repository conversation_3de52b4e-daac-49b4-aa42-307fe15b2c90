<template>
  <q-header
    :elevated="themes.isDark ? false : true"
    class="header bg-gradient-tr"
  >
    <q-toolbar class="toolbar">
      <q-toolbar-title>
        <router-link
          :to="{ name: 'home' }"
          class="q-pa-none flex items-center gap-x-md"
        >
          <q-img
            v-if="q.screen.gt.xs"
            no-spinner
            no-transition
            fetchpriority="high"
            src="/images/logo-full.png"
            class="desktop-logo"
          />
          <q-img
            v-else
            loading="eager"
            no-spinner
            no-transition
            fetchpriority="high"
            src="/images/logo.png"
            class="mobile-logo"
          />
          <div v-if="isDev" class="text-bold">DEV</div>
        </router-link>
      </q-toolbar-title>

      <div class="flex-center q-gutter-x-sm">
        <DropDownAsset />
        <q-btn flat round icon="receipt_long" @click="privateNfyOpen">
          <q-badge
            v-if="handleNotifyCount > 0"
            color="red"
            floating
            rounded
            :label="handleNotifyCount"
            class="q-mt-sm"
          />
        </q-btn>
        <template v-if="q.screen.gt.sm">
          <DropDownUser />
          <theme-toggle-bar />
          <i-18n-btn />
        </template>
        <q-btn
          v-else
          flat
          dense
          round
          icon="menu"
          aria-label="Menu"
          @click="toggleSidebarMenu"
        />
      </div>
    </q-toolbar>
  </q-header>
</template>

<script setup lang="ts">
import { useAuthStore, useOrderStore, useThemeStore } from 'src/stores';
import DropDownAsset from './DropDownAsset.vue';
import DropDownUser from './DropDownUser.vue';
import I18nBtn from 'src/components/I18nBtn.vue';
import ThemeToggleBar from 'src/components/ThemeToggleBar.vue';
import { useQuasar } from 'quasar';
import { computed } from 'vue';

const q = useQuasar();
const auth = useAuthStore();
const orderStore = useOrderStore();
// Agent count order
const pendingCount = computed(() => orderStore.getPendingCount || 0);
const inProgressCount = computed(() => orderStore.inProgressCount || 0);
const userOrderCount = computed(() => orderStore.getUserOrderCount || 0);
defineProps<{
  toggleSidebarMenu: () => void;
  privateNfyOpen: () => void;
}>();

const themes = useThemeStore();
const handleNotifyCount = computed(() => {
  if (auth.isAgent) {
    return (pendingCount.value || 0) + (inProgressCount.value || 0);
  } else {
    return userOrderCount.value || 0;
  }
});

const isDev = import.meta.env.DEV;
</script>

<style scoped>
.header {
  background-color: transparent;
  backdrop-filter: blur(12px);
}

.toolbar {
  height: 3.5rem;
}

.desktop-logo {
  width: 137px;
  height: 2.5rem;
}

.mobile-logo {
  width: 1.875rem;
  height: 2.5rem;
}
</style>
