<template>
  <q-drawer
    v-model="open"
    side="right"
    overlay
    bordered
    behavior="mobile"
    no-swipe-open
    no-swipe-close
    :width="drawerWidth"
    class="q-pa-md"
  >
    <div class="flex items-center justify-between">
      <q-btn
        flat
        round
        icon="close"
        color="grey"
        @click="open = !open"
        class="btn-close"
      />
      <div class="flex-center q-gutter-xs">
        <theme-toggle-bar is-on-drawer />
        <i-18n-btn is-on-drawer />
      </div>
    </div>

    <q-list class="list-wrapper">
      <q-item class="q-pa-md">
        <q-item-section avatar>
          <q-avatar>
            <img src="src/assets/images/avatar.png" />
          </q-avatar>
        </q-item-section>

        <q-item-section>
          <q-item-label>{{ auth.loginRes?.email }}</q-item-label>
          <q-item-label>{{ auth.loginRes?.phoneNumber }}</q-item-label>
        </q-item-section>
      </q-item>

      <q-separator />

      <!-- <q-item clickable> -->
      <!--   <q-item-section>{{ t('menuDrawer.changePassword') }}</q-item-section> -->
      <!-- </q-item> -->
      <!-- <q-item clickable> -->
      <!--   <q-item-section>{{ t('menuDrawer.setup2FA') }}</q-item-section> -->
      <!-- </q-item> -->
      <!---->
      <!-- <q-separator /> -->

      <nav-bar is-on-drawer />

      <q-separator />

      <q-item clickable v-close-popup>
        <q-item-section @click="handleLogout">
          {{ t('menuDrawer.logout') }}
        </q-item-section>
      </q-item>
      <q-separator />
    </q-list>
  </q-drawer>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import I18nBtn from 'src/components/I18nBtn.vue';
import ThemeToggleBar from 'src/components/ThemeToggleBar.vue';
import { computed, onBeforeUnmount, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import NavBar from './NavBar.vue';
import { useAuthStore } from 'src/stores/useAuthStore';
import { useRouter } from 'vue-router';

const { menuDrawerOpen } = defineProps<{
  menuDrawerOpen: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:menuDrawerOpen', value: boolean): void;
}>();

const { t } = useI18n();
const q = useQuasar();
const router = useRouter();
const auth = useAuthStore();

const handleLogout = async () => {
  await auth.logout();
  router.replace({ name: 'login' });
};

const hasScrollbar = ref(false);

const open = computed({
  get: () => menuDrawerOpen,
  set: (val: boolean) => emit('update:menuDrawerOpen', val),
});
const drawerWidth = computed(() => {
  return q.screen.gt.xs
    ? 400
    : q.screen.width - (hasScrollbar.value ? getScrollbarWidth() : 0);
});

function getScrollbarWidth() {
  return window.innerWidth - document.documentElement.clientWidth;
}
function checkScrollbar() {
  hasScrollbar.value = getScrollbarWidth() > 0;
}

onMounted(() => {
  checkScrollbar();

  const observer = new MutationObserver(() => {
    requestAnimationFrame(() => {
      checkScrollbar();
    });
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    characterData: true,
  });

  onBeforeUnmount(() => {
    observer.disconnect();
  });
});
</script>

<style scoped>
.btn-close {
  margin-left: -0.75rem;
}

.list-wrapper {
  margin-left: -1rem;
  margin-right: -1rem;
}

.exchange-rate {
  border: 1px solid currentColor;
  border-radius: 4px;
}
</style>
