<template>
  <q-btn flat>
    <q-skeleton
      v-if="loadingWallets"
      type="text"
      style="width: 8rem; height: 2rem"
    />
    <template v-else>
      <span class="text-subtitle2 text-bold q-px-sm q-mt-xs">{{
        DEFAULT_CRYPTO
      }}</span>
      <span class="text-h6 text-bold">
        {{ thousandTool(walletsStore.totalAmount, DEFAULT_CRYPTO) }}</span
      >
    </template>

    <q-menu
      :offset="[0, 8]"
      anchor="bottom middle"
      self="top middle"
      class="no-shadow column flex-center"
      style="
        background-color: rgba(82, 9, 185, 0.4);
        backdrop-filter: blur(12px);
        width: 20rem;
        height: 22rem;
        border-radius: 20px;
      "
    >
      <AssetOverview />
    </q-menu>
  </q-btn>
</template>

<script setup lang="ts">
import { watch } from 'vue';
import { useWalletsStore } from 'src/stores';
import { DEFAULT_CRYPTO } from 'src/utils/constants';
import { thousandTool } from 'src/utils/NumberTool';
import { useWallets } from 'src/pages/wallets/api';
import AssetOverview from 'src/components/AssetOverview.vue';

const walletsStore = useWalletsStore();
const { data: wallets, loading: loadingWallets } = useWallets();

watch(wallets, (newWallets) => {
  if (newWallets && newWallets.length > 0) {
    walletsStore.setWallets(newWallets);
  }
});
</script>
