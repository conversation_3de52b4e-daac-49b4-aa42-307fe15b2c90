<template>
  <q-drawer
    v-if="auth.isAgent"
    v-model="model"
    side="right"
    overlay
    bordered
    behavior="mobile"
    no-swipe-open
    no-swipe-close
    :width="drawerWidth"
    class="q-pa-md"
  >
    <q-btn
      flat
      round
      icon="close"
      color="grey"
      class="btn-close"
      @click="handleClose"
    />
    <AgentOrdersSection :isOnDrawer="true" />
  </q-drawer>
  <q-drawer
    v-else
    v-model="model"
    side="right"
    overlay
    bordered
    behavior="mobile"
    no-swipe-open
    no-swipe-close
    :width="drawerWidth"
    class="q-pa-md"
  >
    <q-btn
      flat
      round
      icon="close"
      color="grey"
      class="btn-close"
      @click="handleClose"
    />
    <UserOrderSection :isOnDrawer="true" />
  </q-drawer>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import AgentOrdersSection from 'src/pages/home/<USER>/AgentOrdersSection.vue';
import UserOrderSection from 'src/pages/home/<USER>/UserOrderSection.vue';
import { useAuthStore } from 'src/stores';
import { computed, onBeforeUnmount, onMounted, ref } from 'vue';

const auth = useAuthStore();
// Props / Emits
const { ordersDrawerOpen } = defineProps<{
  ordersDrawerOpen: boolean;
}>();
const emit = defineEmits<{
  (e: 'update:ordersDrawerOpen', value: boolean): void;
}>();

// Refs & reactive values
const q = useQuasar();
const hasScrollbar = ref(false);

// Computed
const model = computed({
  get: () => ordersDrawerOpen,
  set: (val: boolean) => emit('update:ordersDrawerOpen', val),
});

const drawerWidth = computed(() => {
  return q.screen.gt.xs
    ? 550
    : q.screen.width - (hasScrollbar.value ? getScrollbarWidth() : 0);
});

function getScrollbarWidth() {
  return window.innerWidth - document.documentElement.clientWidth;
}

function checkScrollbar() {
  hasScrollbar.value = getScrollbarWidth() > 0;
}

const handleClose = () => {
  emit('update:ordersDrawerOpen', false);
};

// Lifecycle hooks
onMounted(() => {
  checkScrollbar();

  const observer = new MutationObserver(() => {
    requestAnimationFrame(checkScrollbar);
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    characterData: true,
  });

  onBeforeUnmount(() => observer.disconnect());
});
</script>

<style scoped>
.btn-close {
  margin-left: -0.75rem;
}
</style>
