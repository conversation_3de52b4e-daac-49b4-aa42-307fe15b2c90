<template>
  <q-header
    :elevated="themes.isDark ? false : true"
    class="header bg-gradient-tr"
  >
    <q-toolbar class="toolbar">
      <q-toolbar-title>
        <router-link to="/auth/login" class="flex items-center gap-x-md">
          <q-img
            v-if="q.screen.gt.xs"
            no-spinner
            no-transition
            fetchpriority="high"
            src="/images/logo-full.png"
            class="desktop-logo"
          />
          <q-img
            v-else
            loading="eager"
            no-spinner
            no-transition
            fetchpriority="high"
            src="/images/logo.png"
            class="mobile-logo"
          />
          <div v-if="isDev" class="text-bold">DEV</div>
        </router-link>
      </q-toolbar-title>

      <div class="flex-center q-gutter-x-sm">
        <theme-toggle-bar />
        <i-18n-btn />
      </div>
    </q-toolbar>
  </q-header>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import I18nBtn from 'src/components/I18nBtn.vue';
import ThemeToggleBar from 'src/components/ThemeToggleBar.vue';

import { useThemeStore } from 'src/stores';

const themes = useThemeStore();
const q = useQuasar();

const isDev = import.meta.env.DEV;
</script>

<style scoped>
.header {
  background-color: transparent;
  backdrop-filter: blur(12px);
}

.toolbar {
  height: 3.5rem;
}

.desktop-logo {
  width: 137px;
  height: 2.5rem;
}

.mobile-logo {
  width: 1.875rem;
  height: 2.5rem;
}
</style>
