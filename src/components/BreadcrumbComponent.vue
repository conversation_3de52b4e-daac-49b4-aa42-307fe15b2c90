<template>
  <div class="label flex gap-x-xs items-center text-bold">
    <q-btn
      flat
      icon="chevron_left"
      @click="handleGoBack"
      class="label"
      padding="0"
    >
      <q-tooltip>{{ t('breadcrumbComponent.back') }}</q-tooltip>
    </q-btn>
    <q-btn flat :to="{ name: 'home' }" padding="0 8px" class="label" no-caps>{{
      t('breadcrumbComponent.home')
    }}</q-btn>
    <span>/</span>
    <span class="text-accent q-px-sm">{{ label }}</span>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';

const { label } = defineProps<{ label: string }>();

const router = useRouter();
const { t } = useI18n();

const handleGoBack = async () => {
  if (window.history.length > 1) {
    router.back();
  } else {
    await router.push('/');
  }
};
</script>

<style scoped>
.label {
  font-size: 1.2rem;
}
</style>
