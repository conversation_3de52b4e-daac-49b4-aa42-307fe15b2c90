<template>
  <q-btn
    flat
    round
    icon="language"
    :color="
      isOnDrawer && !themeStore.isDark && q.screen.lt.md ? 'accent' : 'white'
    "
  >
    <q-menu :offset="[0, 7]" anchor="bottom right" self="top right">
      <q-list>
        <q-item
          clickable
          v-close-popup
          v-for="(lang, index) in langs"
          :key="index"
          @click="() => handleSwitchLocale(lang.locale)"
          :class="{
            'text-white bg-accent': i18n.locale.value === lang.locale,
          }"
        >
          <q-item-section>
            {{ lang.name }}
          </q-item-section>
        </q-item>
      </q-list>
    </q-menu>
  </q-btn>
</template>

<script setup lang="ts">
import { langs } from 'src/i18n';
import { useI18n } from 'vue-i18n';
import { storageHelper } from 'src/utils/foragePkg';
import { useThemeStore } from '../stores';
import { useQuasar } from 'quasar';

const { isOnDrawer = false } = defineProps<{ isOnDrawer?: boolean }>(); // this flag is used to change the button color when it is inside the MenuDrawer on mobile devices, to match the design

const i18n = useI18n({ useScope: 'global' });
const themeStore = useThemeStore();
const q = useQuasar();

const handleSwitchLocale = (locale: string) => {
  storageHelper('locale').setItem(locale);
  i18n.locale.value = locale;
};
</script>
