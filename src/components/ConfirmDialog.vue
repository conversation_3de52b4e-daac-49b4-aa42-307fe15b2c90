<template>
  <q-dialog v-model="model" persistent>
    <q-card class="q-pa-md" style="min-width: 300px">
      <q-card-section class="column gap-y-sm">
        <div class="text-h6 flex items-center gap-x-sm">
          <q-icon
            :name="type === 'info' ? 'error' : 'warning'"
            :color="type === 'info' ? 'info' : 'orange'"
            size="2rem"
          />
          <span>{{ title }}</span>
        </div>
        <div v-if="message" class="q-mt-sm text-body2">{{ message }}</div>
      </q-card-section>

      <template v-if="slots.default">
        <q-separator class="q-mx-sm" />

        <q-card-section>
          <slot />
        </q-card-section>

        <q-separator class="q-mb-sm q-mx-sm" />
      </template>

      <q-card-actions align="right">
        <q-btn
          flat
          :label="t('confirmDialog.cancel')"
          color="accent"
          @click="cancel"
          :loading="loading"
        />
        <q-btn
          unelevated
          :label="t('confirmDialog.confirm')"
          color="accent"
          @click="confirm"
          :loading="loading"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, useSlots } from 'vue';
import { useI18n } from 'vue-i18n';

const {
  modelValue,
  type = 'info',
  title = '',
  message = '',
  loading = false,
  onConfirm = () => {
    //
  },
} = defineProps<{
  modelValue: boolean;
  type?: 'info' | 'warning';
  title: string;
  message?: string;
  loading?: boolean;
  onConfirm?: () => void;
}>();
const emit = defineEmits(['update:modelValue']);
const model = computed({
  get: () => modelValue,
  set: (val: boolean) => emit('update:modelValue', val),
});

const { t } = useI18n();
const slots = useSlots();

const cancel = () => {
  emit('update:modelValue', false);
};

const confirm = () => {
  model.value = false;
  onConfirm();
};
</script>
