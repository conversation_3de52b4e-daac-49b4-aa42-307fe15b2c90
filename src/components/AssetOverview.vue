<template>
  <div class="column flex-center">
    <q-knob
      :max="walletsStore.totalAmount"
      v-model="walletsStore.availableAmount"
      readonly
      :angle="180"
      size="12rem"
      color="positive"
      track-color="warning"
      :thickness="0.15"
      show-value
    >
      <div class="knob__value column flex-center text-white">
        <span class="text-subtitle2 text-weight-regular">{{
          t('assetOverview.balance')
        }}</span>
        <span class="text-h5 text-bold">
          {{ thousandTool(walletsStore.totalAmount, DEFAULT_CRYPTO) }}
        </span>
        <span class="text-subtitle2 text-bold">{{ DEFAULT_CRYPTO }}</span>
      </div>
    </q-knob>

    <q-card class="balance column q-mt-md q-px-md q-py-sm">
      <span class="tooltip text-positive flex gap-x-sm justify-between">
        <q-tooltip
          anchor="top middle"
          self="bottom middle"
          class="column bg-dark text-white"
        >
          <span class="tooltip text-positive">{{
            t('assetOverview.availableTooltip')
          }}</span>
        </q-tooltip>

        <span>{{ t('assetOverview.available') }}:</span>
        <span class="text-bold"
          >{{ thousandTool(walletsStore.availableAmount, DEFAULT_CRYPTO) }}
          {{ DEFAULT_CRYPTO }}</span
        >
      </span>
      <span class="tooltip text-warning flex gap-x-sm justify-between">
        <q-tooltip
          anchor="bottom middle"
          self="top middle"
          class="column bg-dark text-white"
        >
          <span class="tooltip text-warning">{{
            t('assetOverview.frozenTooltip')
          }}</span>
        </q-tooltip>

        <span>{{ t('assetOverview.frozen') }}:</span>
        <span class="text-bold"
          >{{ thousandTool(walletsStore.frozenAmount, DEFAULT_CRYPTO) }}
          {{ DEFAULT_CRYPTO }}</span
        ></span
      >
    </q-card>

    <div class="btn-wrapper q-mt-lg">
      <q-btn
        v-close-popup
        flat
        rounded
        icon-right="chevron_right"
        :to="{ name: 'wallets' }"
        class="btn text-bold text-accent"
        :class="themeStore.isDark ? 'bg-dark' : 'bg-white'"
        >{{ t('assetOverview.topUp') }}</q-btn
      >
      <q-btn
        v-close-popup
        flat
        rounded
        icon-right="chevron_right"
        :to="{ name: 'transfer' }"
        class="btn text-bold text-accent"
        :class="themeStore.isDark ? 'bg-dark' : 'bg-white'"
        >{{ t('assetOverview.transfer') }}</q-btn
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { thousandTool } from 'src/utils/NumberTool';
import { useThemeStore, useWalletsStore } from 'src/stores';
import { DEFAULT_CRYPTO } from 'src/utils/constants';

const { t } = useI18n();
const walletsStore = useWalletsStore();
const themeStore = useThemeStore();
</script>

<style scoped>
.knob__value {
  text-shadow: 0px 0px 8px rgba(0, 0, 0, 0.4);
}

.tooltip {
  font-size: 1rem;
}

.balance {
  background: rgba(37, 4, 83, 0.6);
  border-radius: 12px;
}

.btn-wrapper {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  column-gap: 1rem;
}
.btn {
  box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.1);
}
</style>
