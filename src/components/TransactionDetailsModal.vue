<template>
  <q-dialog v-model="model" class="wrapper">
    <q-card class="wrapper q-pa-md column gap-y-md no-wrap">
      <span class="title text-bold text-center">{{
        t('transactionDetailsModal.title')
      }}</span>

      <div class="column items-center">
        <trade-type-tag :type="transaction.type" />
        <span class="amount"
          >{{
            transaction.status === TradeStatusEnum.Completed
              ? useSwapTradeType(transaction.type) === TradeTypeEnum.Buy
                ? '+'
                : '-'
              : ''
          }}{{
            thousandTool(
              transaction.cryptoAmount,
              transaction.cryptoSymbol as DigitTypes
            )
          }}
          {{ transaction.cryptoSymbol }}</span
        >
        <span class="text-grey"
          >=
          {{
            thousandTool(
              transaction.fiatAmount,
              transaction.fiatCurrency as DigitTypes
            )
          }}
          {{ transaction.fiatCurrency }}</span
        >
      </div>

      <trade-status-label :status="transaction.status" />

      <div class="column gap-y-sm">
        <div class="flex items-center justify-between">
          <span class="text-grey">{{
            t('transactionDetailsModal.orderNumber')
          }}</span>
          <span>{{ transaction.tradeNo }}</span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-grey">{{
            t('transactionDetailsModal.exchangeRate')
          }}</span>
          <span
            >1 {{ transaction.cryptoSymbol }} = {{ transaction.rate }}
            {{ transaction.fiatCurrency }}</span
          >
        </div>
        <div v-if="auth.isAgent" class="flex items-center justify-between">
          <span class="text-grey">{{
            t('transactionDetailsModal.agentFee')
          }}</span>
          <span
            >{{ transaction.agentFeeAmount }}
            {{ transaction.cryptoSymbol }}</span
          >
        </div>
        <div class="flex items-center justify-between">
          <span class="text-grey">{{
            t('transactionDetailsModal.createdAt')
          }}</span>
          <span>{{
            dayjs(transaction.createdAt).format(dateFormator.accurate)
          }}</span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-grey">{{
            t('transactionDetailsModal.paidAt')
          }}</span>
          <span>{{
            dayjs(transaction.userPaidAt).format(dateFormator.accurate)
          }}</span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-grey">{{
            t('transactionDetailsModal.completedAt')
          }}</span>
          <span>{{
            transaction.completedAt
              ? dayjs(transaction.completedAt).format(dateFormator.accurate)
              : '--'
          }}</span>
        </div>
        <div class="column gap-y-sm">
          <span class="text-grey">{{
            t('transactionDetailsModal.buyerInfo')
          }}</span>
          <div
            class="flex items-center justify-between q-pa-md rounded-borders"
            :class="themeStore.isDark ? 'bg-grey-9' : 'bg-grey-2'"
          >
            <span class="text-grey">{{
              t('transactionDetailsModal.bankAccountName')
            }}</span>
            <span>{{ transaction.payerBankAccountName || '--' }}</span>
          </div>
        </div>
        <div class="column gap-y-sm">
          <span class="text-grey">{{
            t('transactionDetailsModal.sellerInfo')
          }}</span>
          <div
            class="q-pa-md rounded-borders"
            :class="themeStore.isDark ? 'bg-grey-9' : 'bg-grey-2'"
          >
            <div class="flex items-center justify-between">
              <span class="text-grey">{{
                t('transactionDetailsModal.bankAccountName')
              }}</span>
              <span>{{ transaction.accountName || '--' }}</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-grey">{{
                t('transactionDetailsModal.bankAccountNumber')
              }}</span>
              <span>{{ transaction.accountNumber || '--' }}</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-grey">{{
                t('transactionDetailsModal.bankName')
              }}</span>
              <span>{{ transaction.bankName || '--' }}</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-grey">{{
                t('transactionDetailsModal.branchCode')
              }}</span>
              <span>{{ transaction.branchCode || '--' }}</span>
            </div>
          </div>
        </div>
      </div>

      <q-separator />

      <div class="column gap-y-sm full-width">
        <div
          v-if="
            [TradeStatusEnum.Completed, TradeStatusEnum.Cancelled].includes(
              transaction.status
            )
          "
          class="btn-wrapper row gap-x-sm"
        >
          <div class="col">
            <chat-record-modal
              :tradeId="transaction.id"
              :orderNumber="transaction.tradeNo"
            />
          </div>
          <div class="col">
            <payment-proof :tradeId="transaction.id" />
          </div>
        </div>
        <q-btn
          v-close-popup
          :outline="
            [
              TradeStatusEnum.Accepted,
              TradeStatusEnum.BankAccountAssigned,
              TradeStatusEnum.PaymentMade,
              TradeStatusEnum.PaymentConfirmed,
            ].includes(transaction.status)
          "
          color="accent"
          :label="t('transactionDetailsModal.btnClose')"
        />
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import dayjs from 'dayjs';
import useSwapTradeType from 'src/hooks/useSwapTradeType';
import { useAuthStore, useThemeStore } from 'src/stores';
import { dateFormator } from 'src/utils/dateTool';
import { TradeStatusEnum, TradeTypeEnum } from 'src/utils/enums';
import { DigitTypes, thousandTool } from 'src/utils/NumberTool';
import TradeStatusLabel from 'src/components/TradeStatusLabel.vue';
import TradeTypeTag from 'src/components/TradeTypeTag.vue';
import { Trade } from 'src/pages/trade/api';
import PaymentProof from 'src/pages/trade/TradingComponents/PaymentProof.vue';
import ChatRecordModal from './ChatRecordModal.vue';

const { t } = useI18n();
const themeStore = useThemeStore();
const props = defineProps<{
  modelValue: boolean;
  transaction: Trade;
}>();
const emit = defineEmits(['update:modelValue']);
const model = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
});

const auth = useAuthStore();
</script>

<style scoped>
.wrapper {
  width: 25rem;
  max-width: 100%;
  font-size: 1rem;
}
.title {
  font-size: 1.5rem;
}
.amount {
  font-size: 2rem;
  font-weight: 700;
}
.btn-wrapper {
  height: 2.25rem;
}
</style>
