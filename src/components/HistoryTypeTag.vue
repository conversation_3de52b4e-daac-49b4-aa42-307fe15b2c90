<template>
  <span class="q-px-md text-white rounded-borders" :class="typeClass">
    {{ t(label ? `options.${label}` : '') }}
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { FlowTypeEnum } from 'src/utils/enums';
import { flowTypeEnumOptions } from 'src/utils/options';

const { type } = defineProps<{
  type: FlowTypeEnum;
}>();

const { t } = useI18n();

const label = computed(
  () =>
    flowTypeEnumOptions.find((option) => option.value === type)?.label ||
    'undefined'
);

const typeClass = computed(() =>
  type === FlowTypeEnum.Buy
    ? 'bg-info'
    : type === FlowTypeEnum.Sell
    ? 'bg-negative'
    : 'bg-accent'
);
</script>
