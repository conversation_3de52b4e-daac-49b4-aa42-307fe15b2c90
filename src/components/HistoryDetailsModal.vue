<template>
  <q-dialog v-model="model" class="wrapper">
    <loading-component v-if="loadingHistoryDetails" />
    <q-card v-else class="wrapper q-pa-md column gap-y-md no-wrap">
      <span class="title text-bold text-center">{{
        t('historyDetailsModal.title')
      }}</span>

      <!-- Header section -->
      <div class="column items-center">
        <history-type-tag :type="history.flowType" />
        <span class="amount"
          >{{ isBuy || isDeposit ? '+' : ''
          }}{{ thousandTool(history.changeAmount, history.cryptoSymbol) }}
          {{ history.cryptoSymbol }}</span
        >
        <span v-if="isTrade" class="text-grey"
          >=
          {{ thousandTool(history.fiatAmount, history.fiatSymbol) }}
          {{ history.fiatSymbol }}</span
        >
        <span v-if="!isTrade" class="text-grey"
          >{{ history.cryptoSymbol }}-{{ networkName }}</span
        >
      </div>

      <div class="column gap-y-sm">
        <!-- Trade Details -->
        <template v-if="isTrade">
          <div class="flex items-center justify-between">
            <span class="text-grey">{{
              t('historyDetailsModal.orderNumber')
            }}</span>
            <span>{{ historyDetails.trade.tradeNo }}</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-grey">{{
              t('historyDetailsModal.exchangeRate')
            }}</span>
            <span
              >1 {{ historyDetails.trade.cryptoSymbol }} =
              {{ historyDetails.trade.rate }}
              {{ historyDetails.trade.fiatCurrency }}</span
            >
          </div>
          <div v-if="auth.isAgent" class="flex items-center justify-between">
            <span class="text-grey">{{
              t('historyDetailsModal.agentFee')
            }}</span>
            <span
              >{{
                thousandTool(
                  historyDetails.trade.agentFeeAmount,
                  historyDetails.trade.cryptoSymbol
                )
              }}
              {{ historyDetails.trade.cryptoSymbol }}</span
            >
          </div>
          <div class="flex items-center justify-between">
            <span class="text-grey">{{
              t('historyDetailsModal.balance')
            }}</span>
            <div class="flex items-center gap-x-sm">
              <span>
                {{
                  showBalance
                    ? `${thousandTool(
                        history.balanceAfter,
                        history.cryptoSymbol
                      )} ${history.cryptoSymbol}`
                    : '********'
                }}
              </span>
              <q-icon
                :name="showBalance ? 'visibility' : 'visibility_off'"
                color="grey"
                @click="showBalance = !showBalance"
              />
            </div>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-grey">{{
              t('historyDetailsModal.createdAt')
            }}</span>
            <span>{{
              dayjs(historyDetails.trade.createdAt).format(
                dateFormator.accurate
              )
            }}</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-grey">{{ t('historyDetailsModal.paidAt') }}</span>
            <span>{{
              dayjs(historyDetails.trade.userPaidAt).format(
                dateFormator.accurate
              )
            }}</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-grey">{{
              t('historyDetailsModal.completedAt')
            }}</span>
            <span>{{
              dayjs(history.timestamp).format(dateFormator.accurate)
            }}</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-grey">{{ t('historyDetailsModal.note') }}</span>
            <span>{{ history.note || '--' }}</span>
          </div>
          <div class="column gap-y-sm">
            <span class="text-grey">{{
              t('historyDetailsModal.buyerInfo')
            }}</span>
            <div
              class="flex items-center justify-between q-pa-md rounded-borders"
              :class="themeStore.isDark ? 'bg-grey-9' : 'bg-grey-2'"
            >
              <span class="text-grey">{{
                t('historyDetailsModal.bankAccountName')
              }}</span>
              <span>{{
                historyDetails.trade.payerBankAccountName || '--'
              }}</span>
            </div>
          </div>
          <div class="column gap-y-sm">
            <span class="text-grey">{{
              t('historyDetailsModal.sellerInfo')
            }}</span>
            <div
              class="q-pa-md rounded-borders"
              :class="themeStore.isDark ? 'bg-grey-9' : 'bg-grey-2'"
            >
              <div class="flex items-center justify-between">
                <span class="text-grey">{{
                  t('historyDetailsModal.bankAccountName')
                }}</span>
                <span>{{ historyDetails.trade.accountName || '--' }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-grey">{{
                  t('historyDetailsModal.bankAccountNumber')
                }}</span>
                <span>{{ historyDetails.trade.accountNumber || '--' }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-grey">{{
                  t('historyDetailsModal.bankName')
                }}</span>
                <span>{{ historyDetails.trade.bankName || '--' }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-grey">{{
                  t('historyDetailsModal.branchCode')
                }}</span>
                <span>{{ historyDetails.trade.branchCode || '--' }}</span>
              </div>
            </div>
          </div>
        </template>

        <!-- Deposit/Withdrawal Details -->
        <template v-else-if="isChainTxLog">
          <div class="flex items-center justify-between">
            <span class="text-grey">{{
              t('historyDetailsModal.orderNumber')
            }}</span>
            <span>{{ historyDetails.chainTxLog.orderNumber }}</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-grey">{{ t('historyDetailsModal.fee') }}</span>
            <span
              >{{
                thousandTool(
                  historyDetails.chainTxLog.fee,
                  historyDetails.chainTxLog.symbol
                )
              }}
              {{ historyDetails.chainTxLog.symbol }}</span
            >
          </div>
          <div class="flex items-center justify-between">
            <span class="text-grey">{{
              t('historyDetailsModal.balance')
            }}</span>
            <div class="flex items-center gap-x-sm">
              <span>
                {{
                  showBalance
                    ? `${thousandTool(
                        history.balanceAfter,
                        history.cryptoSymbol
                      )} ${history.cryptoSymbol}`
                    : '********'
                }}
              </span>
              <q-icon
                :name="showBalance ? 'visibility' : 'visibility_off'"
                color="grey"
                @click="showBalance = !showBalance"
              />
            </div>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-grey">{{
              t('historyDetailsModal.fromAddress')
            }}</span>
            <div>
              <span>{{
                ellipsisMiddle(historyDetails.chainTxLog.fromAddress)
              }}</span>
              <copy-button :value="historyDetails.chainTxLog.fromAddress" />
            </div>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-grey">{{
              t('historyDetailsModal.toAddress')
            }}</span>
            <div>
              <span>{{
                ellipsisMiddle(historyDetails.chainTxLog.toAddress)
              }}</span>
              <copy-button :value="historyDetails.chainTxLog.toAddress" />
            </div>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-grey">{{
              t('historyDetailsModal.transactionHash')
            }}</span>
            <div>
              <span>{{
                ellipsisMiddle(historyDetails.chainTxLog.txHash)
              }}</span>
              <copy-button :value="historyDetails.chainTxLog.txHash" />
            </div>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-grey">{{
              t('historyDetailsModal.completedAt')
            }}</span>
            <span>{{
              dayjs(historyDetails.chainTxLog.completedAt).format(
                dateFormator.accurate
              )
            }}</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-grey">{{ t('historyDetailsModal.note') }}</span>
            <span>{{ history.note || '--' }}</span>
          </div>
        </template>

        <!-- Internal Transfer Details -->
        <template v-else>
          <div class="flex items-center justify-between">
            <span class="text-grey">{{
              t('historyDetailsModal.balance')
            }}</span>
            <div class="flex items-center gap-x-sm">
              <span>
                {{
                  showBalance
                    ? `${thousandTool(
                        history.balanceAfter,
                        history.cryptoSymbol
                      )} ${history.cryptoSymbol}`
                    : '********'
                }}
              </span>
              <q-icon
                :name="showBalance ? 'visibility' : 'visibility_off'"
                color="grey"
                @click="showBalance = !showBalance"
              />
            </div>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-grey">{{
              isDeposit
                ? t('historyDetailsModal.from')
                : t('historyDetailsModal.to')
            }}</span>
            <span>{{ history.counterpartyName }}</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-grey">{{
              t('historyDetailsModal.completedAt')
            }}</span>
            <span>{{
              dayjs(history.timestamp).format(dateFormator.accurate)
            }}</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-grey">{{ t('historyDetailsModal.note') }}</span>
            <span>{{ history.note || '--' }}</span>
          </div>
        </template>
      </div>

      <q-separator />

      <div class="column gap-y-sm full-width">
        <!-- Chat Record & Payment Proof -->
        <div
          v-if="
            isTrade &&
            [TradeStatusEnum.Completed, TradeStatusEnum.Cancelled].includes(
              historyDetails.trade.status
            )
          "
          class="btn-wrapper row gap-x-sm"
        >
          <div class="col">
            <chat-record-modal
              :tradeId="historyDetails.trade.id"
              :orderNumber="historyDetails.trade.tradeNo"
            />
          </div>
          <div class="col">
            <payment-proof :tradeId="historyDetails.trade.id" />
          </div>
        </div>

        <!-- Close button -->
        <q-btn
          v-close-popup
          color="accent"
          :label="t('historyDetailsModal.btnClose')"
        />
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useAuthStore, useThemeStore } from 'src/stores';
import { dateFormator } from 'src/utils/dateTool';
import {
  FlowTypeEnum,
  LedgerSourceTypeEnum,
  TradeStatusEnum,
} from 'src/utils/enums';
import { thousandTool } from 'src/utils/NumberTool';
import { walletNetworkOptions } from 'src/utils/options';
import { History, useHistoryDetails } from 'src/pages/history/api';
import PaymentProof from 'src/pages/trade/TradingComponents/PaymentProof.vue';
import ChatRecordModal from './ChatRecordModal.vue';
import LoadingComponent from './LoadingComponent.vue';
import HistoryTypeTag from './HistoryTypeTag.vue';
import CopyButton from 'src/components/CopyButton.vue';

const { t } = useI18n();
const themeStore = useThemeStore();
const { modelValue, history } = defineProps<{
  modelValue: boolean;
  history: History;
}>();
const emit = defineEmits(['update:modelValue']);
const model = computed({
  get: () => modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
});

const auth = useAuthStore();
const {
  data: historyDetails,
  loading: loadingHistoryDetails,
  run: getHistoryDetails,
} = useHistoryDetails();

const showBalance = ref<boolean>(false);

const ellipsisMiddle = (text?: string, start = 6, end = 6) => {
  if (!text) return '--';
  if (text.length <= start + end) return text;
  return `${text.slice(0, start)}...${text.slice(-end)}`;
};

const isBuy = computed(() => history.flowType === FlowTypeEnum.Buy);
const isDeposit = computed(() => history.flowType === FlowTypeEnum.Deposit);
const isTrade = computed(
  () => history.ledgerSourceType === LedgerSourceTypeEnum.Trade
);
const isChainTxLog = computed(
  () => history.ledgerSourceType === LedgerSourceTypeEnum.ChainTxLog
);
const networkName = computed(() =>
  t(
    `options.${
      walletNetworkOptions.find((option) => option.value === history.network)
        ?.label || ''
    }`
  )
);

watch(
  () => history,
  (newHistory) => {
    if (
      newHistory.ledgerSourceType !== LedgerSourceTypeEnum.BalanceTransfer &&
      newHistory.ledgerSourceType !== LedgerSourceTypeEnum.Unknown
    ) {
      getHistoryDetails({ id: history.id });
    }
  },
  {
    immediate: true,
  }
);
</script>

<style scoped>
.wrapper {
  width: 25rem;
  max-width: 100%;
  font-size: 1rem;
}
.title {
  font-size: 1.5rem;
}
.amount {
  font-size: 2rem;
  font-weight: 700;
}
.btn-wrapper {
  height: 2.25rem;
}
</style>
