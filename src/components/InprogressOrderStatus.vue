<template>
  <div class="flex items-center justify-center gap-x-xs" :class="statusColor">
    <span class="text-center">{{ t(`options.${statusLabel}`) }}</span>
    <q-icon :name="statusIcon" size="1.25rem" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { TradeInprogressStatusEnum } from 'src/utils/enums';
import { inProgressOrderStatusOptions } from 'src/utils/options';

const { status } = defineProps<{
  status: TradeInprogressStatusEnum;
}>();

const { t } = useI18n();

const statusLabel = computed(
  () =>
    inProgressOrderStatusOptions.find((option) => option.value === status)
      ?.label || ''
);

const statusColor = computed(() => {
  switch (status) {
    case TradeInprogressStatusEnum.SetBank:
      return 'text-blue-6';
    case TradeInprogressStatusEnum.GoToTrade:
      return 'text-orange-6';
    case TradeInprogressStatusEnum.NeedFinishOrder:
      return 'text-deep-orange-6';
    default:
      return 'text-grey';
  }
});

const statusIcon = computed(() => {
  switch (status) {
    case TradeInprogressStatusEnum.SetBank:
      return 'account_balance';
    case TradeInprogressStatusEnum.GoToTrade:
      return 'arrow_forward';
    case TradeInprogressStatusEnum.NeedFinishOrder:
      return 'warning';
    default:
      return 'info';
  }
});
</script>
