<template>
  <span class="q-px-md text-white rounded-borders" :class="typeClass">
    {{ t(label ? `options.${label}` : '') }}
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import useSwapTradeType from 'src/hooks/useSwapTradeType';
import { tradeTypeOptions } from 'src/utils/options';
import { TradeTypeEnum } from 'src/utils/enums';

const { type } = defineProps<{
  type: TradeTypeEnum;
}>();

const { t } = useI18n();
const tradeType = useSwapTradeType(type);

const label = computed(
  () =>
    tradeTypeOptions.find((option) => option.value === tradeType)?.label ||
    'undefined'
);

const typeClass = computed(() =>
  tradeType === TradeTypeEnum.Buy ? 'bg-info' : 'bg-negative'
);
</script>
