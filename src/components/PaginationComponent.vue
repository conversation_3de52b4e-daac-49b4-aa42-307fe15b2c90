<template>
  <div class="row items-center justify-between">
    <!-- Offset -->
    <div class="col q-ma-none text-bold">
      <span class="text-grey q-pr-xs"
        >{{ t('paginationComponent.totalItems') }}:</span
      >
      <span>{{ totalCount }}</span>
    </div>

    <!-- Page Number -->
    <div class="col q-ma-none">
      <q-pagination
        v-model="internalPage"
        :max="totalPages"
        direction-links
        :disable="loading"
        boundary-links
        input
        color="accent"
        class="q-ma-none flex justify-center"
        @update:model-value="emitPageChange"
      />
    </div>

    <div class="col q-ma-none">
      <!-- Page Size -->
      <q-select
        v-model="internalPageSize"
        :options="[5, 10, 20, 50, 100]"
        dense
        outlined
        :disable="loading"
        class="q-ma-none q-ml-auto"
        style="width: fit-content"
        @update:model-value="emitPageSizeChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, toRefs } from 'vue';
import { useI18n } from 'vue-i18n';

const props = withDefaults(
  defineProps<{
    modelValue: number;
    pageSize: number;
    totalPages: number;
    totalCount?: number;
    loading?: boolean;
  }>(),
  { totalCount: 0 }
);

const emit = defineEmits<{
  (e: 'update:modelValue', value: number): void;
  (e: 'update:pageSize', value: number): void;
}>();

const { t } = useI18n();

const { modelValue, pageSize } = toRefs(props);
const internalPage = ref(modelValue.value);
const internalPageSize = ref(pageSize.value);

watch(modelValue, (val) => (internalPage.value = val));
watch(pageSize, (val) => (internalPageSize.value = val));

function emitPageChange(val: number) {
  emit('update:modelValue', val);
}
function emitPageSizeChange(val: number) {
  emit('update:pageSize', val);
}
</script>
