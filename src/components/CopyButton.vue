<template>
  <q-btn
    flat
    color="grey"
    dense
    :icon="getIcon"
    :disable="isCopy"
    size="sm"
    @click="handleClick()"
  />
</template>

<script setup lang="ts">
import { copyToClipboard } from 'quasar';
import { useQuasar } from 'quasar';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';

const props = defineProps<{ value?: string }>();

const quasar = useQuasar();
const { t } = useI18n();

const isCopy = ref(false);
const handleClick = () => {
  if (props.value)
    copyToClipboard(props.value)
      .then(() => {
        isCopy.value = true;
        quasar.notify({
          color: 'info',
          message: t('copyButton.copied'),
        });
      })
      .catch(() => {
        quasar.notify({
          color: 'negative',
          message: t('copyButton.copyFailed'),
        });
      });
  setTimeout(() => (isCopy.value = false), 500);
};
const getIcon = computed(() =>
  isCopy.value ? 'check_circle_outline' : 'content_copy'
);
</script>
