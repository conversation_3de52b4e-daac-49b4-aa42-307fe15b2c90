<template>
  <div
    class="flex items-center justify-center gap-x-xs text-bold"
    :class="statusColor"
  >
    <q-icon :name="statusIcon" size="1.25rem" />
    <span class="text-center">{{ t(`options.${statusLabel}`) }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { TradeStatusEnum } from 'src/utils/enums';
import { tradeStatusOptions } from 'src/utils/options';

const { status } = defineProps<{
  status: TradeStatusEnum;
}>();

const { t } = useI18n();

const statusLabel = computed(
  () =>
    tradeStatusOptions.find((option) => option.value === status)?.label ||
    'undefined'
);

const statusColor = computed(() => {
  switch (status) {
    case TradeStatusEnum.Pending:
      return 'text-grey';
    case TradeStatusEnum.Accepted:
      return 'text-blue-6';
    case TradeStatusEnum.BankAccountAssigned:
      return 'text-orange-6';
    case TradeStatusEnum.PaymentMade:
      return 'text-deep-orange-6';
    case TradeStatusEnum.PaymentConfirmed:
      return 'text-teal-6';
    case TradeStatusEnum.Completed:
      return 'text-green-6';
    case TradeStatusEnum.Cancelled:
      return 'text-negative';
    case TradeStatusEnum.Expired:
      return 'text-warning';
    default:
      return 'text-grey';
  }
});

const statusIcon = computed(() => {
  switch (status) {
    case TradeStatusEnum.Pending:
      return 'hourglass_empty';
    case TradeStatusEnum.Accepted:
      return 'task_alt';
    case TradeStatusEnum.BankAccountAssigned:
      return 'account_balance';
    case TradeStatusEnum.PaymentMade:
      return 'paid';
    case TradeStatusEnum.PaymentConfirmed:
      return 'fact_check';
    case TradeStatusEnum.Completed:
      return 'check_circle';
    case TradeStatusEnum.Cancelled:
      return 'cancel';
    case TradeStatusEnum.Expired:
      return 'schedule';
    default:
      return 'info';
  }
});
</script>
