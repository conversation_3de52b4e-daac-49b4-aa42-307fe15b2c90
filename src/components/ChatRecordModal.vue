<template>
  <q-btn
    outline
    color="accent"
    :label="
      hasMessage
        ? t('chatRecordModal.btnViewChatRecord')
        : t('chatRecordModal.btnNoChatRecord')
    "
    :disable="!hasMessage"
    :loading="loadingMessages"
    class="full-width"
    @click="handleShowChatRecord"
  />

  <q-dialog v-model="showChatRecord" full-height>
    <q-card class="chat-box column no-wrap">
      <header class="flex items-start justify-between q-pa-md no-wrap">
        <section class="text-bold">
          <span class="title">{{ t('chatRecordModal.title') }}</span>
          <div class="flex gap-x-xs">
            <span class="text-bold text-grey"
              >{{ t('chatRecordModal.orderNumber') }}:</span
            >
            <span>{{ orderNumber }}</span>
          </div>
        </section>
        <q-btn v-close-popup flat round icon="close" color="grey" />
      </header>

      <q-separator />

      <section
        ref="messagesContainer"
        class="messages q-pa-md flex-1 column gap-y-sm no-wrap"
      >
        <loading-component v-if="loadingMessages" class="full-height" />
        <div
          v-else-if="messages.length === 0"
          class="text-bold text-grey text-center"
        >
          {{ t('chatRecordModal.emptyMessage') }}
        </div>
        <template v-else>
          <message-item
            v-for="message in messages"
            :key="message.messageId"
            :message="message"
            @load="scrollToBottom"
          />
        </template>
      </section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { debounce } from 'quasar';
import { computed, nextTick, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useGetMessages } from 'src/pages/trade/api';
import MessageItem from 'src/pages/trade/TradingComponents/MessageItem.vue';
import LoadingComponent from './LoadingComponent.vue';

const { tradeId, orderNumber } = defineProps<{
  tradeId: number;
  orderNumber: string;
}>();

const { t } = useI18n();
const {
  data: messages,
  loading: loadingMessages,
  refresh,
} = useGetMessages({
  id: tradeId,
});

const showChatRecord = ref<boolean>(false);
const messagesContainer = ref<HTMLElement | null>(null);

const scrollToBottom = debounce(() => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
  });
}, 100);

const hasMessage = computed(() => messages.value?.length);

const handleShowChatRecord = () => {
  showChatRecord.value = true;
  refresh();
};
</script>

<style scoped>
.chat-box {
  width: 40rem;
}
.title {
  font-size: 1rem;
}
.messages {
  overflow-y: auto;
}
</style>
