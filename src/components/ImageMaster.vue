<template>
  <q-img
    @click="() => (isFullScreen = true)"
    :src="src"
    :width="width"
    :height="height"
    @load="(file: Event) => $emit('load', file)"
    class="rounded-borders"
  />
  <q-dialog maximized v-model="isFullScreen">
    <div
      class="background flex items-center justify-center"
      @click="isFullScreen = false"
    >
      <q-btn
        v-close-popup
        class="bg-white fixed-top-right"
        dense
        unelevated
        color="black"
        icon="close"
      />
      <q-img :src="src" />
    </div>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';

defineProps<{ src: string; width?: string; height?: string }>();
defineEmits(['load']);
const isFullScreen = ref(false);
</script>

<style scoped>
.background {
  width: 100vw;
  min-height: 100vh;
  padding: 3.5vh 4vw;
}
</style>
