<template>
  <span>
    {{ formattedMinutes(timeLeft) }}:{{ formattedSeconds(timeLeft) }}
  </span>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue';
import { formattedMinutes, formattedSeconds } from 'src/utils/dateTool';

const { startAt, duration } = defineProps<{
  startAt: string;
  duration: number;
}>();
const emit = defineEmits<{
  (e: 'countdown-finished'): void;
}>();

const timeLeft = ref(0);
const interval = ref<NodeJS.Timeout | null>(null);

const updateTimeLeft = () => {
  const updatedTime = new Date(startAt).getTime();
  const expiryTime = updatedTime + duration;
  const now = Date.now();
  timeLeft.value = Math.max(0, expiryTime - now);
};

onMounted(() => {
  updateTimeLeft();
  if (timeLeft.value === 0) {
    emit('countdown-finished');
    return;
  }
  interval.value = setInterval(updateTimeLeft, 1000);
});
onUnmounted(() => {
  if (interval.value) {
    clearInterval(interval.value);
    interval.value = null;
  }
});

watch(timeLeft, (newVal, oldVal) => {
  if (newVal === 0 && oldVal !== 0) {
    emit('countdown-finished');
    if (interval.value) {
      clearInterval(interval.value);
      interval.value = null;
    }
  }
});
</script>
