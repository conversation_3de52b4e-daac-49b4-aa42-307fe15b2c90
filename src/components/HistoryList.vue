<template>
  <history-item
    v-for="history in historyList"
    :key="history.sourceId"
    :history="history"
    @open-details-modal="openModal"
  />
  <history-details-modal
    v-if="selectedHistory"
    v-model="showModal"
    :history="selectedHistory"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { History } from 'src/pages/history/api';
import HistoryItem from './HistoryItem.vue';
import HistoryDetailsModal from './HistoryDetailsModal.vue';

const showModal = ref(false);
const selectedHistory = ref<History | null>(null);

const { historyList } = defineProps<{
  historyList: Array<History>;
}>();

const openModal = (history: History) => {
  selectedHistory.value = history;
  showModal.value = true;
};
</script>
