<template>
  <q-btn
    flat
    round
    :icon="themeStore.isDark ? 'nights_stay' : 'wb_sunny'"
    :color="isOnDrawer && !themeStore.isDark ? 'accent' : 'white'"
    @click="handleToggleTheme"
  />
</template>

<script setup lang="ts">
import { useThemeStore } from '../stores';

const { isOnDrawer = false } = defineProps<{ isOnDrawer?: boolean }>(); // this flag is used to change the button color when it is inside the MenuDrawer on mobile devices, to match the design

const themeStore = useThemeStore();

const handleToggleTheme = () => {
  themeStore.toggleTheme();
};
</script>
