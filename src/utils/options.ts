import {
  ChainTransferDirectionEnum,
  ChainTxStatusEnum,
  FlowTypeEnum,
  LedgerSourceTypeEnum,
  TradeInprogressStatusEnum,
  TradeStatusEnum,
  TradeTypeEnum,
  WalletNetworkEnum,
} from './enums';

type EnumObjBase<
  T extends Record<string, string | number>,
  Other = object
> = Record<keyof T, { label: string; value: ValueOf<T> } & Other>;

const eNumEntities = <T extends object>(originEnum: T) => {
  const keyofEnums = Object.keys(originEnum);
  const keysLength = keyofEnums.length;
  const lengthAvg = Math.floor(keysLength / 2);
  const vailedKeys = keyofEnums.slice(-lengthAvg);

  const result = vailedKeys.reduce(
    (arr, key) => {
      const enumKey = key as keyof typeof originEnum;
      const valueOfObj = originEnum[enumKey];
      if (!arr.keys.includes(enumKey) && valueOfObj !== undefined)
        return {
          keys: [...arr.keys, enumKey],
          values: [...arr.values, valueOfObj],
        };
      return arr;
    },
    {
      keys: [] as Array<keyof typeof originEnum>,
      values: [] as Array<ValueOf<typeof originEnum>>,
    }
  );

  return result;
};

const walletNetworkObj: EnumObjBase<typeof WalletNetworkEnum> = {
  TRC20: {
    label: 'trc20WalletNetworkEnum',
    value: WalletNetworkEnum.TRC20,
  },
  ERC20: {
    label: 'erc20WalletNetworkEnum',
    value: WalletNetworkEnum.ERC20,
  },
};
const walletNetworkOptions = eNumEntities<typeof WalletNetworkEnum>(
  WalletNetworkEnum
).keys.map((enumKey) => {
  const { label = 'undefined', value } = walletNetworkObj[enumKey];
  return { label, value };
});

const tradeTypeObj: EnumObjBase<typeof TradeTypeEnum> = {
  Buy: {
    label: 'buyTradeTypeEnum',
    value: TradeTypeEnum.Buy,
  },
  Sell: {
    label: 'sellTradeTypeEnum',
    value: TradeTypeEnum.Sell,
  },
};
const tradeTypeOptions = eNumEntities<typeof TradeTypeEnum>(
  TradeTypeEnum
).keys.map((enumKey) => {
  const { label = 'undefined', value } = tradeTypeObj[enumKey];
  return { label, value };
});

const tradeStatusObj: EnumObjBase<typeof TradeStatusEnum> = {
  Pending: {
    label: 'pendingTradeStatusEnum',
    value: TradeStatusEnum.Pending,
  },
  Accepted: {
    label: 'acceptedTradeStatusEnum',
    value: TradeStatusEnum.Accepted,
  },
  BankAccountAssigned: {
    label: 'bankAccountAssignedTradeStatusEnum',
    value: TradeStatusEnum.BankAccountAssigned,
  },
  PaymentMade: {
    label: 'paymentMadeTradeStatusEnum',
    value: TradeStatusEnum.PaymentMade,
  },
  PaymentConfirmed: {
    label: 'paymentConfirmedTradeStatusEnum',
    value: TradeStatusEnum.PaymentConfirmed,
  },
  Completed: {
    label: 'completedTradeStatusEnum',
    value: TradeStatusEnum.Completed,
  },
  Cancelled: {
    label: 'cancelledTradeStatusEnum',
    value: TradeStatusEnum.Cancelled,
  },
  Expired: {
    label: 'expiredTradeStatusEnum',
    value: TradeStatusEnum.Expired,
  },
};
const tradeStatusOptions = eNumEntities<typeof TradeStatusEnum>(
  TradeStatusEnum
).keys.map((enumKey) => {
  const { label = 'undefined', value } = tradeStatusObj[enumKey];
  return { label, value };
});

const inProgessObj: EnumObjBase<typeof TradeInprogressStatusEnum> = {
  SetBank: {
    label: 'setbankTradeInprogressStatusEnum',
    value: TradeInprogressStatusEnum.SetBank,
  },
  GoToTrade: {
    label: 'goToTradeTradeInprogressStatusEnum',
    value: TradeInprogressStatusEnum.GoToTrade,
  },
  NeedFinishOrder: {
    label: 'needFinishOrderTradeInprogressStatusEnum',
    value: TradeInprogressStatusEnum.NeedFinishOrder,
  },
};
const inProgressOrderStatusOptions = eNumEntities<
  typeof TradeInprogressStatusEnum
>(TradeInprogressStatusEnum).keys.map((enumKey) => {
  const { label = 'undefined', value } = inProgessObj[enumKey];
  return { label, value };
});

const chainTransferDirectionObj: EnumObjBase<
  typeof ChainTransferDirectionEnum
> = {
  Deposit: {
    label: 'depositChainTransferDirectionEnum',
    value: ChainTransferDirectionEnum.Deposit,
  },
  Withdrawal: {
    label: 'withdrawalChainTransferDirectionEnum',
    value: ChainTransferDirectionEnum.Withdrawal,
  },
  Internal: {
    label: 'internalChainTransferDirectionEnum',
    value: ChainTransferDirectionEnum.Internal,
  },
};
const chainTransferDirectionOptions = eNumEntities<
  typeof ChainTransferDirectionEnum
>(ChainTransferDirectionEnum).keys.map((enumKey) => {
  const { label = 'undefined', value } = chainTransferDirectionObj[enumKey];
  return { label, value };
});

const chainTxStatusObj: EnumObjBase<typeof ChainTxStatusEnum> = {
  Pending: {
    label: 'pendingChainTxStatusEnum',
    value: ChainTxStatusEnum.Pending,
  },
  Confirmed: {
    label: 'confirmedChainTxStatusEnum',
    value: ChainTxStatusEnum.Confirmed,
  },
  Failed: {
    label: 'failedChainTxStatusEnum',
    value: ChainTxStatusEnum.Failed,
  },
};
const chainTxStatusOptions = eNumEntities<typeof ChainTxStatusEnum>(
  ChainTxStatusEnum
).keys.map((enumKey) => {
  const { label = 'undefined', value } = chainTxStatusObj[enumKey];
  return { label, value };
});

const flowTypeEnumObj: EnumObjBase<typeof FlowTypeEnum> = {
  Deposit: {
    label: 'depositFlowTypeEnum',
    value: FlowTypeEnum.Deposit,
  },
  Withdrawal: {
    label: 'withdrawalFlowTypeEnum',
    value: FlowTypeEnum.Withdrawal,
  },
  Buy: {
    label: 'buyFlowTypeEnum',
    value: FlowTypeEnum.Buy,
  },
  Sell: {
    label: 'sellFlowTypeEnum',
    value: FlowTypeEnum.Sell,
  },
};
const flowTypeEnumOptions = eNumEntities<typeof FlowTypeEnum>(
  FlowTypeEnum
).keys.map((enumKey) => {
  const { label = 'undefined', value } = flowTypeEnumObj[enumKey];
  return { label, value };
});

const ledgerSourceTypeEnumObj: EnumObjBase<typeof LedgerSourceTypeEnum> = {
  Unknown: {
    label: 'unknownLedgerSourceTypeEnum',
    value: LedgerSourceTypeEnum.Unknown,
  },
  Trade: {
    label: 'tradeLedgerSourceTypeEnum',
    value: LedgerSourceTypeEnum.Trade,
  },
  ChainTxLog: {
    label: 'chainTxLogLedgerSourceTypeEnum',
    value: LedgerSourceTypeEnum.ChainTxLog,
  },
  BalanceTransfer: {
    label: 'balanceTransferLedgerSourceTypeEnum',
    value: LedgerSourceTypeEnum.BalanceTransfer,
  },
};
const ledgerSourceTypeEnumOptions = eNumEntities<typeof LedgerSourceTypeEnum>(
  LedgerSourceTypeEnum
).keys.map((enumKey) => {
  const { label = 'undefined', value } = ledgerSourceTypeEnumObj[enumKey];
  return { label, value };
});

export {
  walletNetworkOptions,
  tradeTypeOptions,
  tradeStatusOptions,
  inProgressOrderStatusOptions,
  chainTransferDirectionOptions,
  chainTxStatusOptions,
  flowTypeEnumOptions,
  ledgerSourceTypeEnumOptions,
};
