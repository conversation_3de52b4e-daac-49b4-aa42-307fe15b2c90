enum TradeTypeEnum {
  Buy = 0,
  Sell = 1,
}

enum TradeModeEnum {
  Normal,
  MerchantProxy,
  Forwarded,
}

enum TradeDisputeStatusEnum {
  None,
  InDispute,
  Resolved,
}

enum TradeStatusEnum {
  Pending = 0, // Order created, waiting for agent
  Accepted = 1, // Agent accepted, bank not set
  BankAccountAssigned = 2, // Bank set, waiting for user to pay
  PaymentMade = 3, // User paid
  PaymentConfirmed = 4, // Agent confirmed payment
  Completed = 5, // Trade finished
  Cancelled = 6, // Trade cancelled
  Expired = 7, // Trade expired
}

enum TradeInprogressStatusEnum {
  SetBank = 1,
  GoToTrade = 2,
  NeedFinishOrder = 3,
}

enum WalletNetworkEnum {
  TRC20 = 1,
  ERC20 = 2,
}

enum SenderRole {
  User = 'User',
  Agent = 'Agent',
  SupportTeam = 'SupportTeam',
}

enum ChatMessageType {
  Text,
  Image,
  System,
  File,
}

enum ChainTransferDirectionEnum {
  Deposit = 1,
  Withdrawal = 2,
  Internal = 3,
}

enum ChainTxStatusEnum {
  Pending = 0,
  Confirmed = 1,
  Failed = 2,
}

enum FlowTypeEnum {
  Deposit = 1,
  Withdrawal = 2,
  Buy = 3,
  Sell = 4,
}

enum LedgerSourceTypeEnum {
  Unknown = 0,
  Trade = 1,
  ChainTxLog = 2,
  BalanceTransfer = 3,
}

enum OrderAcceptProblem {
  noBank = 10202,
  notEnough = 10200,
  timeOut = 10301,
}
function stringToEnumValue<T extends object>(
  enumObj: T,
  key: string
): T[keyof T] | undefined {
  return (enumObj as any)[key] as T[keyof T] | undefined;
}
export {
  stringToEnumValue,
  TradeTypeEnum,
  TradeModeEnum,
  TradeDisputeStatusEnum,
  TradeStatusEnum,
  WalletNetworkEnum,
  TradeInprogressStatusEnum,
  SenderRole,
  ChatMessageType,
  ChainTransferDirectionEnum,
  ChainTxStatusEnum,
  FlowTypeEnum,
  LedgerSourceTypeEnum,
  OrderAcceptProblem,
};
