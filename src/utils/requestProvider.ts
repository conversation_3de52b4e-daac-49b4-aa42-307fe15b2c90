import { AxiosError } from 'axios';
import hooks from 'src/hooks';
import { ref } from 'vue';
import { Options, Service, useRequest } from 'vue-request';

type CustomProps = {
  noFeedback?: boolean;
  noTempData?: boolean;
  noErrorNotify?: boolean;
};
export type ErrorRes = {
  title?: string;
  errors?: Record<string, Array<string>>;
  errorCode?: number;
};
export const requestProvider = <DATA, Params = unknown>(
  service: Service<DATA, [Params]>,
  options: Options<DATA, [Params]>,
  customProps?: CustomProps
) => {
  const tempData = ref<DATA>();
  const requestInstance = useRequest<DATA, [Params]>(service, {
    ...options,
    onSuccess: (virgilRes, params) => {
      if (!customProps?.noTempData) tempData.value = virgilRes;
      if (options.manual && !customProps?.noFeedback)
        hooks.useSuccessNotify('Success');
      if (options.onSuccess) options.onSuccess(virgilRes, params);
    },
    onError: (error, params) => {
      const virgilError = error as Error | AxiosError<ErrorRes>;
      const useCode =
        'response' in virgilError ? virgilError.response?.data : undefined;

      if (
        useCode !== undefined &&
        !customProps?.noErrorNotify &&
        options.manual
      ) {
        if (useCode.errorCode === 10004) {
          const allErrors = useCode.errors ?? {};
          const allMessages = Object.values(allErrors)
            .flat()
            .filter(Boolean)
            .join('\n');

          if (allMessages) {
            hooks.useErrorNotify(allMessages);
          } else {
            const fallback = useCode.title || 'Unknown error';
            hooks.useErrorNotify(fallback);
          }
        } else {
          const identityError =
            useCode.errors?.Identity?.[0] || useCode.title || 'Unknown error';
          hooks.useErrorNotify(identityError);
        }
      }

      if (options.onError) options.onError(virgilError, params);
    },
  });
  return { ...requestInstance, tempData };
};
