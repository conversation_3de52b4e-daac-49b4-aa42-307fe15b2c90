import axios from 'axios';
import { forage } from './foragePkg';
import { router } from 'src/router';

const externalAxiosProvider = axios.create({
  baseURL: import.meta.env.VITE_API_BASE,
});

externalAxiosProvider.interceptors.request.use(async (config) => {
  const proxyToken = await forage<string>().getItem('proxyToken');
  if (!proxyToken) return config;

  const pureConfig = { ...config };
  pureConfig.headers.Authorization = `Bearer ${proxyToken}`;
  return pureConfig;
});

externalAxiosProvider.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      await forage().removeItem('proxyToken');
      const currentComponent =
        router.currentRoute.value.matched[0]?.instances?.default;
      if (currentComponent && 'verificationError' in currentComponent) {
        currentComponent.verificationError = true;
      } else {
        // If we can't access the component directly, reload the page to show the error
        window.location.reload();
      }
    }
    return Promise.reject(error);
  }
);

export { externalAxiosProvider };
