const dateFormator = {
  accurate: 'YYYY-MM-DD HH:mm:ss',
  day: 'dddd',
  clock: 'YYYY MM-DD dddd. HH:mm:ss [(Asia/Beijing SDT +08:00)]',
  date: 'YYYY.MM.DD',
};

const secondsToMinutes = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs
    .toString()
    .padStart(2, '0')}`;
};

const formattedMinutes = (time: number) =>
  String(Math.floor(time / 60000)).padStart(2, '0');

const formattedSeconds = (time: number) =>
  String(Math.floor((time % 60000) / 1000)).padStart(2, '0');
export { dateFormator, secondsToMinutes, formattedMinutes, formattedSeconds };
