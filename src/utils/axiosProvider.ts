import axios from 'axios';
import { LoginRes } from 'src/pages/auth/api/useLogin';
import { router } from 'src/router';
import { useAuthStore } from 'src/stores';
import { forage } from './foragePkg';

const axiosProvider = axios.create({
  baseURL: import.meta.env.VITE_API_BASE,
});
const auth = useAuthStore();

axiosProvider.interceptors.request.use(async (config) => {
  const auth = await forage<LoginRes>().getItem('loginRes');
  const proxyToken = await forage<string>().getItem('proxyToken');
  if (!auth && !proxyToken) return config;
  const pureConfig = { ...config };
  pureConfig.headers.Authorization = `Bearer ${auth?.token ?? proxyToken}`;
  return pureConfig;
});

// temporary redirect user to login page if unauthorized
axiosProvider.interceptors.response.use(
  (response) => response,
  async (error) => {
    const currentPath = router.currentRoute.value.path;
    const isExternal = currentPath.startsWith('/external/');
    if (error.response?.status === 401 && !isExternal) {
      auth.logout();
      router.replace({ name: 'login' });
    }

    return Promise.reject(error);
  }
);

export { axiosProvider };
