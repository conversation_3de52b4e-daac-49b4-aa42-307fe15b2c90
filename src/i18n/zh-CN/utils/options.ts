export default {
  undefined: '未定义',

  // WalletNetworkEnum
  trc20WalletNetworkEnum: 'TRC20',
  erc20WalletNetworkEnum: 'ERC20',

  // TradeTypeEnum
  buyTradeTypeEnum: '购买',
  sellTradeTypeEnum: '出售',

  // TradeStatusEnum
  pendingTradeStatusEnum: '待匹配',
  acceptedTradeStatusEnum: '已接受',
  bankAccountAssignedTradeStatusEnum: '已分配银行账户',
  paymentMadeTradeStatusEnum: '已付款',
  paymentConfirmedTradeStatusEnum: '付款已确认',
  completedTradeStatusEnum: '已完成',
  cancelledTradeStatusEnum: '已取消',
  expiredTradeStatusEnum: '已过期',

  // Trade inProgress Status
  setbankTradeInprogressStatusEnum: '设置银行',
  goToTradeTradeInprogressStatusEnum: '前往交易',
  needFinishOrderTradeInprogressStatusEnum: '需完成订单',

  // ChainTransferDirectionEnum
  depositChainTransferDirectionEnum: '充币',
  withdrawalChainTransferDirectionEnum: '提币',
  internalChainTransferDirectionEnum: '内部转账',

  // ChainTxStatusEnum
  pendingChainTxStatusEnum: '待确认',
  confirmedChainTxStatusEnum: '已确认',
  failedChainTxStatusEnum: '失败',

  // FlowTypeEnum
  depositFlowTypeEnum: '存款',
  withdrawalFlowTypeEnum: '提款',
  buyFlowTypeEnum: '购买',
  sellFlowTypeEnum: '出售',

  // LedgerSourceTypeEnum
  unknownLedgerSourceTypeEnum: '未知',
  tradeLedgerSourceTypeEnum: '交易',
  chainTxLogLedgerSourceTypeEnum: '链上交易记录',
  balanceTransferLedgerSourceTypeEnum: '余额转账',
};
