export default {
  title: 'Payment Details',
  currentPrice: 'Current Price',
  orderTime: 'Order Time',
  paymentAmount: 'Payment Amount',
  orderNumber: 'Order Number',
  recipientBank: 'Recipient Bank',
  recipientAccount: 'Account Number',
  recipientName: 'Recipient Name',
  paymentMethod: 'Payment Method',
  paymentNotice: 'Payment Notice',
  paymentInstructions:
    'Please complete the payment within 15 minutes, otherwise the order will be automatically cancelled.',
  confirmPaymentButton: 'I have completed the payment',
  confirmed: 'Confirmed',
  cancelButton: 'Cancel Order',
  customerService: 'Chat with agent',
  online: 'Online',
  typeMessage: 'Type a message...',
  welcomeMessage: 'Hello! Welcome to K28U customer service.',
  helpMessage: 'How can I help you with your order today?',
  autoReply:
    'Thank you for your message. Our customer service team will respond shortly.',
  imageReceived: 'Thank you for sending the image. We will review it shortly.',
  invalidFileType: 'Please select an image file (JPG, PNG, GIF).',
  fileTooLarge: 'Image file is too large. Maximum size is 5MB.',
  imageLoadError: 'Failed to load the image. Please try again.',
  imageUploadError: 'Failed to open file selector. Please try again.',
  connectionError: 'Connection error. Please try again.',
  messageSendError: 'Failed to send message. Please try again.',
  verifying: 'Verifying your entry code...',
  verificationError:
    'Failed to verify entry code. Please recreate the order to obtain a new entry code.',
  agentNotAcceptingOrderError:
    'Agent is not accepting orders at the moment. Please try again later.',
  orderExpiredError: 'Order expired. Please create a new order.',
  tryAgain: 'Try Again',
  copiedOrderNumber: 'Order number copied to clipboard',
  copiedAccountNumber: 'Account number copied to clipboard',
  copiedPayerBankAccountName: 'Payer bank account name copied to clipboard',
  copiedFiatAmount: 'Fiat amount copied to clipboard',
  copyFailed: 'Failed to copy to clipboard',
  paymentConfirmed: 'Payment confirmed. We are processing your order.',
  paymentError: 'Error confirming payment. Please try again.',
  orderCompleted: 'Your order has been completed successfully!',
  cancelConfirmTitle: 'Cancel Order',
  cancelConfirmMessage:
    'Are you sure you want to cancel this order? This action cannot be undone.',
  timeRemaining: 'Time Remaining',
  orderExpired: 'Order expired. Please create a new order.',
  cryptoAmount: 'Crypto Amount',
  agentFee: 'Agent Fee',
  orderStatus: 'Order Status',
  network: 'Network',
  tradeType: 'Trade Type',
  uploadPaymentProofButton: 'Upload Payment Proof',
  viewPaymentProofButton: 'View Payment Proof',
  paymentProofDialogTitle: 'Payment Proof',
  paymentProofUploaded: 'Payment proof uploaded successfully',
  paymentProofUploadError: 'Failed to upload payment proof. Please try again.',
  closeButton: 'Close',
  confirmPaymentDialogTitle: 'Confirm Payment',
  confirmPaymentDialogMessage:
    'Are you sure you want to confirm the payment? This action cannot be undone.',
  confirmButton: 'Yes',
  cancelButtonn: 'Cancel',
  confirmFundsCheckbox:
    'I confirm the funds are in my account, and the Payer’s Name and Amount are correct.',
  paymentDetailsTitle: 'Payment Details',
  paymentDetailsBankName: 'Bank Name',
  paymentDetailsAccountName: 'Account Name',
  paymentDetailsAccountNumber: 'Account Number',
  paymentDetailsBranchCode: 'Branch Code',
  copiedPaymentAccountNumber: 'Payment account number copied to clipboard',
  waitingForAgentMessage: 'Waiting for agent to send payment details...',
  agentWillSendDetailsSoon: 'Agent will send details soon',
  transactionCompleted: 'Transaction Completed',
  thankYouForPayment: 'Thank you for your payment!',
  transactionNo: 'Transaction No',
  completedAt: 'Completed At',
  amountPaid: 'Amount Paid',
  cryptoReceived: 'Crypto Received',
  exchangeRate: 'Exchange Rate',
  recipientInfo: 'Recipient Info',
  needHelp:
    'Need help? Contact our support if you have questions about this transaction.',
  downloadReceipt: 'Download Receipt',
  transactionDetails: 'Transaction Details',
  viewTransactionHistory: 'View Transaction History',
  backToDashboard: 'Back to Dashboard',
  yourCrypto: 'Your Crypto',
  paymentInfo: 'Payment Information',
  bankTransferDetails: 'Bank Transfer Details',
  receiverDetails: 'Receiver Details',
  printReceipt: 'Print Receipt',
  saveAsPdf: 'Save as PDF',
  shareReceipt: 'Share Receipt',
  transactionSummary: 'Transaction Summary',
  helpAndSupport: 'Help & Support',
  faqTitle: 'Frequently Asked Questions',
  supportCenter: 'Support Center',
  chatWithUs: 'Chat with Us',
  contactPhone: 'Contact Phone',
  howItWorksTitle: 'How It Works',
  securityGuarantee: 'Security Guarantee',
  newOrder: 'New Order',
  viewBlockchainExplorer: 'View on Blockchain Explorer',
  orderDetails: 'Order Details',
  paymentDetailsSubtitle:
    'Please use the following bank details to complete your payment',
  copyText: 'Copy to clipboard',
  copyRecipientAccount: 'Copy recipient account',
  copyPayerBankAccountName: 'Copy payer bank account name',
  copyAccountNumber: 'Copy account number',
  orderOverview: 'Order Overview',
  paymentStatus: 'Payment Status',
  processingPayment: 'Processing Payment',
  awaitingPayment: 'Awaiting Payment',
  verifyingPayment: 'Verifying Payment',
  transferDetails: 'Transfer Details',
  accountInformation: 'Account Information',
  uploaded: 'Uploaded',
  notesTitle: 'Notes:',
  note1:
    'This platform only supports USDT transactions, and the actual number may not be exactly the same.',
  note2:
    'This platform cannot verify the identity of the sender, please ensure the accuracy of the information.',
  note3:
    'This platform can only verify whether the funds have arrived in the designated account.',
  note4:
    'Please confirm that you can use USDT for payment before placing an order.',
  note5:
    'Minimum purchase amount: 100USDT, small amounts may not be processed due to network congestion.',
  note6:
    'Please avoid phishing websites and malicious links to prevent account theft.',
  note7:
    'If you have any questions about the order, please contact our customer service through the website.',
};
