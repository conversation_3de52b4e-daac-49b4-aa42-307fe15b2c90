export default {
  enterTransDetail: 'Please Enter Transfer Details',
  networkLabel: 'Select Network',
  walletAddressLabel: 'Wallet Address',
  walletAddressPlaceholder: 'Please enter Wallet Address',
  walletAddressError: 'Please enter Wallet Address',
  amountLabel: 'Transfer Amount',
  amountPlaceholder: 'Please enter Transfer Amount',
  amountError: 'Please enter a valid amount',
  exceedsAvailableBalance: 'Amount exceeds available balance',
  availableBalance: 'Available Balance',
  remarkLabel: 'Remark',
  remarkPlaceholder: 'Enter Remark Content (optional)',
  submit: 'Submit',
  transSuccess: 'Transfer Succesfully',
};
