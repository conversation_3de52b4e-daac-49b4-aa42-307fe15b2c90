export default {
  title: 'Buying USDT',
  usdtAmountLabel: 'I want to Buy',
  usdtAmountError: 'Please enter a valid Amount',
  noExchangeRate: 'Cannot get Exchange Rate',
  fiatAmountLabel: 'I will Pay',
  fiatAmountError: 'Please enter a valid Amount',
  bankAccountNameLabel: 'Bank Account Name',
  bankAccountNamePlaceholder: 'Please enter Your Bank Account Name',
  bankAccountNameError: 'Please enter Your Bank Account Name',
  orderInformation: 'Order Information',
  amountToBuy: 'Amount to Buy',
  amountToPay: 'Amount to Pay',
  rateDescription: 'Exchange Rate fixed at Order Creation',
  confirmTitle: 'Please Note',
  confirmMessage:
    'When making a transfer, do not include any text related to cryptocurrency. If the remittance details do not match, the transaction will not be processed, and the transferred amount will be refunded. Any related handling fees will be deducted from the refund. Thank you.',
  submit: 'Start Pairing',
};
