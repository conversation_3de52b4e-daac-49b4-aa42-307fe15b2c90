export default {
  title: 'Selling USDT',
  usdtAmountLabel: 'I want to Sell',
  availableBalance: 'Available Balance',
  usdtAmountError: 'Please enter a valid amount',
  exceedsAvailableBalance: 'Amount exceeds available balance',
  noExchangeRate: 'Cannot get Exchange Rate',
  fiatAmountLabel: 'I will receive',
  fiatAmountError: 'Please enter a valid amount',
  bankAccountInformation: 'Bank Account Information',
  btnSelectBankAccount: 'Select Bank Account',
  bankNameLabel: 'Bank Name',
  bankNamePlaceholder: 'Please enter Your Bank Name',
  bankNameError: 'Please enter Your Bank Name',
  accountNumberLabel: 'Account Number',
  accountNumberPlaceholder: 'Please enter Your Account Number',
  accountNumberError: 'Please enter Your Account Number',
  accountNameLabel: 'Account Name',
  accountNamePlaceholder: 'Please enter Your Account Name',
  accountNameError: 'Please enter Your Account Name',
  branchCodeLabel: 'Bank Branch Code',
  branchCodePlaceholder: 'Please enter Your Bank Branch Code',
  branchCodeError: 'Please enter Your Bank Branch Code',
  orderInformation: 'Order Information',
  amountToSell: 'Amount to Sell',
  amountToReceive: 'You will Receive',
  rateDescription: 'Exchange Rate fixed at Order Creation',
  confirmTitle: 'Please Note',
  confirmMessage:
    'When making a transfer, do not include any text related to cryptocurrency. If the remittance details do not match, the transaction will not be processed, and the transferred amount will be refunded. Any related handling fees will be deducted from the refund. Thank you.',
  submit: 'Start Pairing',
};
