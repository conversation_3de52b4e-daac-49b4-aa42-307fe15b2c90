export default {
  undefined: 'Undefined',

  // WalletNetworkEnum
  trc20WalletNetworkEnum: 'TRC20',
  erc20WalletNetworkEnum: 'ERC20',

  // TradeTypeEnum
  buyTradeTypeEnum: 'Buy',
  sellTradeTypeEnum: 'Sell',

  // TradeStatusEnum
  pendingTradeStatusEnum: 'Pending',
  acceptedTradeStatusEnum: 'Accepted',
  bankAccountAssignedTradeStatusEnum: 'Bank Account Assigned',
  paymentMadeTradeStatusEnum: 'Payment Made',
  paymentConfirmedTradeStatusEnum: 'Payment Confirmed',
  completedTradeStatusEnum: 'Completed',
  cancelledTradeStatusEnum: 'Cancelled',
  expiredTradeStatusEnum: 'Expired',

  // Trade inProgress Status
  setbankTradeInprogressStatusEnum: 'Set Bank',
  goToTradeTradeInprogressStatusEnum: 'Go to Trade',
  needFinishOrderTradeInprogressStatusEnum: 'Complete the Trade',

  // ChainTransferDirectionEnum
  depositChainTransferDirectionEnum: 'Deposit',
  withdrawalChainTransferDirectionEnum: 'Withdrawal',
  internalChainTransferDirectionEnum: 'Internal',

  // ChainTxStatusEnum
  pendingChainTxStatusEnum: 'Pending',
  confirmedChainTxStatusEnum: 'Confirmed',
  failedChainTxStatusEnum: 'Failed',

  // FlowTypeEnum
  depositFlowTypeEnum: 'Deposit',
  withdrawalFlowTypeEnum: 'Withdrawal',
  buyFlowTypeEnum: 'Buy',
  sellFlowTypeEnum: 'Sell',

  // LedgerSourceTypeEnum
  unknownLedgerSourceTypeEnum: 'Unknown',
  tradeLedgerSourceTypeEnum: 'Trade',
  chainTxLogLedgerSourceTypeEnum: 'Chain Tx Log',
  balanceTransferLedgerSourceTypeEnum: 'Balance Transfer',
};
