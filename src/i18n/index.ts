import { createI18n } from 'vue-i18n';
import en_US from './en-US';
import vi_VN from './vi-VN';
import zh_CN from './zh-CN';
import zh_TW from './zh-TW';
import { storageHelper } from 'src/utils/foragePkg';

export enum K28LangsNum {
  En_US = 'en-US',
  Zh_TW = 'zh-TW',
  Vi_VN = 'vi-VN',
  Zh_CN = 'zh-CN',
}

export const messages = {
  'en-US': en_US,
  'zh-TW': zh_TW,
  'vi-VN': vi_VN,
  'zh-CN': zh_CN,
};

export const langs = [
  { locale: 'en-US', name: 'English' },
  { locale: 'zh-TW', name: '中文(繁體)' },
  { locale: 'vi-VN', name: 'Tiếng Việt' },
  { locale: 'zh-CN', name: '中文(简体)' },
];

export const i18n = createI18n({
  legacy: false,
  globalInjection: true,
  locale: storageHelper<K28LangsNum>('locale').getItem() ?? K28LangsNum.En_US,
  fallbackLocale: false,
  messages,
});
