import {
  assetOverview,
  breadcrumbComponent,
  chatRecordModal,
  confirmDialog,
  datePicker,
  historyDetailsModal,
  historyItem,
  copyButton,
  qrScanner,
  transactionDetailsModal,
  paginationComponent,
} from './components';
import { useCountryCodeOpts } from './hooks';
import { dropDownUser, menuDrawer, ordersDrawer, navBar } from './layouts';
import {
  accountItem,
  addAccountModal,
  addBankForm,
  addEPayForm,
  accountsPage,
  loginPage,
  forgetPage,
  addMailFormStep,
  sendCodeFormStep,
  newPwFormStep,
  pwDoneModal,
  registerPage,
  registerBase,
  sendCodeVfy,
  registerDone,
  forgetWarings,
  agentOrdersSection,
  assetSection,
  inProgressItem,
  pendingItem,
  realTimeWaitingOrder,
  recentTransactionsSection,
  userOrdersSection,
  historyPage,
  agreementCheckbox,
  buyForm,
  selectAccountHeader,
  selectAccountItem,
  selectAccountModal,
  sellForm,
  buyAssigned,
  buyCommitted,
  buyConfirmModal,
  cancelSuccess,
  chatBox,
  disputeConfirmModal,
  inPairing,
  messageItem,
  paymentProof,
  sellAssigned,
  sellCommitted,
  sellConfirmModal,
  tradeCompleted,
  tradeExpired,
  tradingLayout,
  uploadPreview,
  tradePage,
  externalTransfer,
  transferCodeStep,
  transferStep,
  internalTransfer,
  transferConfirmModal,
  transferPage,
  walletDetails,
  walletsPage,
  externalOrderPage,
  externalPaymentDetailPage,
} from './pages';
import { options } from './utils';

export default {
  assetOverview,
  breadcrumbComponent,
  chatRecordModal,
  confirmDialog,
  datePicker,
  historyDetailsModal,
  historyItem,
  copyButton,
  qrScanner,
  transactionDetailsModal,
  paginationComponent,

  useCountryCodeOpts,

  dropDownUser,
  menuDrawer,
  ordersDrawer,
  navBar,

  accountItem,
  addAccountModal,
  addBankForm,
  addEPayForm,
  accountsPage,

  loginPage,
  addMailFormStep,
  sendCodeFormStep,
  newPwFormStep,
  pwDoneModal,
  forgetWarings,
  forgetPage,
  registerPage,
  registerBase,
  sendCodeVfy,
  registerDone,

  agentOrdersSection,
  assetSection,
  inProgressItem,
  pendingItem,
  realTimeWaitingOrder,
  recentTransactionsSection,
  userOrdersSection,

  historyPage,

  agreementCheckbox,
  buyForm,
  selectAccountHeader,
  selectAccountItem,
  selectAccountModal,
  sellForm,
  buyAssigned,
  buyCommitted,
  buyConfirmModal,
  cancelSuccess,
  chatBox,
  disputeConfirmModal,
  inPairing,
  messageItem,
  paymentProof,
  sellAssigned,
  sellCommitted,
  sellConfirmModal,
  tradeCompleted,
  tradeExpired,
  tradingLayout,
  uploadPreview,
  tradePage,

  externalTransfer,
  transferCodeStep,
  transferStep,
  internalTransfer,
  transferConfirmModal,
  transferPage,

  walletDetails,
  walletsPage,
  externalOrderPage,
  externalPaymentDetailPage,

  options,
};
