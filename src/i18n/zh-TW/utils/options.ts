export default {
  undefined: '未定義',

  // WalletNetworkEnum
  trc20WalletNetworkEnum: 'TRC20',
  erc20WalletNetworkEnum: 'ERC20',

  // TradeTypeEnum
  buyTradeTypeEnum: '購買',
  sellTradeTypeEnum: '出售',

  // TradeStatusEnum
  pendingTradeStatusEnum: '待配對',
  acceptedTradeStatusEnum: '已接受',
  bankAccountAssignedTradeStatusEnum: '已分配銀行帳戶',
  paymentMadeTradeStatusEnum: '已付款',
  paymentConfirmedTradeStatusEnum: '付款已確認',
  completedTradeStatusEnum: '已完成',
  cancelledTradeStatusEnum: '已取消',
  expiredTradeStatusEnum: '已過期',

  // Trade inProgress Status
  setbankTradeInprogressStatusEnum: '設定銀行',
  goToTradeTradeInprogressStatusEnum: '設定銀行',
  needFinishOrderTradeInprogressStatusEnum: '設定銀行',

  // ChainTransferDirectionEnum
  depositChainTransferDirectionEnum: '充幣',
  withdrawalChainTransferDirectionEnum: '提幣',
  internalChainTransferDirectionEnum: '內部轉帳',

  // ChainTxStatusEnum
  pendingChainTxStatusEnum: '待確認',
  confirmedChainTxStatusEnum: '已確認',
  failedChainTxStatusEnum: '失敗',

  // FlowTypeEnum
  depositFlowTypeEnum: '存款',
  withdrawalFlowTypeEnum: '提款',
  buyFlowTypeEnum: '購買',
  sellFlowTypeEnum: '出售',

  // LedgerSourceTypeEnum
  unknownLedgerSourceTypeEnum: '未知',
  tradeLedgerSourceTypeEnum: '交易',
  chainTxLogLedgerSourceTypeEnum: '鏈上交易紀錄',
  balanceTransferLedgerSourceTypeEnum: '餘額轉帳',
};
