export default {
  undefined: '<PERSON>hông xác định',

  // WalletNetworkEnum
  trc20WalletNetworkEnum: 'TRC20',
  erc20WalletNetworkEnum: 'ERC20',

  // TradeTypeEnum
  buyTradeTypeEnum: 'Mua',
  sellTradeTypeEnum: 'Bán',

  // TradeStatusEnum
  pendingTradeStatusEnum: 'Đang chờ',
  acceptedTradeStatusEnum: 'Đã chấp nhận',
  bankAccountAssignedTradeStatusEnum: 'Đã chọn tài khoản',
  paymentMadeTradeStatusEnum: 'Đã thanh toán',
  paymentConfirmedTradeStatusEnum: 'Đã xác nhận',
  completedTradeStatusEnum: 'Hoàn thành',
  cancelledTradeStatusEnum: 'Đã huỷ',
  expiredTradeStatusEnum: 'Đã hết hạn',

  // Trade inProgress Status
  setbankTradeInprogressStatusEnum: 'Chọn tài khoản',
  goToTradeTradeInprogressStatusEnum: '<PERSON>ia<PERSON> dịch',
  needFinishOrderTradeInprogressStatusEnum: 'Hoàn tất giao dịch',

  // ChainTransferDirectionEnum
  depositChainTransferDirectionEnum: 'Nạp',
  withdrawalChainTransferDirectionEnum: 'Rút',
  internalChainTransferDirectionEnum: 'Nội bộ',

  // ChainTxStatusEnum
  pendingChainTxStatusEnum: 'Đang chờ',
  confirmedChainTxStatusEnum: 'Đã xác nhận',
  failedChainTxStatusEnum: 'Thất bại',

  // FlowTypeEnum
  depositFlowTypeEnum: 'Nạp',
  withdrawalFlowTypeEnum: 'Rút',
  buyFlowTypeEnum: 'Mua',
  sellFlowTypeEnum: 'Bán',

  // LedgerSourceTypeEnum
  unknownLedgerSourceTypeEnum: 'Không rõ',
  tradeLedgerSourceTypeEnum: 'Giao dịch',
  chainTxLogLedgerSourceTypeEnum: 'Nạp/Rút',
  balanceTransferLedgerSourceTypeEnum: 'Chuyển tiền',
};
