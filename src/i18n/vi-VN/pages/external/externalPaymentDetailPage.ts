export default {
  title: '<PERSON> Tiết Thanh Toán',
  currentPrice: '<PERSON><PERSON><PERSON>ệ<PERSON>',
  orderTime: 'Thời Gian Đặt Hàng',
  paymentAmount: 'S<PERSON> Tiền Thanh Toán',
  orderNumber: '<PERSON><PERSON> Đơn Hàng',
  recipientBank: '<PERSON><PERSON> Hàng <PERSON>ờ<PERSON>',
  recipientAccount: '<PERSON><PERSON>à<PERSON>',
  recipientName: 'Tên <PERSON>',
  paymentMethod: 'Phương Thức Thanh Toán',
  paymentNotice: 'Thông Báo Thanh Toán',
  paymentInstructions:
    'Vui lòng hoàn tất thanh toán trong vòng 15 phút, nếu không đơn hàng sẽ tự động bị hủy.',
  confirmPaymentButton: 'Tôi đã hoàn tất thanh toán',
  confirmed: 'Đã Xác Nhận',
  cancelButton: 'Hủy Đơn Hàng',
  customerService: 'Chat với nhân viên',
  online: '<PERSON><PERSON> trự<PERSON> t<PERSON>ế<PERSON>',
  typeMessage: '<PERSON><PERSON> tin nhắn...',
  welcomeMessage: 'Xin chào! Chào mừng bạn đến với dịch vụ khách hàng K28U.',
  helpMessage: 'Tôi có thể giúp gì cho bạn về đơn hàng hôm nay?',
  autoReply:
    'Cảm ơn bạn đã gửi tin nhắn. Bộ phận chăm sóc khách hàng sẽ phản hồi trong giây lát.',
  imageReceived: 'Cảm ơn bạn đã gửi hình ảnh. Chúng tôi sẽ kiểm tra sớm.',
  invalidFileType: 'Vui lòng chọn file hình ảnh (JPG, PNG, GIF).',
  fileTooLarge: 'Kích thước ảnh quá lớn. Tối đa 5MB.',
  imageLoadError: 'Không tải được hình ảnh. Vui lòng thử lại.',
  imageUploadError: 'Không mở được bộ chọn file. Vui lòng thử lại.',
  connectionError: 'Lỗi kết nối. Vui lòng thử lại.',
  messageSendError: 'Gửi tin nhắn thất bại. Vui lòng thử lại.',
  verifying: 'Đang xác minh mã truy cập của bạn...',
  verificationError:
    'Xác minh mã truy cập thất bại. Vui lòng tạo đơn hàng mới để lấy mã truy cập mới.',
  tryAgain: 'Thử lại',
  copiedOrderNumber: 'Đã sao chép số đơn hàng vào bộ nhớ tạm',
  copiedAccountNumber: 'Đã sao chép số tài khoản vào bộ nhớ tạm',
  copyFailed: 'Sao chép vào bộ nhớ tạm thất bại',
  paymentConfirmed:
    'Đã xác nhận thanh toán. Chúng tôi đang xử lý đơn hàng của bạn.',
  paymentError: 'Lỗi khi xác nhận thanh toán. Vui lòng thử lại.',
  orderCompleted: 'Đơn hàng của bạn đã hoàn tất thành công!',
  cancelConfirmTitle: 'Hủy Đơn Hàng',
  cancelConfirmMessage:
    'Bạn có chắc chắn muốn hủy đơn hàng này? Hành động này không thể hoàn tác.',
  timeRemaining: 'Thời Gian Còn Lại',
  orderExpired: 'Đơn hàng đã hết hạn. Vui lòng tạo đơn hàng mới.',
  cryptoAmount: 'Số Lượng Crypto',
  agentFee: 'Phí Đại Lý',
  orderStatus: 'Trạng Thái Đơn Hàng',
  network: 'Mạng',
  tradeType: 'Loại Giao Dịch',
  uploadPaymentProofButton: 'Tải lên Biên Lai Thanh Toán',
  viewPaymentProofButton: 'Xem Biên Lai Thanh Toán',
  paymentProofDialogTitle: 'Biên Lai Thanh Toán',
  paymentProofUploaded: 'Tải biên lai thanh toán thành công',
  paymentProofUploadError:
    'Tải biên lai thanh toán thất bại. Vui lòng thử lại.',
  closeButton: 'Đóng',
  confirmPaymentDialogTitle: 'Xác Nhận Thanh Toán',
  confirmPaymentDialogMessage:
    'Bạn có chắc chắn muốn xác nhận thanh toán? Hành động này không thể hoàn tác.',
  confirmButton: 'Có',
  cancelButtonn: 'Hủy',
  confirmFundsCheckbox:
    'Tôi xác nhận tiền đã có trong tài khoản, tên người thanh toán và số tiền là chính xác.',
  paymentDetailsTitle: 'Chi Tiết Thanh Toán',
  paymentDetailsBankName: 'Tên Ngân Hàng',
  paymentDetailsAccountName: 'Tên Tài Khoản',
  paymentDetailsAccountNumber: 'Số Tài Khoản',
  paymentDetailsBranchCode: 'Mã Chi Nhánh',
  copiedPaymentAccountNumber:
    'Đã sao chép số tài khoản thanh toán vào bộ nhớ tạm',
  waitingForAgentMessage: 'Đang chờ nhân viên gửi chi tiết thanh toán...',
  agentWillSendDetailsSoon: 'Nhân viên sẽ gửi chi tiết sớm',
  transactionCompleted: 'Giao Dịch Đã Hoàn Thành',
  thankYouForPayment: 'Cảm ơn bạn đã thanh toán!',
  transactionNo: 'Số Giao Dịch',
  completedAt: 'Hoàn Thành Lúc',
  amountPaid: 'Số Tiền Đã Thanh Toán',
  cryptoReceived: 'Số Crypto Đã Nhận',
  exchangeRate: 'Tỷ Giá',
  recipientInfo: 'Thông Tin Người Nhận',
  needHelp:
    'Cần hỗ trợ? Liên hệ bộ phận hỗ trợ nếu bạn có thắc mắc về giao dịch này.',
  downloadReceipt: 'Tải Biên Lai',
  transactionDetails: 'Chi Tiết Giao Dịch',
  viewTransactionHistory: 'Xem Lịch Sử Giao Dịch',
  backToDashboard: 'Quay Lại Bảng Điều Khiển',
  yourCrypto: 'Crypto Của Bạn',
  paymentInfo: 'Thông Tin Thanh Toán',
  bankTransferDetails: 'Chi Tiết Chuyển Khoản Ngân Hàng',
  receiverDetails: 'Chi Tiết Người Nhận',
  printReceipt: 'In Biên Lai',
  saveAsPdf: 'Lưu dưới dạng PDF',
  shareReceipt: 'Chia sẻ Biên Lai',
  transactionSummary: 'Tóm Tắt Giao Dịch',
  helpAndSupport: 'Hỗ Trợ & Giúp Đỡ',
  faqTitle: 'Câu Hỏi Thường Gặp',
  supportCenter: 'Trung Tâm Hỗ Trợ',
  chatWithUs: 'Chat với chúng tôi',
  contactPhone: 'Số Điện Thoại Liên Hệ',
  howItWorksTitle: 'Cách Thức Hoạt Động',
  securityGuarantee: 'Bảo Đảm An Toàn',
  newOrder: 'Đơn Hàng Mới',
  viewBlockchainExplorer: 'Xem trên Blockchain Explorer',
  orderDetails: 'Chi Tiết Đơn Hàng',
  paymentDetailsSubtitle:
    'Vui lòng sử dụng các thông tin ngân hàng dưới đây để hoàn tất thanh toán',
  copyText: 'Sao chép vào bộ nhớ tạm',
  copyRecipientAccount: 'Sao chép số tài khoản người nhận',
  copyAccountNumber: 'Sao chép số tài khoản',
  orderOverview: 'Tổng Quan Đơn Hàng',
  paymentStatus: 'Trạng Thái Thanh Toán',
  processingPayment: 'Đang Xử Lý Thanh Toán',
  awaitingPayment: 'Đang Chờ Thanh Toán',
  verifyingPayment: 'Đang Xác Minh Thanh Toán',
  transferDetails: 'Chi Tiết Chuyển Khoản',
  accountInformation: 'Thông Tin Tài Khoản',
  uploaded: 'Đã Tải Lên',
  notesTitle: 'Lưu ý:',
  note1:
    'Nền tảng này chỉ hỗ trợ giao dịch USDT, số lượng thực tế có thể không chính xác hoàn toàn.',
  note2:
    'Nền tảng không thể xác minh danh tính người gửi, vui lòng đảm bảo thông tin chính xác.',
  note3:
    'Nền tảng chỉ xác minh được liệu tiền đã chuyển vào tài khoản chỉ định hay chưa.',
  note4:
    'Vui lòng đảm bảo bạn có thể sử dụng USDT để thanh toán trước khi đặt hàng.',
  note5:
    'Số lượng mua tối thiểu: 100 USDT, các giao dịch nhỏ có thể không được xử lý do tắc nghẽn mạng.',
  note6:
    'Vui lòng tránh các trang web lừa đảo và liên kết độc hại để tránh bị đánh cắp tài khoản.',
  note7:
    'Nếu bạn có bất kỳ thắc mắc nào về đơn hàng, vui lòng liên hệ bộ phận chăm sóc khách hàng qua trang web.',
};
