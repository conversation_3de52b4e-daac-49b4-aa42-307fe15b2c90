import { route } from 'quasar/wrappers';
import { useAuthStore } from 'src/stores/useAuthStore';
import {
  createMemoryHistory,
  createRouter,
  createWebHashHistory,
  createWebHistory,
  Router,
} from 'vue-router';

import routes from './routes';

let routerInstance: Router;

export default route(function (/* { store, ssrContext } */) {
  const createHistory = process.env.SERVER
    ? createMemoryHistory
    : process.env.VUE_ROUTER_MODE === 'history'
    ? createWebHistory
    : createWebHashHistory;

  const Router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,
    history: createHistory(process.env.VUE_ROUTER_BASE),
  });

  routerInstance = Router;

  Router.beforeEach(async (to, _, next) => {
    const auth = useAuthStore();
    await auth.loadFromStorage();

    if (to.meta.requiresAuth && !auth.isLoggedIn) {
      next({ name: 'login' });
    } else if (to.name === 'login' && auth.isLoggedIn) {
      next({ name: 'home' });
    } else {
      next();
    }
  });

  return Router;
});

export { routerInstance as router };
