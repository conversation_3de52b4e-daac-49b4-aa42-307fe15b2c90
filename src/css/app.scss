// Start: Reset CSS
a {
  text-decoration: none;
}

a:visited {
  color: inherit;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type='number'] {
  appearance: textfield;
}

// End: Reset CSS

// background image for home page
.bg {
  position: fixed;
  inset: 0;
  z-index: -10;
  height: 64vh;
  background-image: url('/images/bg.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top;
  background-attachment: scroll;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to bottom, transparent 56px, #ffffff);
  }

  &::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
      to bottom,
      transparent 56px,
      rgba(255, 255, 255, 0.7)
    );
  }
}

// background image for home page in dark mode
body.body--dark .bg {
  position: fixed;
  inset: 0;
  z-index: -10;
  height: 50vh;
  background-image: url('/images/bg.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top;
  background-attachment: scroll;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
      to bottom,
      transparent 56px,
      var(--q-dark-page)
    );
  }

  &::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
      to bottom,
      transparent 56px,
      rgba(33, 30, 54, 0.7)
    );
  }
}

// background gradient for header
.bg-gradient-tr {
  background-image: linear-gradient(
    to right,
    $primary 16%,
    rgba(82, 9, 185, 0.3)
  );
}

// background gradient for sidebar navigation
.bg-gradient-tb {
  background-image: linear-gradient(to bottom, $primary, $secondary);
}

.container {
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  max-width: 50rem;
}

.flex-1 {
  flex-grow: 1;
}

.flex-col {
  flex-direction: column;
}

.gap-xs {
  gap: 0.25rem;
}

.gap-sm {
  gap: 0.5rem;
}

.gap-md {
  gap: 1rem;
}

.gap-lg {
  gap: 1.5rem;
}

.gap-xl {
  gap: 3rem;
}

.gap-2xl {
  gap: 4rem;
}

.gap-x-xs {
  column-gap: 0.25rem;
}

.gap-x-sm {
  column-gap: 0.5rem;
}

.gap-x-md {
  column-gap: 1rem;
}

.gap-x-lg {
  column-gap: 1.5rem;
}

.gap-x-xl {
  column-gap: 3rem;
}

.gap-x-2xl {
  column-gap: 4rem;
}

.gap-y-xs {
  row-gap: 0.25rem;
}

.gap-y-sm {
  row-gap: 0.5rem;
}

.gap-y-md {
  row-gap: 1rem;
}

.gap-y-lg {
  row-gap: 1.5rem;
}

.gap-y-xl {
  row-gap: 3rem;
}

.gap-y-2xl {
  row-gap: 4rem;
}

// private navbar

.on-left {
  margin-right: 0;
}

.k28-loading {
  background-color: rgb(177, 149, 232);

  .k28-loading-box {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .k28-loading-icon {
    width: 60px;
    height: 60px;
    background-image: url('images/logo.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    animation: spin 2s linear infinite;
  }

  .loading-text {
    margin-top: 12px;
    font-size: 18px;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
