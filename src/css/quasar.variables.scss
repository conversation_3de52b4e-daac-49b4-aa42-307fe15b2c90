// Quasar SCSS (& Sass) Variables
// --------------------------------------------------
// To customize the look and feel of this app, you can override
// the Sass/SCSS variables found in Quasar's source Sass/SCSS files.

// Check documentation for full list of Quasar variables

// Your own variables (that are declared here) and Quasar's own
// ones will be available out of the box in your .vue/.scss/.sass files

// It's highly recommended to change the default colors
// to match your app's branding.
// Tip: Use the "Theme Builder" on Quasar's documentation website.

$primary: #250453;
$secondary: #5209b9;
$accent: #7d3ffa;

$dark: #2b2744;
$dark-page: #211e36;

$positive: #a1ffbd;
$negative: #df1f22;
$info: #0256f0;
$warning: #ff816b;

// custom colors
$border: #e5e5e5;
$border-dark: #433a67;

:root {
  --primary-color: #{$primary};
  --secondary-color: #{$secondary};
  --accent-color: #{$accent};

  --dark-color: #{$dark};
  --dark-page-color: #{$dark-page};

  --positive-color: #{$positive};
  --negative-color: #{$negative};
  --info-color: #{$info};
  --warning-color: #{$warning};

  --border-color: #{$border};
}

body.body--dark {
  --border-color: #{$border-dark};
}
