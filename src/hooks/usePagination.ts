import { ref } from 'vue';

const usePagination = (initialPage = 1, initialSize = 10) => {
  const currentPage = ref(initialPage);
  const pageSize = ref(initialSize);

  const setPage = (page: number) => {
    currentPage.value = page;
  };

  const setPageSize = (size: number) => {
    pageSize.value = size;
    currentPage.value = 1;
  };

  return {
    currentPage,
    pageSize,
    setPage,
    setPageSize,
  };
};

export default usePagination;
