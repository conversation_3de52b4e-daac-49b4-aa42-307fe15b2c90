import { computed, Ref } from 'vue';

export default (code: Ref<string | undefined | null | number>) => {
  const mask = computed(() => {
    switch (Number(code.value)) {
      case 886:
        return '#'.repeat(10); // Taiwan
      case 852:
        return '#'.repeat(8); // Hong Kong
      case 81:
        return '#'.repeat(8); // Japan
      case 86:
        return '#'.repeat(11); // China
      case 65:
        return '#'.repeat(8); // Singapore
      case 60:
        return '#'.repeat(10); // Malaysia
      case 82:
        return '#'.repeat(10); // South Korea
      case 66:
        return '#'.repeat(9); // Thailand
      case 1:
        return '#'.repeat(10); // United States
      case 44:
        return '#'.repeat(10); // United Kingdom
      case 84:
        return '#'.repeat(10); // Vietnam
      default:
        return '#'.repeat(10);
    }
  });

  return mask;
};
