import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import api from './api';
import { CountryCodeRes } from './api/useCountryCode';

export type ExtendedCountryCode = CountryCodeRes & {
  label: string;
  value: number;
};

export default () => {
  const { data } = api.useCountryCode();
  const { t } = useI18n();

  const countryCodeOpts = computed<ExtendedCountryCode[]>(() =>
    (data.value || []).map((country) => {
      const value = Number(country.dialCode.replace('+', ''));
      const label = `${t(`useCountryCodeOpts.${country.code}`)} (${
        country.dialCode
      })`;
      return {
        ...country,
        value,
        label,
      };
    })
  );

  return countryCodeOpts;
};
