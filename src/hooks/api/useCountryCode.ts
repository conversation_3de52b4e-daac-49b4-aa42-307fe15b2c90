import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

export type CountryCodeRes = {
  code: string;
  dialCode: string;
  name: string;
  emojiFlag: string;
};
export const useCountryCode = () => {
  const vueRequest = requestProvider<Array<CountryCodeRes>>(
    (props) => {
      const request = axiosProvider
        .get('common/phone-codes', { params: props })
        .then(({ data }) => data);
      return request;
    },
    {
      manual: false,
      cacheKey: 'history',
    },
    {
      noFeedback: true,
    }
  );
  return { ...vueRequest };
};
