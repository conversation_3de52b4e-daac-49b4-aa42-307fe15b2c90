import * as signalR from '@microsoft/signalr';
import { Message, Trade } from 'src/pages/trade/api';
import { Wallet } from 'src/pages/wallets/api';
// prettier-ignore
import { useAuthStore, useChatStore, useOrderStore, useTradeStore, useWalletsStore, } from 'src/stores';
import { useSoundStore } from 'src/stores/sound';
import { ChatMessageType } from 'src/utils/enums';
import { onMounted, onUnmounted, ref } from 'vue';

const url = import.meta.env.VITE_CLIENT_HUBS;
const isDev = import.meta.env.DEV;

export function useClientHub(token: string, manuallyConnect = false) {
  const connection = ref<signalR.HubConnection | null>(null);
  const isConnected = ref(false);
  const soundStore = useSoundStore();
  const auth = useAuthStore();
  const chatStore = useChatStore();
  const orderStore = useOrderStore();
  const tradeStore = useTradeStore();
  const walletsStore = useWalletsStore();

  const startConnection = async (token: string) => {
    if (!token) {
      console.warn('Token is required to connect to client hub.');
      return;
    }

    const hubConnection = new signalR.HubConnectionBuilder()
      .withUrl(url, {
        accessTokenFactory: () => token,
      })
      .withAutomaticReconnect()
      .configureLogging(signalR.LogLevel.Information)
      .build();

    connection.value = hubConnection;

    try {
      await hubConnection.start();
      isConnected.value = true;
      if (isDev) console.log('Connected to client hub');

      // add new events here
      hubConnection.on('BalanceUpdated', (data: Array<Wallet>) => {
        walletsStore.setWallets(data);
      });
      hubConnection.on('TradeUpdated', (data: Trade) => {
        const orderType = orderStore.setOrderRealTime(data);
        tradeStore.setTrade(data);
        orderStore.setOrderRealTime(data);
        if (!auth.isAgent) {
          orderStore.setUserWaitingOrder(data);
        }
        if (auth.isAgent && soundStore.enabled) {
          switch (orderType) {
            case 'pending':
              soundStore.playLoopedSound('sounds/instants5.mp3');
              break;

            case 'inProgress':
              if (soundStore.currentSound === 'sounds/instants5.mp3') {
                soundStore.stopSound();
              }
              soundStore.playSound('sounds/match.mp3');
              break;

            case 'payMade':
              if (soundStore.currentSound === 'sounds/instants5.mp3') {
                soundStore.stopSound();
              }
              soundStore.playSound('sounds/payment2.mp3');
              break;
          }
        }
      });
      hubConnection.on('ReceiveChatMessage', (data: Message) => {
        chatStore.setMessage(data);
      });
    } catch (err) {
      console.error('Connection failed:', err);
      isConnected.value = false;
    }
  };

  chatStore.setSendMessage(
    async (payload: {
      tradeId: number;
      payload: string;
      messageType?: ChatMessageType;
      caption?: string;
      file?: File | null;
    }) => {
      if (
        !connection.value ||
        connection.value.state !== signalR.HubConnectionState.Connected
      ) {
        console.warn('Not connected to hub.');
        return;
      }

      try {
        const {
          tradeId,
          payload: content,
          messageType = ChatMessageType.Text,
          caption,
        } = payload;

        const formData = new FormData();
        formData.append('TradeId', tradeId.toString());
        formData.append('Payload', content);
        formData.append('MessageType', messageType.toString());

        await connection.value.invoke('SendTradeChatMessage', {
          tradeId,
          payload: content,
          messageType,
          caption,
        });
      } catch (error) {
        console.error('Failed to send message:', error);
      }
    }
  );

  onMounted(() => {
    if (manuallyConnect) return;
    startConnection(token);
  });

  onUnmounted(() => {
    if (connection.value) {
      connection.value.stop();
      if (isDev) console.log('Disconnected from client hub');
    }
  });

  return {
    connection,
    isConnected,
    startConnection,
  };
}
