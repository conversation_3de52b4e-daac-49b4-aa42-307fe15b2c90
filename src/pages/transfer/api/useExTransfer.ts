import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

export type ExTransferProps = {
  amount: number;
  to: string;
  code: string;
  note?: string;
};

type ExTransferRes = unknown;

const useExTransfer = ({ ...useProps }: UseProps<ExTransferRes>) => {
  const vueRequest = requestProvider<ExTransferRes, ExTransferProps>(
    (props: ExTransferProps) => {
      const request = axiosProvider
        .post('/chain/tron/usdt/withdraw/confirm', props)
        .then(({ data }) => data);

      return request;
    },
    {
      ...useProps,
      manual: true,
      onSuccess: (res) => {
        if (useProps.onSuccess) useProps.onSuccess(res);
      },
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
  return vueRequest;
};

export { useExTransfer };
