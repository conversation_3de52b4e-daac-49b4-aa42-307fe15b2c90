import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

export type InTransferProps = {
  walletAddress: string;
  amount: number;
  note?: string;
};

type InTransferRes = unknown;

const useInTransfer = ({ ...useProps }: UseProps<InTransferRes>) => {
  const vueRequest = requestProvider<InTransferRes, InTransferProps>(
    (props: InTransferProps) => {
      const request = axiosProvider
        .post('/user/balance/transfer', props)
        .then(({ data }) => data);
      return request;
    },
    {
      ...useProps,
      manual: true,
      onSuccess: (res) => {
        if (useProps.onSuccess) useProps.onSuccess(res);
      },
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
  return vueRequest;
};

export { useInTransfer };
