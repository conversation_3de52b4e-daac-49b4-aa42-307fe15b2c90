import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

export type CodeTransferProps = {
  amount: number;
  to: string;
};

type CodeTransferRes = unknown;

const useCodeTransfer = ({ ...useProps }: UseProps<CodeTransferRes>) => {
  const vueRequest = requestProvider<CodeTransferRes, CodeTransferProps>(
    (props: CodeTransferProps) => {
      const request = axiosProvider
        .post('/chain/tron/usdt/withdraw/send-code', props)
        .then(({ data }) => data);

      return request;
    },
    {
      ...useProps,
      manual: true,
      onSuccess: (res) => {
        if (useProps.onSuccess) useProps.onSuccess(res);
      },
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
  return vueRequest;
};

export { useCodeTransfer };
