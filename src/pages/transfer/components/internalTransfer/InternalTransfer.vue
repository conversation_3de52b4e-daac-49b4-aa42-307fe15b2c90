<template>
  <q-form class="column gap-y-md q-mt-lg" @submit.prevent="goInTransfer">
    <q-card class="column gap-y-sm q-pa-md">
      <div class="text-bold q-mb-sm">
        {{ t('internalTransfer.enterTransDetail') }}
      </div>
      <q-select
        v-model="internalForm.network"
        filled
        :options="networkOptions"
        stack-label
        :label="t('internalTransfer.networkLabel')"
      />
      <q-input
        class="q-mt-lg q-mb-sm"
        v-model="internalForm.address"
        filled
        stack-label
        :label="t('internalTransfer.walletAddressLabel')"
        :placeholder="t('internalTransfer.walletAddressPlaceholder')"
        :rules="[
              (val: string) => val && val.trim() !== '' || t('internalTransfer.walletAddressError')
            ]"
      >
      </q-input>
      <q-input
        v-model="internalForm.amount"
        filled
        stack-label
        inputmode="numeric"
        lang="en"
        :label="t('internalTransfer.amountLabel')"
        :placeholder="t('internalTransfer.amountPlaceholder')"
        @focus="
          () => {
            if (!internalForm.amount) internalForm.amount = '0';
          }
        "
        @update:model-value="
    (val: string | number | null) => {
      internalForm.amount = thousandInput(sanitizeAmount(String(val ?? '')));
    }
  "
        :rules="[
    (val: string) => !val || numberTool(val) > 0 || t('internalTransfer.amountError'),
    (val: string) => !val || numberTool(val) <= walletsStore.availableAmount || t('internalTransfer.exceedsAvailableBalance'),
  ]"
        @blur="
          () => {
            if (!/^[0-9,.]+$/.test(internalForm.amount)) {
              internalForm.amount = '';
            }
          }
        "
      >
        <template v-slot:append>
          <div class="text-grey text-subtitle2">{{ DEFAULT_CRYPTO }}</div>
        </template>
      </q-input>
      <div class="flex gap-x-xs text-bold q-mt-sm">
        <span class="text-grey"
          >{{ t('internalTransfer.availableBalance') }}:</span
        >
        <span class="text-info"
          >{{ thousandTool(walletsStore.availableAmount, DEFAULT_CRYPTO) }}
          {{ DEFAULT_CRYPTO }}</span
        >
      </div>
    </q-card>

    <q-input
      class="q-mt-sm q-mb-sm"
      v-model="internalForm.note"
      filled
      stack-label
      :label="t('internalTransfer.remarkLabel')"
      :placeholder="t('internalTransfer.remarkPlaceholder')"
    />
    <agreement-checkbox v-model="internalForm.agreement" />
    <q-btn
      :disable="!isValidToTransfer"
      type="submit"
      color="accent"
      :label="t('internalTransfer.submit')"
      class="q-py-sm"
    />
  </q-form>
  <TransferConfirmModal
    mode="internal"
    v-model="showInTransferModal"
    :network="internalForm.network.value"
    :address="internalForm.address"
    :amount="numberTool(internalForm.amount)"
    :note="internalForm.note"
    @confirm="handleInTransfer"
    :transferLoad="inTransLoad"
  />
</template>

<script setup lang="ts">
import AgreementCheckbox from 'src/pages/trade/CreateComponents/AgreementCheckbox.vue';
import { useWalletsStore } from 'src/stores';
import { DEFAULT_CRYPTO } from 'src/utils/constants';
import { WalletNetworkEnum } from 'src/utils/enums';
import { numberTool, thousandInput, thousandTool } from 'src/utils/NumberTool';
import { walletNetworkOptions } from 'src/utils/options';
import { computed, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import api from '../../api';
import hooks from 'src/hooks';
import TransferConfirmModal from '../TransferConfirmModal.vue';
import type { Network } from '../externalTransfer/types';

interface InternalTransfer {
  network: Network;
  address: string;
  note?: string;
  amount: string;
  agreement: boolean;
}

const { t } = useI18n();
const walletsStore = useWalletsStore();
const showInTransferModal = ref(false);

const internalForm = reactive<InternalTransfer>({
  network: walletNetworkOptions.map((options) => ({
    ...options,
    label: t('options.' + (options.label || undefined)),
  }))[0],
  address: '',
  note: '',
  amount: '0',
  agreement: false,
});
const networkOptions = computed(() =>
  walletNetworkOptions.map((option) => ({
    ...option,
    label: t('options.' + option.label),
    disable: option.value === WalletNetworkEnum.ERC20,
  }))
);

// vue request

const { run: inTrans, loading: inTransLoad } = api.useInTransfer({
  onSuccess: () => {
    hooks.useSuccessNotify(`${t('internalTransfer.transSuccess')}`);
    resetInTransferForm();
  },
});

// Sanitize input
const sanitizeAmount = (val: string) => {
  return val.replace(/[^\d.,]/g, '');
};

const isValidToTransfer = computed(() => {
  if (!internalForm.network) return false;
  if (!internalForm.amount) return false;
  if (!internalForm.agreement) return false;
  if (!internalForm.address?.trim()) return false;

  const amountNumber = numberTool(internalForm.amount);
  if (amountNumber <= 0) return false;
  if (amountNumber > walletsStore.availableAmount) return false;

  return true;
});

// handle submit  transfer
const goInTransfer = () => {
  showInTransferModal.value = true;
};
const handleInTransfer = () => {
  inTrans({
    walletAddress: internalForm.address,
    amount: numberTool(internalForm.amount),
    note: internalForm.note,
  });
};

// form reset after Success
const resetInTransferForm = () => {
  Object.assign(internalForm, {
    network: networkOptions.value[0],
    note: '',
    amount: '',
    agreement: false,
  });
  showInTransferModal.value = false;
};
</script>
