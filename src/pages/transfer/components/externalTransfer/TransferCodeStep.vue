<template>
  <div class="column gap-y-md">
    <q-select
      v-model="codeStepForm.network"
      filled
      :options="networkOptions"
      stack-label
      :label="t('transferCodeStep.networkLabel')"
    />
    <q-input
      v-model="codeStepForm.address"
      filled
      stack-label
      :label="t('transferCodeStep.walletAddressLabel')"
      :placeholder="t('transferCodeStep.walletAddressPlaceholder')"
      :rules="[
        (val: string) => val?.trim() !== '' || t('transferCodeStep.walletAddressError'),
      ]"
    />
    <div>
      <q-input
        v-model="codeStepForm.amount"
        filled
        stack-label
        inputmode="numeric"
        lang="en"
        :label="t('transferCodeStep.amountLabel')"
        :placeholder="t('transferCodeStep.amountPlaceholder')"
        @focus="
          () => {
            if (numberTool(form.amount) < 1) codeStepForm.amount = '0';
          }
        "
        @update:model-value="
          (val: string | number | null) =>
            (codeStepForm.amount = thousandInput(
              sanitizeAmount(String(val ?? ''))
            ))
        "
        :rules="[
          (val: string) => numberTool(val) > 0 || t('transferCodeStep.amountError'),
          (val: string) =>
            numberTool(val) <= walletsStore.availableAmount ||
            t('transferCodeStep.exceedsAvailableBalance'),
        ]"
        @blur="
          () => {
            if (!/^[0-9,.]+$/.test(form.amount)) codeStepForm.amount = '0';
          }
        "
      >
        <template v-slot:append>
          <div class="text-grey text-subtitle2">{{ DEFAULT_CRYPTO }}</div>
        </template>
      </q-input>
      <div class="flex justify-between">
        <div class="flex gap-x-xs text-bold q-mt-sm">
          <span class="text-grey"
            >{{ t('transferCodeStep.availableBalance') }}:</span
          >
          <span class="text-info">
            {{ thousandTool(walletsStore.availableAmount) }}
            {{ DEFAULT_CRYPTO }}
          </span>
        </div>
        <div class="flex gap-x-xs text-bold q-mt-sm">
          <span class="text-grey">{{ t('transferCodeStep.fee') }}:</span>
          <span class="text-grey">
            {{ thousandTool(2, 'USDT') }}
            {{ DEFAULT_CRYPTO }}
          </span>
        </div>
      </div>
    </div>
    <q-btn
      type="button"
      color="accent"
      class="q-mt-md full-width"
      :label="t('transferCodeStep.getCodeTransfer')"
      :disable="!isValidCodeForm"
      @click="$emit('update:form', { ...codeStepForm })"
      :loading="codeLoad"
    />
  </div>
</template>

<script setup lang="ts">
import { useWalletsStore } from 'src/stores';
import { DEFAULT_CRYPTO } from 'src/utils/constants';
import { WalletNetworkEnum } from 'src/utils/enums';
import { numberTool, thousandInput, thousandTool } from 'src/utils/NumberTool';
import { walletNetworkOptions } from 'src/utils/options';
import { computed, reactive } from 'vue';
import { useI18n } from 'vue-i18n';
import { ExTransferFormType } from './types';

const { t } = useI18n();
const props = defineProps<{ form: ExTransferFormType; codeLoad: boolean }>();
defineEmits<{
  (e: 'update:form', val: ExTransferFormType): void;
  (e: 'open-scanner'): void;
}>();
const walletsStore = useWalletsStore();

// Form data
const codeStepForm = reactive({ ...props.form });

// Options
const networkOptions = computed(() =>
  walletNetworkOptions.map((option) => ({
    ...option,
    label: t('options.' + option.label),
    disable: option.value === WalletNetworkEnum.ERC20,
  }))
);

// Sanitize numeric input
const sanitizeAmount = (val: string) => {
  return val.replace(/[^\d.,]/g, '');
};

// Form validation
const isValidCodeForm = computed(() => {
  if (!codeStepForm.network) return false;
  if (!codeStepForm.address?.trim()) return false;

  const amountNumber = numberTool(codeStepForm.amount);
  if (amountNumber <= 0) return false;
  if (amountNumber > walletsStore.availableAmount) return false;

  return true;
});
</script>
