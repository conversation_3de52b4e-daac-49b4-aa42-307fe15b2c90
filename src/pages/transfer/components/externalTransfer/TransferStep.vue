<template>
  <div class="column gap-y-md">
    <q-input
      v-model="formTransfer.address"
      filled
      disable
      stack-label
      :label="t('transferStep.walletAddressLabel')"
    />
    <q-input
      v-model="formTransfer.amount"
      filled
      disable
      stack-label
      :label="t('transferStep.amountLabel')"
    />
    <q-input
      class="q-mt-sm q-mb-sm"
      v-model="formTransfer.note"
      filled
      stack-label
      :label="t('transferStep.remarkLabel')"
      :placeholder="t('transferStep.remarkPlaceholder')"
    />
    <q-card class="q-pa-md">
      <div class="text-bold q-mb-sm">
        {{ t('transferStep.enterCodeTit') }}
      </div>
      <q-input
        v-model="formTransfer.codeTransfer"
        filled
        stack-label
        :label="t('transferStep.transferCodeLabel')"
        :placeholder="t('transferStep.placeHolderCode')"
        :rules="[
          (val: string) => /^\d*$/.test(val) || t('transferStep.codeRule1'),
          (val: string) => !!val || t('transferStep.codeRule1'),
        ]"
        @keypress="onKeyPress"
      />
    </q-card>
    <AgreementCheckbox v-model="formTransfer.agreement" />
    <q-btn
      type="button"
      color="accent full-width"
      :label="t('transferStep.submit')"
      :disable="!canSubmit"
      @click="goExTransfer"
    />
    <div
      :class="[
        q.screen.lt.sm
          ? 'text-center q-pa-md q-gutter-y-lg'
          : 'row items-center justify-between q-mt-md q-pa-lg',
      ]"
    >
      <q-btn
        :label="t('transferStep.editIF')"
        @click="() => $emit('editInfo')"
      />
      <q-btn
        :loading="reCodeLoad"
        :uppercase="false"
        flat
        :disable="isCounting"
        :label="
          isCounting
            ? `${t('transferStep.plseWait')} ${countDown} ${t(
                'transferStep.second'
              )}`
            : `${t('transferStep.noCode')}`
        "
        @click="codeTransferResend"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import { devEnv } from 'src/router/routes';
import AgreementCheckbox from 'src/pages/trade/CreateComponents/AgreementCheckbox.vue';
import { ExTransferFormType } from './types';

const q = useQuasar();
const props = defineProps<{ form: ExTransferFormType; reCodeLoad: boolean }>();
const formTransfer = props.form;
const emit = defineEmits<{
  (e: 'editInfo'): void;
  (e: 'update:codeTransfer', value: string): void;
  (e: 'resendTransferCode', value: ExTransferFormType): void;
}>();

const { t } = useI18n();
const resendWait = devEnv ? 25 : 45;
const countDown = ref(resendWait);
const isCounting = computed(() => countDown.value > 0);
let timer: number | null = null;

const canSubmit = computed(
  () =>
    formTransfer.agreement && (formTransfer.codeTransfer ?? '').trim() !== ''
);

const onKeyPress = (event: KeyboardEvent) => {
  const allowedKeys = ['Backspace', 'ArrowLeft', 'ArrowRight', 'Tab'];
  if (!/[0-9]/.test(event.key) && !allowedKeys.includes(event.key)) {
    event.preventDefault();
  }
};

const goExTransfer = () => {
  emit('update:codeTransfer', formTransfer.codeTransfer || '');
};

// waiting for resend
const waitToResend = () => {
  countDown.value = resendWait;
  timer && clearInterval(timer);
  timer = window.setInterval(() => {
    if (countDown.value <= 1) {
      if (timer !== null) clearInterval(timer);
      timer = null;
      countDown.value = 0;
    } else {
      countDown.value--;
    }
  }, 1000);
};

// set wait
onMounted(() => waitToResend());
const codeTransferResend = () => {
  emit('resendTransferCode', formTransfer);
  waitToResend();
};
</script>
