<template>
  <confirm-dialog
    :loading="transferLoad"
    v-model="model"
    @confirm="onConfirm"
    type="warning"
    :title="t('transferConfirmModal.title')"
    :message="t('transferConfirmModal.message')"
  >
    <div class="wrapper column gap-y-sm">
      <div class="flex items-center justify-between">
        <span class="text-bold text-grey">{{
          t('transferConfirmModal.agreementType')
        }}</span>
        <span>{{ DEFAULT_CRYPTO }} - {{ networkLabel }}</span>
      </div>
      <div class="flex items-center justify-between">
        <span class="text-bold text-grey">{{
          t('transferConfirmModal.walletAddress')
        }}</span>
        <span>{{ address }}</span>
      </div>

      <div class="flex items-center justify-between">
        <span class="text-bold text-grey">{{
          t('transferConfirmModal.amount')
        }}</span>
        <span class="text-bold text-info"
          >{{ thousandTool(amount, DEFAULT_CRYPTO) }} {{ DEFAULT_CRYPTO }}</span
        >
      </div>
      <div class="flex items-center justify-between">
        <span class="text-bold text-grey">
          {{ t('transferConfirmModal.remark') }}</span
        >
        <span class="text-bold text-accent">{{ note || '------' }}</span>
      </div>
    </div>
  </confirm-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { DEFAULT_CRYPTO } from 'src/utils/constants';
import { WalletNetworkEnum } from 'src/utils/enums';
import { thousandTool } from 'src/utils/NumberTool';
import { walletNetworkOptions } from 'src/utils/options';
import ConfirmDialog from 'src/components/ConfirmDialog.vue';

const props = defineProps<{
  modelValue: boolean;
  network: WalletNetworkEnum;
  address: string;
  amount: number;
  transferLoad: boolean;
  note?: string;
  mode?: 'external' | 'internal';
}>();
const emit = defineEmits(['update:modelValue', 'confirm']);
const model = computed({
  get: () => props.modelValue,
  set: (val: boolean) => emit('update:modelValue', val),
});

const onConfirm = () => {
  emit('confirm');
  model.value = false;
};

const { t } = useI18n();

const networkLabel = computed(() =>
  t(
    'options.' +
      walletNetworkOptions.find((option) => option.value === props.network)
        ?.label || 'undefined'
  )
);
</script>

<style scoped>
.wrapper {
  width: 40rem;
  max-width: 100%;
}
</style>
