<template>
  <q-form class="column gap-y-sm" @submit="handleAdd" :loading="adding">
    <div>
      <label class="label q-mb-xs block">{{
        t('addBankForm.bankNameLabel')
      }}</label>
      <q-input
        v-model="form.bankName"
        filled
        hide-bottom-space
        :placeholder="t('addBankForm.bankNamePlaceholder')"
        :rules="[(val: string) => !!val || t('addBankForm.bankNameError')]"
      />
    </div>
    <div>
      <label class="label q-mb-xs block">{{
        t('addBankForm.currencyLabel')
      }}</label>
      <q-select
        filled
        v-model="form.currency"
        hide-bottom-space
        :options="currencyOptions"
        :loading="loadingCurrencyCodes"
        :label="t('addBankForm.currencyPlaceholder')"
        :rules="[(val: string) => !!val || t('addBankForm.currencyError')]"
      />
    </div>
    <div>
      <label class="label q-mb-xs block">{{
        t('addBankForm.accountNameLabel')
      }}</label>
      <q-input
        v-model="form.accountName"
        filled
        hide-bottom-space
        :placeholder="t('addBankForm.accountNamePlaceholder')"
        :rules="[(val: string) => !!val || t('addBankForm.accountNameError')]"
      />
    </div>
    <div>
      <label class="label q-mb-xs block">{{
        t('addBankForm.accountNumberLabel')
      }}</label>
      <q-input
        v-model="form.accountNumber"
        filled
        hide-bottom-space
        :placeholder="t('addBankForm.accountNumberPlaceholder')"
        :rules="[(val: string) => !!val || t('addBankForm.accountNumberError')]"
      />
    </div>
    <div>
      <label class="label q-mb-xs block">{{
        t('addBankForm.branchCodeLabel')
      }}</label>
      <q-input
        v-model="form.branchCode"
        filled
        hide-bottom-space
        :placeholder="t('addBankForm.branchCodePlaceholder')"
        :rules="[(val: string) => !!val || t('addBankForm.branchCodeError')]"
      />
    </div>

    <q-btn
      type="submit"
      color="accent"
      :label="t('addBankForm.add')"
      class="q-mt-sm q-py-sm"
    />
  </q-form>
</template>

<script setup lang="ts">
import { CurrencyCode, useCurrencyCodes } from 'src/api';
import hooks from 'src/hooks';
import { computed, reactive } from 'vue';
import { useI18n } from 'vue-i18n';
import { useAddBankAccount } from '../api';

const { refetch, handleClose } = defineProps<{
  refetch: () => void;
  handleClose: () => void;
}>();

const { t } = useI18n();
const { data: currencyCodes, loading: loadingCurrencyCodes } =
  useCurrencyCodes();
const { run: add, loading: adding } = useAddBankAccount({
  onSuccess: () => {
    hooks.useSuccessNotify(t('addBankForm.addSuccessMessage'));
    refetch();
    handleClose();
  },
});

const form = reactive<{
  bankName: string;
  currency: CurrencyCode['code'];
  accountName: string;
  accountNumber: string;
  branchCode: string;
}>({
  bankName: '',
  currency: '',
  accountName: '',
  accountNumber: '',
  branchCode: '',
});

const currencyOptions = computed(() =>
  currencyCodes.value?.map((currencyCode) => currencyCode.code)
);

const handleAdd = () => {
  add({
    bankName: form.bankName,
    currency: form.currency,
    accountName: form.accountName,
    accountNumber: form.accountNumber,
    branchCode: form.branchCode,
  });
};
</script>

<style scoped>
.label {
  font-size: 1rem;
}
</style>
