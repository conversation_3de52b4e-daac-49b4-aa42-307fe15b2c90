<template>
  <div class="wrapper flex items-start">
    <q-icon name="account_balance" color="info" class="icon q-ml-xs q-mr-md" />
    <div class="content">
      <span class="badge bg-accent text-white">{{ account.currency }}</span>
      <div class="flex items-center justify-between">
        <div class="flex gap-x-sm">
          <div class="column">
            <span
              :class="[
                'name text-bold',
                account.isDefault ? 'text-accent' : '',
              ]"
              >{{ t('accountItem.bankNameLabel') }}:</span
            >
            <span class="text-bold text-grey"
              >{{ t('accountItem.accountNameLabel') }}:</span
            >
            <span class="text-bold text-grey"
              >{{ t('accountItem.accountNumberLabel') }}:</span
            >
            <span class="text-bold text-grey"
              >{{ t('accountItem.branchCodeLabel') }}:</span
            >
          </div>
          <div class="column">
            <span
              :class="[
                'name text-bold',
                account.isDefault ? 'text-accent' : '',
              ]"
              >{{ account.bankName }}</span
            >
            <span class="text-grey">{{ account.accountName }}</span>
            <span class="text-grey">{{ account.accountNumber }}</span>
            <span class="text-grey">{{ account.branchCode }}</span>
          </div>
        </div>
        <div class="flex-center gap-x-sm">
          <q-btn
            flat
            round
            :icon="account.isDefault ? 'star' : 'star_outline'"
            :disable="settingDefault || account.isDefault"
            class="btn-favorite"
            @click="handleShowConfirm('setDefault')"
          >
            <q-tooltip
              v-if="!account.isDefault"
              anchor="bottom middle"
              self="bottom middle"
            >
              {{ t('accountItem.setDefaultTooltip') }}
            </q-tooltip>
          </q-btn>
          <q-btn
            flat
            round
            icon="delete"
            color="negative"
            :disable="deleting"
            @click="handleShowConfirm('delete')"
          >
            <q-tooltip anchor="bottom middle" self="bottom middle">
              {{ t('accountItem.deleteTooltip') }}
            </q-tooltip>
          </q-btn>
        </div>
      </div>
    </div>
  </div>

  <confirm-dialog
    v-model="showConfirm"
    type="warning"
    :title="confirmTitle"
    :message="confirmMessage"
    :onConfirm="confirmCallback"
  >
    <div class="flex gap-x-sm">
      <div class="column">
        <span class="name text-bold"
          >{{ t('accountItem.bankNameLabel') }}:</span
        >
        <span class="text-bold text-grey"
          >{{ t('accountItem.accountNameLabel') }}:</span
        >
        <span class="text-bold text-grey"
          >{{ t('accountItem.accountNumberLabel') }}:</span
        >
        <span class="text-bold text-grey"
          >{{ t('accountItem.branchCodeLabel') }}:</span
        >
      </div>
      <div class="column">
        <span class="name text-bold">{{ account.bankName }}</span>
        <span class="text-grey">{{ account.accountName }}</span>
        <span class="text-grey">{{ account.accountNumber }}</span>
        <span class="text-grey">{{ account.branchCode }}</span>
      </div>
    </div>
  </confirm-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import hooks from 'src/hooks';
import { BankAccount } from '../api/useBankAccounts';
import { useSetDefaultBankAccount, useDeleteBankAccount } from '../api';
import ConfirmDialog from 'src/components/ConfirmDialog.vue';

const { account, refetch } = defineProps<{
  account: BankAccount;
  refetch: () => void;
}>();

const { t } = useI18n();
const { run: setDefaultBankAccount, loading: settingDefault } =
  useSetDefaultBankAccount({
    onSuccess: () => {
      hooks.useSuccessNotify(t('accountItem.setDefaultSuccessMessage'));
      refetch();
    },
  });
const { run: deleteBankAccount, loading: deleting } = useDeleteBankAccount({
  onSuccess: () => {
    hooks.useSuccessNotify(t('accountItem.deleteSuccessMessage'));
    refetch();
  },
});

const showConfirm = ref<boolean>(false);
const confirmTitle = ref<string>('');
const confirmMessage = ref<string>('');
const confirmCallback = ref<() => void>();

const handleShowConfirm = (action: 'setDefault' | 'delete') => {
  switch (action) {
    case 'setDefault':
      confirmTitle.value = t('accountItem.setDefaultConfirmTitle');
      confirmMessage.value = t('accountItem.setDefaultConfirmMessage');
      confirmCallback.value = handleSetDefault;
      break;
    case 'delete':
      confirmTitle.value = t('accountItem.deleteConfirmTitle');
      confirmMessage.value = t('accountItem.deleteConfirmMessage');
      confirmCallback.value = handleDelete;
      break;
  }
  showConfirm.value = true;
};
const handleSetDefault = () => {
  setDefaultBankAccount({ id: account.id });
};
const handleDelete = () => {
  deleteBankAccount({ id: account.id });
};
</script>

<style scoped>
.wrapper {
  width: 100%;
  border-bottom: 1px solid var(--border-color);
}
.wrapper:not(:last-child) {
  padding-bottom: 1rem;
}

.icon {
  font-size: 1.5rem;
}

.content {
  flex: 1;
  position: relative;
}

.name {
  font-size: 1rem;
}

.btn-favorite {
  color: #ffc300;
}

.badge {
  position: absolute;
  top: -0.5rem;
  right: -2rem;
  padding: 0.25rem 1.25rem;
  &::before {
    content: '';
    position: absolute;
    top: 100%;
    right: 0;
    border-top: 6px solid #0437a4;
    border-left: 8px solid #0437a4;
    border-right: 8px solid transparent;
    border-bottom: 6px solid transparent;
  }
}
@media screen and (max-width: 599px) {
  .badge {
    right: -1.5rem;
    &::before {
      border-top: 3px solid #0437a4;
      border-left: 4px solid #0437a4;
      border-right: 4px solid transparent;
      border-bottom: 3px solid transparent;
    }
  }
}
</style>
