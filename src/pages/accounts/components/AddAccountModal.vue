<template>
  <q-dialog v-model="model">
    <q-card class="wrapper q-pa-md">
      <div class="flex items-center justify-between q-mb-md">
        <span class="title text-bold">{{ t('addAccountModal.title') }}</span>
        <q-btn flat round icon="close" color="grey" @click="handleClose" />
      </div>

      <span class="label q-mb-xs block">{{
        t('addAccountModal.paymentMethodsLabel')
      }}</span>
      <q-tabs
        v-model="tab"
        active-color="accent"
        indicator-color="transparent"
        class="q-mb-lg"
        inline-label
      >
        <div class="tabs-wrapper flex">
          <q-tab name="bank" :class="['tab', tab === 'bank' ? 'active' : '']">
            <q-icon
              name="account_balance"
              color="info"
              class="text-h5 q-mr-sm"
            />
            <span
              :class="[
                'label',
                themeStore.isDark ? 'text-white' : 'text-black',
              ]"
              >{{ t('addAccountModal.bank') }}</span
            >
          </q-tab>
          <q-tab
            name="e-pay"
            :class="['tab', tab === 'e-pay' ? 'active' : '']"
            disable
          >
            <q-icon name="qr_code" color="info" class="text-h5 q-mr-sm" />
            <span
              :class="[
                'label',
                themeStore.isDark ? 'text-white' : 'text-black',
              ]"
              >{{ t('addAccountModal.ePay') }}</span
            >
          </q-tab>
        </div>
      </q-tabs>

      <q-tab-panels v-model="tab" animated>
        <q-tab-panel name="bank" class="q-pa-none">
          <add-bank-form :refetch="refetch" :handleClose="handleClose" />
        </q-tab-panel>
        <q-tab-panel name="e-pay" class="q-pa-none">
          <add-e-pay-form />
        </q-tab-panel>
      </q-tab-panels>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, type Ref, ref } from 'vue';
import { useThemeStore } from 'src/stores';
import AddBankForm from './AddBankForm.vue';
import AddEPayForm from './AddEPayForm.vue';
import { useI18n } from 'vue-i18n';

const { modelValue, refetch } = defineProps<{
  modelValue: boolean;
  refetch: () => void;
}>();
const { t } = useI18n();

const emit = defineEmits(['update:modelValue']);

const themeStore = useThemeStore();

const tab: Ref<'bank' | 'e-pay'> = ref('bank');

const model = computed({
  get: () => modelValue,
  set: (val: boolean) => emit('update:modelValue', val),
});

const handleClose = () => {
  emit('update:modelValue', false);
};
</script>

<style scoped>
.wrapper {
  width: 25rem;
}

.title {
  font-size: 1.5rem;
}

.label {
  font-size: 1rem;
}

.tabs-wrapper {
  width: 100%;
  gap: 0.5rem;
}

.tab {
  flex: 1;
  color: var(--border-color);
  border: 1px solid;
  border-color: currentColor;
  border-radius: 6px;
  min-height: 3.75rem;
}

.active {
  color: #7d3ffa;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    border-top: 8px solid currentColor;
    border-right: 8px solid currentColor;
    border-left: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-top-right-radius: 4px;
  }
}
</style>
