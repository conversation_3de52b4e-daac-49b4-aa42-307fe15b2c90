<template>
  <q-form class="column gap-y-sm q-mb-lg">
    <div>
      <label class="label q-mb-xs block">{{
        t('addEPayForm.paymentGatewayLabel')
      }}</label>
      <q-input
        v-model="paymentGateway"
        filled
        :placeholder="t('addEPayForm.paymentGatewayPlaceholder')"
      />
    </div>
    <div>
      <label class="label q-mb-xs block">{{
        t('addEPayForm.nameLabel')
      }}</label>
      <q-input
        v-model="name"
        filled
        :placeholder="t('addEPayForm.namePlaceholder')"
      />
    </div>
    <div>
      <label class="label q-mb-xs block">{{
        t('addEPayForm.qrCodeLabel')
      }}</label>
      <q-file
        v-model="file"
        accept="image/*"
        :label="t('addEPayForm.qrCodePlaceholder')"
        filled
        @update:model-value="onFileSelected"
      />
    </div>
  </q-form>

  <!-- This section should be placed outside so the Image Preview is not covered by the Add button -->
  <div>
    <q-img
      v-if="imageUrl"
      :src="imageUrl"
      class="image q-mb-lg"
      no-spinner
      no-transition
    />
    <q-btn
      color="accent"
      :label="t('addEPayForm.add')"
      class="btn-submit q-py-sm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

const paymentGateway = ref('');
const name = ref('');
const file = ref<File | null>(null);
const imageUrl = ref<string | null>(null);
const { t } = useI18n();

const onFileSelected = (fileObj: File | null) => {
  if (!fileObj) {
    imageUrl.value = null;
    return;
  }

  const reader = new FileReader();
  reader.onload = (e: ProgressEvent<FileReader>) => {
    if (e.target?.result) {
      imageUrl.value = e.target.result as string;
    }
  };
  reader.readAsDataURL(fileObj);
};
</script>

<style scoped>
.label {
  font-size: 1rem;
}

.image {
  max-width: 100%;
}

.btn-submit {
  width: 100%;
}
</style>
