<template>
  <q-page padding class="container q-py-lg flex items-center column">
    <breadcrumb-component
      :label="t('accountsPage.title')"
      class="self-start q-mb-md"
    />

    <section class="flex column gap-y-md full-width">
      <q-card class="q-pa-md gap-y-sm flex column">
        <div class="flex gap-sm">
          <q-input
            dense
            filled
            clearable
            debounce="800"
            v-model="search.bankName"
            :label="t('accountsPage.bankNameLabel')"
            :class="q.screen.lt.md ? 'full-width' : 'flex-1'"
          >
            <template v-slot:append>
              <q-icon name="search" />
            </template>
          </q-input>
          <q-input
            dense
            filled
            clearable
            debounce="800"
            v-model="search.accountName"
            :label="t('accountsPage.accountNameLabel')"
            :class="q.screen.lt.md ? 'full-width' : 'flex-1'"
          >
            <template v-slot:append>
              <q-icon name="search" />
            </template>
          </q-input>
          <q-input
            dense
            filled
            clearable
            debounce="800"
            v-model="search.accountNumber"
            :label="t('accountsPage.accountNumberLabel')"
            :class="q.screen.lt.md ? 'full-width' : 'flex-1'"
          >
            <template v-slot:append>
              <q-icon name="search" />
            </template>
          </q-input>
          <q-select
            dense
            filled
            clearable
            v-model="search.currency"
            :options="currencyOptions"
            :label="t('accountsPage.currencyLabel')"
            :disable="loadingCurrencyCodes"
            class="flex-1"
          />
        </div>
        <div class="flex items-center justify-between">
          <q-checkbox
            toggle-order="tf"
            toggle-indeterminate
            color="accent"
            v-model="search.isDefault"
            :label="t('accountsPage.isDefaultLabel')"
            :disable="loadingBankAccounts"
            class="default-checkbox"
          />
          <q-btn
            rounded
            color="accent"
            size="sm"
            icon="add"
            :label="t('accountsPage.addAccount')"
            class="q-py-sm"
            @click="handleOpenModal"
          />
        </div>
      </q-card>

      <pagination-component
        v-model="currentPage"
        :page-size="pageSize"
        :total-pages="bankAccounts?.totalPages || 1"
        :total-count="bankAccounts?.totalCount"
        :loading="loadingBankAccounts"
        @update:modelValue="setPage"
        @update:pageSize="setPageSize"
      />

      <q-card class="column flex items-center gap-y-lg q-pa-md q-py-lg">
        <loading-component v-if="loadingBankAccounts" />
        <div
          v-else-if="bankAccounts?.items.length === 0"
          class="text-bold text-grey"
        >
          {{ t('accountsPage.emptyList') }}
        </div>
        <account-item
          v-else
          v-for="bankAccount in bankAccounts?.items"
          :key="bankAccount.id"
          :account="bankAccount"
          :refetch="refresh"
        />
      </q-card>
    </section>

    <add-account-modal v-model="isOpenModal" :refetch="refresh" />
  </q-page>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { reactive, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { useBankAccounts } from './api';
import usePagination from 'src/hooks/usePagination';
import AccountItem from './components/AccountItem.vue';
import PaginationComponent from 'src/components/PaginationComponent.vue';
import BreadcrumbComponent from 'src/components/BreadcrumbComponent.vue';
import AddAccountModal from './components/AddAccountModal.vue';
import { CurrencyCode, useCurrencyCodes } from 'src/api';
import LoadingComponent from 'src/components/LoadingComponent.vue';

const q = useQuasar();
const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const { currentPage, pageSize, setPage, setPageSize } = usePagination(
  route.query.currentPage ? Number(route.query.currentPage) : 1,
  route.query.pageSize ? Number(route.query.pageSize) : 5
);
const { data: currencyCodes, loading: loadingCurrencyCodes } =
  useCurrencyCodes();
const {
  data: bankAccounts,
  loading: loadingBankAccounts,
  run,
  refresh,
} = useBankAccounts({
  pageNumber: currentPage.value,
  pageSize: pageSize.value,
});

const isOpenModal = ref(false);

const search = reactive<{
  bankName: string;
  accountName: string;
  accountNumber: string;
  currency: CurrencyCode['code'];
  isDefault: boolean | null;
}>({
  bankName: (route.query.bankName as string) || '',
  accountName: (route.query.accountName as string) || '',
  accountNumber: (route.query.accountNumber as string) || '',
  currency: (route.query.currency as string) || '',
  isDefault:
    route.query.isDefault !== undefined
      ? route.query.isDefault === 'true'
      : null,
});

const currencyOptions = computed(() =>
  currencyCodes.value?.map((currencyCode) => currencyCode.code)
);

const handleOpenModal = () => {
  isOpenModal.value = true;
};

watch(
  [currentPage, pageSize, search],
  () => {
    run({
      pageNumber: currentPage.value,
      pageSize: pageSize.value,
      bankName: search.bankName,
      accountName: search.accountName,
      accountNumber: search.accountNumber,
      currency: search.currency,
      isDefault: search.isDefault,
    });
    router.replace({
      query: {
        ...route.query,
        bankName: search.bankName || undefined,
        accountName: search.accountName || undefined,
        accountNumber: search.accountNumber || undefined,
        currency: search.currency || undefined,
        isDefault:
          search.isDefault !== null ? search.isDefault.toString() : undefined,
        currentPage: currentPage.value || undefined,
        pageSize: pageSize.value || undefined,
      },
    });
  },
  {
    immediate: true,
  }
);
</script>

<style scoped>
.flex-1 {
  flex: 1;
}
.default-checkbox {
  margin-left: -0.5rem;
}
</style>
