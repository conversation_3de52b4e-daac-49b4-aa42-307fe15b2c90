import { computed, onMounted } from 'vue';
import { BankAccount, useBankAccounts } from './useBankAccounts';

const useGetDefaultBankAccount = () => {
  const { data, loading, run } = useBankAccounts({});

  onMounted(() => {
    run({ isDefault: true });
  });

  const defaultBankAccount = computed<BankAccount | null>(() => {
    return data.value?.items[0] ?? null;
  });

  return {
    defaultBankAccount,
    loading,
  };
};

export { useGetDefaultBankAccount };
