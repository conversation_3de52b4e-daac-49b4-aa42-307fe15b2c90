import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

type SetDefaultBankAccountRes = unknown;

interface SetDefaultBankAccountProps {
  id: number;
}

const useSetDefaultBankAccount = ({ ...useProps }: UseProps) => {
  const vueRequest = requestProvider<
    SetDefaultBankAccountRes,
    SetDefaultBankAccountProps
  >(
    (props) => {
      const request = axiosProvider
        .put(`/user/bank-accounts/${props.id}/set-default`)
        .then(({ data }) => data);

      return request;
    },
    {
      ...useProps,
      manual: true,
      onSuccess: (res) => {
        if (useProps.onSuccess) useProps.onSuccess(res);
      },
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export { useSetDefaultBankAccount };
