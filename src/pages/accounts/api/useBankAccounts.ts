import { CurrencyCode } from 'src/api';
import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

interface BankAccount {
  id: number;
  bankName: string;
  accountName: string;
  accountNumber: string;
  branchCode: string;
  currency: CurrencyCode['code'];
  isDefault: boolean;
  createdAt: string;
}

interface BankAccountsRes {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: Array<BankAccount>;
}

interface BankAccountsProps {
  pageNumber?: number | null;
  pageSize?: number | null;
  orderBy?: 'Id' | null;
  orderByDescending?: boolean | null;
  bankName?: string | null;
  accountName?: string | null;
  accountNumber?: string | null;
  currency?: CurrencyCode['code'] | null;
  isDefault?: boolean | null;
}

const useBankAccounts = (useProps: BankAccountsProps) => {
  const vueRequest = requestProvider<BankAccountsRes, BankAccountsProps>(
    (props) => {
      const request = axiosProvider
        .get('/user/bank-accounts', { params: props })
        .then(({ data }) => data);

      return request;
    },
    {
      defaultParams: [useProps],
      manual: true,
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export { useBankAccounts };
export type { BankAccount, BankAccountsProps };
