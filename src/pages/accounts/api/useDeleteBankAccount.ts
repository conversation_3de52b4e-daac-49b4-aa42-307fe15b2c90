import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

type DeleteBankAccountRes = unknown;

interface DeleteBankAccountProps {
  id: number;
}

const useDeleteBankAccount = ({ ...useProps }: UseProps) => {
  const vueRequest = requestProvider<
    DeleteBankAccountRes,
    DeleteBankAccountProps
  >(
    (props) => {
      const request = axiosProvider
        .delete(`/user/bank-accounts/${props.id}`)
        .then(({ data }) => data);

      return request;
    },
    {
      ...useProps,
      manual: true,
      onSuccess: (res) => {
        if (useProps.onSuccess) useProps.onSuccess(res);
      },
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export { useDeleteBankAccount };
