import { CurrencyCode } from 'src/api';
import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

type AddBankAccountRes = unknown;

interface AddBankAccountProps {
  bankName: string;
  currency: CurrencyCode['code'];
  accountName: string;
  accountNumber: string;
  branchCode: string;
}

const useAddBankAccount = ({ ...useProps }: UseProps) => {
  const vueRequest = requestProvider<AddBankAccountRes, AddBankAccountProps>(
    (props) => {
      const request = axiosProvider
        .post('/user/bank-accounts', props)
        .then(({ data }) => data);

      return request;
    },
    {
      ...useProps,
      manual: true,
      onSuccess: (res) => {
        if (useProps.onSuccess) useProps.onSuccess(res);
      },
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export { useAddBankAccount };
