<template>
  <q-page
    padding
    class="container q-py-lg flex items-center column justify-center"
  >
    <!-- Waiting Overlay -->
    <div v-if="tradeStatus === 'Pending'" class="waiting-overlay">
      <div class="waiting-container">
        <div class="waiting-card">
          <q-spinner-puff color="primary" size="60px" />
          <div class="waiting-title">
            {{ t('externalPaymentDetailPage.waitingForAgentMessage') }}
          </div>
          <div class="waiting-subtitle">
            {{ t('externalPaymentDetailPage.agentWillSendDetailsSoon') }}
          </div>
          <!-- Countdown timer -->
          <div class="countdown-container q-mb-lg">
            <div class="countdown-label">
              {{ t('externalPaymentDetailPage.timeRemaining') }}
            </div>
            <div class="countdown-timer">{{ formatTime(countdown) }}</div>
            <q-linear-progress
              :value="countdownProgress"
              color="warning"
              size="10px"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- Loading state -->
    <div v-if="verifying" class="full-width flex flex-center column">
      <q-spinner color="primary" size="3em" />
      <div class="q-mt-md text-subtitle1">
        {{ t('externalPaymentDetailPage.verifying') }}
      </div>
    </div>

    <!-- Error state -->
    <div
      v-else-if="agentNotAcceptingOrderError"
      class="full-width flex flex-center column"
    >
      <q-icon name="error" color="negative" size="3em" />
      <div class="q-mt-md text-subtitle1 text-negative">
        {{ t('externalPaymentDetailPage.agentNotAcceptingOrderError') }}
      </div>
    </div>
    <!-- Error state -->
    <div
      v-else-if="orderExpiredError"
      class="full-width flex flex-center column"
    >
      <q-icon name="error" color="negative" size="3em" />
      <div class="q-mt-md text-subtitle1 text-negative">
        {{ t('externalPaymentDetailPage.orderExpiredError') }}
      </div>
    </div>
    <!-- Error state -->
    <div
      v-else-if="verificationError"
      class="full-width flex flex-center column"
    >
      <q-icon name="error" color="negative" size="3em" />
      <div class="q-mt-md text-subtitle1 text-negative">
        {{ t('externalPaymentDetailPage.verificationError') }}
      </div>
      <q-btn
        color="primary"
        class="q-mt-lg"
        :label="t('externalPaymentDetailPage.tryAgain')"
        @click="verifyEntryCodeFromUrl"
      />
    </div>
    <!-- Payment details -->
    <q-card
      v-else
      :class="[
        'wrapper',
        q.screen.lt.sm ? 'q-pa-md' : 'q-pa-lg',
        themeStore.isDark ? 'bg-dark' : 'bg-white',
      ]"
      style="max-width: 1200px"
    >
      <div class="text-center text-h5 q-mb-lg">
        {{ t('externalPaymentDetailPage.title') }}
      </div>

      <!-- Countdown timer -->
      <div v-if="!shouldNotCountdown" class="countdown-container q-mb-lg">
        <div class="countdown-label">
          {{ t('externalPaymentDetailPage.timeRemaining') }}
        </div>
        <div class="countdown-timer">{{ formatTime(countdown) }}</div>
        <q-linear-progress
          :value="countdownProgress"
          color="warning"
          size="10px"
        />
      </div>

      <div class="row q-col-gutter-md">
        <!-- Left side: Order details -->
        <div class="col-12 col-md-7">
          <div
            v-if="tradeStatus !== 'Completed'"
            @class="
              [
                themeStore.isDark ? 'bg-dark' : 'bg-white',
                'modern-order-container q-mx-auto',
              ]
            "
            style="max-width: 700px"
          >
            <!-- Order Summary Card -->
            <div
              class="order-summary-card bg-white shadow-8 rounded-borders q-mb-lg"
              style="border-radius: 16px; overflow: hidden"
            >
              <!-- Header Banner -->
              <div
                class="order-header bg-gradient text-white q-pa-md"
                style="
                  background: linear-gradient(135deg, #3949ab 0%, #5c6bc0 100%);
                  position: relative;
                  overflow: hidden;
                "
              >
                <div class="row items-center justify-between q-py-sm">
                  <div class="col-auto">
                    <div class="text-h6 text-weight-medium">
                      {{ t('externalPaymentDetailPage.orderDetails') }}
                    </div>
                    <div class="text-caption q-mt-xs">
                      {{ orderData.orderTime }}
                    </div>
                  </div>

                  <div class="col-auto flex justify-center items-center gap-md">
                    <div v-if="orderData.disputeStatus !== 'None'">
                      <q-icon name="warning" color="warning">
                        <q-tooltip anchor="top middle" self="bottom middle">
                          There is a dispute on this transaction.
                        </q-tooltip>
                      </q-icon>
                    </div>
                    <q-badge
                      :color="getStatusColor(orderData.orderStatus)"
                      text-color="white"
                      class="q-pa-sm"
                    >
                      <trade-status-label
                        :status="
                          stringToEnumValue(
                            TradeStatusEnum,
                            orderData.orderStatus
                          )
                        "
                      />
                    </q-badge>
                  </div>
                </div>
                <!-- Decorative elements -->
                <div
                  class="header-decoration"
                  style="
                    position: absolute;
                    top: -30px;
                    right: -30px;
                    width: 120px;
                    height: 120px;
                    border-radius: 50%;
                    opacity: 0.1;
                    background: radial-gradient(
                      circle,
                      #ffffff 0%,
                      transparent 70%
                    );
                  "
                ></div>
                <div
                  class="header-decoration"
                  style="
                    position: absolute;
                    bottom: -40px;
                    left: -40px;
                    width: 100px;
                    height: 100px;
                    border-radius: 50%;
                    opacity: 0.1;
                    background: radial-gradient(
                      circle,
                      #ffffff 0%,
                      transparent 70%
                    );
                  "
                ></div>
              </div>

              <!-- Order Overview -->
              <div class="order-overview q-pa-lg">
                <!-- Amount Cards -->
                <div class="row q-col-gutter-md q-mb-md">
                  <!-- Payment amount -->
                  <div class="col-12 col-md-6">
                    <div
                      class="amount-card bg-indigo-1 rounded-borders q-pa-md"
                      style="border-radius: 12px"
                    >
                      <div class="row items-center">
                        <div class="col">
                          <div class="text-caption text-grey-8">
                            {{ t('externalPaymentDetailPage.paymentAmount') }}
                          </div>
                          <div
                            class="text-h5 flex items-center text-weight-bold text-indigo-9 q-mt-xs"
                          >
                            {{ orderData.paymentAmount }}
                            <q-btn
                              flat
                              round
                              dense
                              color="grey-7"
                              icon="content_copy"
                              size="sm"
                              class="q-ml-xs"
                              @click="copyFiatAmount"
                            />
                          </div>
                        </div>

                        <div class="col-auto">
                          <q-icon
                            name="payments"
                            color="indigo-5"
                            size="2rem"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Crypto amount -->
                  <div class="col-12 col-md-6">
                    <div
                      class="amount-card bg-blue-1 rounded-borders q-pa-md"
                      style="border-radius: 12px"
                    >
                      <div class="row items-center">
                        <div class="col">
                          <div class="text-caption text-grey-8">
                            {{ t('externalPaymentDetailPage.cryptoAmount') }}
                          </div>
                          <div
                            class="text-h5 text-weight-bold text-blue-9 q-mt-xs"
                          >
                            {{ orderData.cryptoAmount }}
                          </div>
                        </div>
                        <div class="col-auto">
                          <q-icon
                            name="currency_bitcoin"
                            color="blue-5"
                            size="2rem"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Divider -->
                <q-separator class="q-my-md" />

                <!-- Transaction Details -->
                <div class="transaction-details q-pa-md">
                  <div class="details-list">
                    <!-- Header -->
                    <div class="payment-header q-pt-md q-mb-md">
                      <div class="row items-center">
                        <q-icon
                          name="account_balance"
                          color="primary"
                          size="1.8rem"
                          class="q-mr-sm"
                        />
                        <div class="text-h6 text-weight-medium text-primary">
                          {{
                            t('externalPaymentDetailPage.paymentDetailsTitle')
                          }}
                        </div>
                      </div>
                      <div class="text-caption text-grey-7 q-mt-sm">
                        {{
                          t('externalPaymentDetailPage.paymentDetailsSubtitle')
                        }}
                      </div>
                    </div>

                    <!-- Bank Info Card -->
                    <div
                      class="bank-info bg-blue-1 rounded-borders q-pa-md q-mb-md"
                      style="border-radius: 12px"
                    >
                      <div class="row q-col-gutter-md">
                        <div class="col-12 col-sm-6">
                          <!-- Account Number Card with Copy Button -->
                          <div
                            class="account-number-card rounded-borders"
                            style="border-radius: 12px"
                          >
                            <div class="text-caption text-grey-7">
                              Bank Name
                            </div>
                            <div
                              class="row items-center justify-between q-mt-sm"
                            >
                              <div class="text-body1 text-weight-bold">
                                {{ orderData.recipientBank }}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-12 col-sm-6">
                          <!-- Account Number Card with Copy Button -->
                          <div
                            class="account-number-card rounded-borders"
                            style="border-radius: 12px"
                          >
                            <div class="text-caption text-grey-7">
                              {{
                                t(
                                  'externalPaymentDetailPage.paymentDetailsAccountNumber'
                                )
                              }}
                            </div>
                            <div
                              class="row items-center justify-between q-mt-sm"
                            >
                              <div class="text-body1 text-weight-bold">
                                {{ orderData.recipientAccount }}
                              </div>
                              <div class="col-auto">
                                <q-btn
                                  flat
                                  round
                                  dense
                                  icon="content_copy"
                                  size="xs"
                                  color="grey-7"
                                  @click="copyRecipientAccount"
                                >
                                  <q-tooltip>{{
                                    t(
                                      'externalPaymentDetailPage.copyRecipientAccount'
                                    )
                                  }}</q-tooltip>
                                </q-btn>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="row q-col-gutter-md">
                        <div class="col-12 col-sm-6">
                          <div class="text-caption text-grey-7">
                            {{
                              t(
                                'externalPaymentDetailPage.paymentDetailsBranchCode'
                              )
                            }}
                          </div>
                          <div class="text-body2 text-weight-medium q-mt-xs">
                            {{ orderData.recipientBranchCode }}
                          </div>
                        </div>
                        <div class="col-12 col-sm-6">
                          <div class="text-caption text-grey-7">
                            {{
                              t(
                                'externalPaymentDetailPage.paymentDetailsAccountName'
                              )
                            }}
                          </div>
                          <div
                            class="text-body2 text-weight-medium q-mt-xs text-truncate"
                          >
                            {{ orderData.recipientName }}
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Recipient Information -->
                    <div class="recipient-info q-mt-md q-pb-md">
                      <div
                        class="text-subtitle1 text-weight-medium text-grey-8 q-mb-sm"
                      >
                        {{ t('externalPaymentDetailPage.recipientInfo') }}
                      </div>

                      <div class="row q-col-gutter-md">
                        <!-- Recipient Name -->
                        <div class="col-12">
                          <div
                            class="recipient-account bg-grey-1 rounded-borders q-pa-md"
                          >
                            <div class="row items-center justify-between">
                              <div class="col">
                                <div class="text-caption text-grey-7">
                                  {{
                                    t('externalPaymentDetailPage.recipientName')
                                  }}
                                </div>
                                <div
                                  class="text-body2 text-weight-medium q-mt-xs"
                                >
                                  {{ orderData.payerBankAccountName }}
                                </div>
                              </div>
                              <div class="col-auto">
                                <q-btn
                                  flat
                                  round
                                  dense
                                  icon="content_copy"
                                  size="xs"
                                  color="grey-7"
                                  @click="copyPayerBankAccountName"
                                >
                                  <q-tooltip>{{
                                    t(
                                      'externalPaymentDetailPage.copyPayerBankAccountName'
                                    )
                                  }}</q-tooltip>
                                </q-btn>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Payment Notice and Time Remaining -->
            <div
              class="payment-notice bg-orange-1 rounded-borders q-pa-lg q-mb-lg"
              style="border-radius: 16px; position: relative"
            >
              <div class="row">
                <div class="col-auto q-mr-md">
                  <q-icon name="schedule" color="orange-8" size="2rem" />
                </div>
                <div class="col">
                  <div class="text-subtitle1 text-weight-bold text-orange-9">
                    {{ t('externalPaymentDetailPage.paymentNotice') }}
                  </div>
                  <div class="text-body2 q-mt-sm text-grey-8">
                    {{ t('externalPaymentDetailPage.paymentInstructions') }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
              <!-- Confirm Payment Button -->
              <q-btn
                color="primary"
                class="full-width q-py-sm q-mb-md"
                style="border-radius: 8px; font-weight: 500; height: 54px"
                :label="
                  !confirmedPaid
                    ? t('externalPaymentDetailPage.confirmPaymentButton')
                    : t('externalPaymentDetailPage.confirmed')
                "
                :icon="confirmedPaid ? 'check_circle' : ''"
                @click="handleConfirmPayment"
                :disable="confirmedPaid"
                :loading="loading"
              />

              <!-- Payment Proof Button -->
              <q-btn
                :color="paymentProofUrl?.value?.url ? 'secondary' : 'blue-5'"
                class="full-width q-py-sm q-mb-md"
                style="border-radius: 8px; font-weight: 500; height: 54px"
                :label="
                  paymentProofUrl?.value?.url
                    ? t('externalPaymentDetailPage.uploaded')
                    : t('externalPaymentDetailPage.uploadPaymentProofButton')
                "
                :icon-right="
                  paymentProofUrl?.value?.url ? 'visibility' : 'upload_file'
                "
                @click="
                  paymentProofUrl?.value?.url
                    ? showPaymentProof()
                    : openPaymentProofDialog()
                "
                :loading="uploadingPaymentProof.value"
                :disable="confirmedProof"
              />

              <!--              &lt;!&ndash; Cancel Button &ndash;&gt;-->
              <!--              <q-btn-->
              <!--                flat-->
              <!--                color="grey-7"-->
              <!--                class="full-width q-py-sm"-->
              <!--                style="border-radius: 8px; font-weight: 500; height: 44px"-->
              <!--                :label="t('externalPaymentDetailPage.cancelButton')"-->
              <!--                :disable="confirmedProof || confirmedPaid"-->
              <!--                @click="handleCancel"-->
              <!--              />-->

              <!-- Hidden file input for payment proof upload -->
              <input
                type="file"
                ref="paymentProofInput"
                accept="image/*"
                style="display: none"
                @change="handlePaymentProofSelected"
              />
            </div>
          </div>
          <!-- Complete Transaction Card (replaces order details section) -->
          <div
            v-else
            class="completed-card q-mx-auto bg-white shadow-8 rounded-borders q-pa-none"
            style="max-width: 700px; border-radius: 16px; overflow: hidden"
          >
            <!-- Success Banner -->
            <div
              class="success-banner bg-gradient text-white q-pa-lg text-center relative-position"
              style="
                background: linear-gradient(135deg, #21ba45 0%, #1db954 100%);
                height: 180px;
              "
            >
              <div class="absolute-center">
                <q-icon name="check_circle" size="4rem" class="q-mb-sm" />
                <div class="text-h5 text-weight-bold">
                  {{ $t('externalPaymentDetailPage.orderCompleted') }}
                </div>
              </div>
            </div>

            <!-- Transaction Details Container -->
            <div class="transaction-container q-pa-lg">
              <!-- Transaction ID and Time Section -->
              <div class="transaction-header q-mb-lg">
                <div class="row justify-between items-center">
                  <div class="col-auto">
                    <div class="text-subtitle1 text-weight-medium text-grey-8">
                      {{ $t('externalPaymentDetailPage.orderNumber') }}
                    </div>
                    <div class="row items-center">
                      <div class="text-h6 text-primary">
                        {{ orderData.orderNumber }}
                      </div>
                      <q-btn
                        flat
                        round
                        dense
                        color="grey-7"
                        icon="content_copy"
                        size="sm"
                        class="q-ml-xs"
                        @click="
                          copyToClipboard(
                            orderData.orderNumber,
                            'copiedOrderNumber'
                          )
                        "
                      />
                    </div>
                  </div>
                  <div class="col-auto">
                    <q-badge
                      color="green-2"
                      text-color="green-10"
                      class="q-pa-sm"
                    >
                      <q-icon name="check" size="xs" class="q-mr-xs" />
                      {{ $t('externalPaymentDetailPage.orderStatus') }}:
                      {{ $t('externalPaymentDetailPage.orderCompleted') }}
                    </q-badge>
                  </div>
                </div>
                <div class="text-caption text-grey-7 q-mt-sm">
                  {{ $t('externalPaymentDetailPage.completedAt') }}:
                  {{ new Date(orderData.completedAt).toLocaleString() }}
                </div>
              </div>

              <q-separator spaced />

              <!-- Amount Information -->
              <div class="amount-section q-mb-lg">
                <div class="row q-col-gutter-md">
                  <!-- Fiat Amount -->
                  <div class="col-12 col-sm-6">
                    <div class="bg-grey-1 rounded-borders q-pa-md">
                      <div class="text-caption text-grey-7">
                        {{ $t('externalPaymentDetailPage.paymentAmount') }}
                      </div>
                      <div
                        class="text-h5 text-weight-bold q-mt-sm text-primary"
                      >
                        {{ orderData.fiatCurrency }}
                        {{ orderData.fiatAmount }}
                      </div>
                    </div>
                  </div>

                  <!-- Crypto Amount -->
                  <div class="col-12 col-sm-6">
                    <div class="bg-grey-1 rounded-borders q-pa-md">
                      <div class="text-caption text-grey-7">
                        {{ $t('externalPaymentDetailPage.cryptoAmount') }}
                      </div>
                      <div
                        class="text-h5 text-weight-bold q-mt-sm text-secondary"
                      >
                        {{ orderData.cryptoAmount }}
                        {{ orderData.cryptoSymbol }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Exchange Rate -->
                <div
                  class="exchange-rate q-mt-md bg-blue-1 rounded-borders q-pa-md"
                >
                  <div class="row items-center">
                    <q-icon
                      name="sync_alt"
                      color="blue-8"
                      size="sm"
                      class="q-mr-sm"
                    />
                    <div class="text-body2 text-blue-8">
                      <span class="text-weight-medium"
                        >{{
                          $t('externalPaymentDetailPage.exchangeRate')
                        }}:</span
                      >
                      {{ orderData.exchangeRate }}
                    </div>
                  </div>
                </div>
              </div>

              <q-separator spaced />

              <!-- Recipient Information -->
              <div class="recipient-section q-mb-lg">
                <div
                  class="text-subtitle1 text-weight-medium text-grey-8 q-mb-md"
                >
                  {{ $t('externalPaymentDetailPage.recipientInfo') }}
                </div>

                <div class="bg-grey-1 rounded-borders q-pa-md">
                  <div class="row q-col-gutter-md">
                    <!-- Bank Info -->
                    <div class="col-12 col-sm-6">
                      <div class="q-mb-md">
                        <div class="text-caption text-grey-7">
                          {{
                            $t(
                              'externalPaymentDetailPage.paymentDetailsBankName'
                            )
                          }}
                        </div>
                        <div class="text-body1 text-weight-medium">
                          {{ orderData.recipientBank }}
                        </div>
                      </div>

                      <div>
                        <div class="text-caption text-grey-7">
                          {{ $t('externalPaymentDetailPage.recipientName') }}
                        </div>
                        <div class="text-body1 text-weight-medium">
                          {{ orderData.recipientName }}
                        </div>
                      </div>
                    </div>

                    <!-- Account Number -->
                    <div class="col-12 col-sm-6">
                      <div>
                        <div class="text-caption text-grey-7">
                          {{ $t('externalPaymentDetailPage.recipientAccount') }}
                        </div>
                        <div class="row items-center">
                          <div class="text-body1 text-weight-medium">
                            {{ orderData.recipientAccount }}
                          </div>
                          <q-btn
                            flat
                            round
                            dense
                            color="grey-7"
                            icon="content_copy"
                            size="sm"
                            class="q-ml-xs"
                            @click="
                              copyToClipboard(
                                orderData.recipientAccount,
                                'copiedAccountNumber'
                              )
                            "
                          />
                        </div>
                      </div>
                      <div class="q-mt-md">
                        <div class="text-caption text-grey-7">
                          {{
                            $t(
                              'externalPaymentDetailPage.paymentDetailsBranchCode'
                            )
                          }}
                        </div>
                        <div class="text-body1 text-weight-medium">
                          {{ orderData.recipientBranchCode }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Help and Support -->
              <div class="help-section text-center q-mt-xl">
                <q-icon
                  name="support_agent"
                  color="grey-7"
                  size="1.5rem"
                  class="q-mb-sm"
                />
                <div class="text-body2 text-grey-7">
                  {{ $t('externalPaymentDetailPage.needHelp') }}
                  <a href="#" class="text-primary">{{
                    $t('externalPaymentDetailPage.customerService')
                  }}</a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Right side: Chat -->
        <div class="col-12 col-md-5">
          <div
            :class="[
              'chat-container',
              tradeStatus === 'Completed' ? 'is-completed' : 'is-not-completed',
            ]"
          >
            <div class="chat-header q-pa-sm flex items-center justify-between">
              <div class="text-weight-bold">
                {{ t('externalPaymentDetailPage.customerService') }}
              </div>
              <q-badge
                color="green"
                :label="t('externalPaymentDetailPage.online')"
              />
            </div>

            <div class="chat-messages q-px-sm q-py-md" ref="chatMessagesRef">
              <div
                v-for="(message, index) in chatMessages"
                :key="index"
                :class="[
                  'message-item q-mb-sm',
                  message.senderRole !== SenderRole.User
                    ? 'customer-message'
                    : 'user-message',
                ]"
              >
                <div class="message-content q-pa-sm">
                  <!-- Text message -->
                  <div v-if="!message.isImage">{{ message.content }}</div>
                  <!-- Image message -->
                  <q-img
                    v-else
                    :src="message.content"
                    spinner-color="primary"
                    style="width: 200px; height: 200px; border-radius: 4px"
                    @load="scrollToBottom"
                    fit="contain"
                  />
                </div>
                <div class="message-time text-caption text-grey">
                  {{ message.time }}
                </div>
              </div>
            </div>

            <!-- Hidden file input for image upload -->
            <input
              type="file"
              ref="fileInput"
              accept="image/*"
              style="display: none"
              @change="handleFileSelected"
            />

            <div class="chat-input q-pa-sm">
              <q-input
                v-model="messageInput"
                outlined
                dense
                :placeholder="t('externalPaymentDetailPage.typeMessage')"
                @keyup.enter="sendMessage"
              >
                <template v-slot:before>
                  <q-btn
                    round
                    dense
                    flat
                    icon="attach_file"
                    color="primary"
                    @click="openFileDialog"
                  />
                </template>
                <template v-slot:after>
                  <q-btn
                    round
                    dense
                    flat
                    icon="send"
                    color="primary"
                    @click="sendMessage"
                  />
                </template>
              </q-input>
            </div>
          </div>
        </div>
      </div>

      <!-- Notes section -->
      <div class="q-mt-xl">
        <div class="text-subtitle1 q-mb-sm">
          {{ t('externalPaymentDetailPage.notesTitle') }}
        </div>
        <ul class="q-pl-md">
          <li
            v-for="(note, index) in notes"
            :key="index"
            class="q-mb-xs text-grey-8"
          >
            {{ note }}
          </li>
        </ul>
      </div>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import {
  computed,
  nextTick,
  onBeforeUnmount,
  onMounted,
  ref,
  watch,
} from 'vue';
import { useI18n } from 'vue-i18n';
import { copyToClipboard, useQuasar } from 'quasar';
import { useRoute, useRouter } from 'vue-router';
import { DigitTypes, thousandTool } from 'src/utils/NumberTool';
import {
  useAuthStore,
  useChatStore,
  useThemeStore,
  useTradeStore,
} from 'src/stores';
import {
  useGetMessages,
  useGetProxyOrder,
  usePaymentMade,
  useSendChatImage,
  useVerifyEntryCode,
} from './api';
import { useClientHub } from 'src/hooks/useClientHub';
import { usePaymentProof } from './api/usePaymentProof';
import {
  SenderRole,
  stringToEnumValue,
  TradeStatusEnum,
} from '../../utils/enums';
import TradeStatusLabel from '../../components/TradeStatusLabel.vue';

const { t } = useI18n();
const q = useQuasar();
const route = useRoute();
const themeStore = useThemeStore();
const authStore = useAuthStore();
const loading = ref<boolean>(false);
const tradeStatus = ref<string>('');
const chatMessagesRef = ref<HTMLElement | null>(null);
const fileInput = ref<HTMLInputElement | null>(null);
const messageInput = ref<string>('');
const verifying = ref<boolean>(true);
const verificationError = ref<boolean>(false);
const agentNotAcceptingOrderError = ref<boolean>(false);
const orderExpiredError = ref<boolean>(false);
const entryCode = ref<string>('');
const paymentProofInput = ref<HTMLInputElement | null>(null);
const confirmedPaid = ref<boolean>(false);
const confirmedProof = ref<boolean>(false);

const uploadingPaymentProof = ref<boolean>(false);

// API hooks
const { run: verifyEntryCode, error: verifyEntryCodeError } =
  useVerifyEntryCode({
    onSuccess: () => {
      // Verification successful

      fetchProxyOrder({});
      verifying.value = false;
      verificationError.value = false;
    },
    onError: (error) => {
      console.error('Error verifying entry code:', error);
      verifying.value = false;
      verificationError.value = true;
    },
  });
const { data: proxyOrder, run: fetchProxyOrder } = useGetProxyOrder();
const { run: confirmPayment } = usePaymentMade({});
const { run: paymentProof, data: paymentProofUrl } = usePaymentProof({});

// Initialize SignalR connection
const tradeStore = useTradeStore();
const chatStore = useChatStore();
const { isConnected, startConnection } = useClientHub('', true);
// Countdown timer - 30 minutes in seconds (default)
let COUNTDOWN_DURATION = 30 * 60;
const countdown = ref<number>(COUNTDOWN_DURATION);
const countdownInterval = ref<number | null>(null);
const countdownProgress = computed(() => countdown.value / COUNTDOWN_DURATION);
const getStatusColor = (status: string) => {
  switch (status) {
    case 'Completed':
      return 'positive';
    case 'BankAccountAssigned':
      return 'blue-7';
    case 'Pending':
    case 'PaymentMade':
      return 'orange-8';
    case 'Cancelled':
    case 'Expired':
      return 'negative';
    default:
      return 'grey-7';
  }
};
// Format time as MM:SS
const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
    .toString()
    .padStart(2, '0')}`;
};
watch(paymentProofUrl, (newUrl) => {
  console.log('paymentProofUrl updated:', newUrl);
});
// Computed order data from API
const orderData = computed(() => {
  if (!proxyOrder.value) {
    return {
      exchangeRate: '',
      orderTime: '',
      paymentAmount: '',
      orderNumber: '',
      recipientBank: '',
      recipientAccount: '',
      recipientName: '',
      recipientBranchCode: '',
      paymentMethod: '',
      payerBankAccountName: '',
      updatedAt: '',
      chatRoomId: 0,
      cryptoAmount: '',
      cryptoSymbol: '',
      agentFee: '',
      orderStatus: '',
      disputeStatus: 'None',
      network: '',
      tradeType: '',
      completedAt: '',
      fiatAmount: 0,
      fiatCurrency: '',
    };
  }

  return {
    exchangeRate: `1 ${proxyOrder.value.cryptoSymbol} = ${proxyOrder.value.rate} ${proxyOrder.value.fiatCurrency}`,
    orderTime: new Date(proxyOrder.value.createdAt).toLocaleString(),
    paymentAmount: `${proxyOrder.value.fiatCurrency} ${thousandTool(
      proxyOrder.value.fiatAmount,
      proxyOrder.value.fiatCurrency as unknown as DigitTypes
    )}`,
    orderNumber: proxyOrder.value.tradeNo,
    recipientBank: proxyOrder.value.bankName,
    recipientAccount: proxyOrder.value.accountNumber,
    recipientName: proxyOrder.value.accountName,
    recipientBranchCode: proxyOrder.value.branchCode,
    paymentMethod: proxyOrder.value.bankName,
    payerBankAccountName: proxyOrder.value.payerBankAccountName,
    chatRoomId: proxyOrder.value.id,
    updatedAt: proxyOrder.value.updatedAt,
    cryptoAmount: `${thousandTool(
      proxyOrder.value.cryptoAmount,
      proxyOrder.value.cryptoSymbol as unknown as DigitTypes
    )} ${proxyOrder.value.cryptoSymbol}`,
    cryptoSymbol: proxyOrder.value.cryptoSymbol,
    agentFee: `${proxyOrder.value.fiatCurrency} ${thousandTool(
      proxyOrder.value.agentFeeAmount,
      proxyOrder.value.fiatCurrency as unknown as DigitTypes
    )}`,
    orderStatus: proxyOrder.value.statusDesc,
    disputeStatus: proxyOrder.value.disputeStatusDesc,
    network: proxyOrder.value.networkDesc,
    tradeType: proxyOrder.value.typeDesc,
    completedAt: proxyOrder.value.completedAt,
    fiatAmount: proxyOrder.value.fiatAmount,
    fiatCurrency: proxyOrder.value.fiatCurrency,
  };
});

// Define interface for chat messages
interface ChatMessage {
  content: string;
  time: string;
  senderRole: SenderRole;
  isImage: boolean;
  caption?: string | null;
}

// Chat functionality
const chatMessages = ref<ChatMessage[]>([]);

// Get chat messages when order data is available
const { data: apiMessages, run: fetchMessages } = useGetMessages({});

// Send chat image
const { run: sendChatImage } = useSendChatImage({});
// Start countdown timer
const startCountdown = (
  state:
    | 'WAITING_FOR_AGENT_RESPONSE'
    | 'WAITING_FOR_CONFIRMATION'
    | 'WAITING_FOR_AGENT_CONFIRMATION' = 'WAITING_FOR_CONFIRMATION'
) => {
  //  Clear any previously running countdown
  if (countdownInterval.value) {
    clearInterval(countdownInterval.value);
    countdownInterval.value = null;
  }
  const updatedAtTime = new Date(
    orderData?.value?.updatedAt ?? Date.now()
  ).getTime();
  const now = Date.now();
  const elapsedSeconds = Math.floor((now - updatedAtTime) / 1000);

  if (state === 'WAITING_FOR_CONFIRMATION') {
    countdown.value = 30 * 60; // 30 minutes in seconds
    // Calculate remaining time using updatedAt
  } else if (
    state === 'WAITING_FOR_AGENT_RESPONSE' ||
    state === 'WAITING_FOR_AGENT_CONFIRMATION'
  ) {
    countdown.value = 5 * 60; // 5 minutes in seconds
  }
  // Adjust countdown.value based on elapsed time
  countdown.value = Math.max(countdown.value - elapsedSeconds, 0);
  COUNTDOWN_DURATION = countdown.value;
  countdownInterval.value = window.setInterval(() => {
    if (countdown.value >= 1) countdown.value--;

    if (countdown.value <= 0) {
      // Time's up - handle order cancellation
      if (countdownInterval.value) {
        clearInterval(countdownInterval.value);
        countdownInterval.value = null;
      }

      // Show notification and redirect

      if (state == 'WAITING_FOR_CONFIRMATION') {
        orderExpiredError.value = true;
        q.notify({
          color: 'negative',
          message: t('externalPaymentDetailPage.orderExpired'),
          icon: 'timer_off',
        });
      } else if (state == 'WAITING_FOR_AGENT_RESPONSE') {
        agentNotAcceptingOrderError.value = true;
        q.notify({
          color: 'negative',
          message: t('externalPaymentDetailPage.orderExpired'),
          icon: 'timer_off',
        });
      }
      tradeStatus.value = 'Expired';
    }
  }, 1000);
};
// Transform API messages to our chat format
watch(
  () => apiMessages.value,
  (newMessages) => {
    if (newMessages && newMessages.length > 0) {
      chatMessages.value = newMessages.map((msg) => ({
        content: msg.messageType === 1 ? msg.payload : msg.payload, // Image or text
        time: new Date(msg.sentAt).toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        }),
        senderRole: msg.senderRole,
        isImage: msg.messageType === 1, // 1 is image type
        caption: msg.caption,
      }));
      scrollToBottom();
    }
  }
);

// Watch for new messages from SignalR
watch(
  () => chatStore.message,
  (newMessage) => {
    if (newMessage && orderData.value.chatRoomId === newMessage.tradeId) {
      chatMessages.value.push({
        content:
          newMessage.messageType === 1
            ? newMessage.payload
            : newMessage.payload,
        time: new Date(newMessage.sentAt).toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        }),
        senderRole: newMessage.senderRole,
        isImage: newMessage.messageType === 1,
      });
      scrollToBottom();
    }
  }
);
watch(
  () => proxyOrder.value,
  async (newOrder) => {
    if (newOrder) tradeStatus.value = newOrder.statusDesc;
    if (newOrder && !verifyEntryCodeError.value) {
      confirmedPaid.value =
        newOrder.status === TradeStatusEnum.PaymentMade ||
        newOrder.status === TradeStatusEnum.PaymentConfirmed ||
        newOrder.status === TradeStatusEnum.Completed ||
        newOrder.status === TradeStatusEnum.Cancelled ||
        newOrder.status === TradeStatusEnum.Expired;
      confirmedProof.value =
        newOrder.status === TradeStatusEnum.Completed ||
        newOrder.status === TradeStatusEnum.Cancelled ||
        newOrder.status === TradeStatusEnum.Expired;
      fetchMessages({ tradeId: newOrder.id });

      if (authStore.proxyToken) await startConnection(authStore.proxyToken);
      // Scroll to bottom of chat
      scrollToBottom();
    }
  }
);
watch(
  () => tradeStore.trade,
  (newTrade) => {
    if (newTrade && orderData.value.chatRoomId === newTrade.id) {
      orderData.value.orderStatus = newTrade.statusDesc;
      orderData.value.updatedAt = newTrade.updatedAt;
      orderData.value.disputeStatus = newTrade.disputeStatusDesc;
      orderData.value.network = newTrade.networkDesc;
      orderData.value.tradeType = newTrade.typeDesc;
      orderData.value.cryptoAmount = `${thousandTool(
        newTrade.cryptoAmount,
        newTrade.cryptoSymbol as unknown as DigitTypes
      )} ${newTrade.cryptoSymbol}`;
      orderData.value.agentFee = `${newTrade.fiatCurrency} ${thousandTool(
        newTrade.agentFeeAmount,
        newTrade.fiatCurrency as unknown as DigitTypes
      )}`;
      orderData.value.paymentAmount = `${newTrade.fiatCurrency} ${thousandTool(
        newTrade.fiatAmount,
        newTrade.fiatCurrency as unknown as DigitTypes
      )}`;
      orderData.value.payerBankAccountName = newTrade.payerBankAccountName;
      orderData.value.exchangeRate = `1 ${newTrade.cryptoSymbol} = ${proxyOrder.value?.rate} ${proxyOrder.value?.fiatCurrency}`;
      orderData.value.orderTime = new Date(newTrade.createdAt).toLocaleString();
      orderData.value.updatedAt = newTrade.updatedAt;
      orderData.value.fiatAmount = newTrade.fiatAmount;
      orderData.value.orderNumber = newTrade.tradeNo;
      orderData.value.cryptoSymbol = newTrade.cryptoSymbol;
      orderData.value.fiatCurrency = newTrade.fiatCurrency;
      orderData.value.paymentMethod = newTrade.bankName;
      orderData.value.completedAt = newTrade.completedAt;
      tradeStatus.value = newTrade.statusDesc;

      if (newTrade.status === TradeStatusEnum.BankAccountAssigned) {
        orderData.value.recipientBank = newTrade.bankName;
        orderData.value.recipientAccount = newTrade.accountNumber;
        orderData.value.recipientName = newTrade.accountName;
        orderData.value.recipientBranchCode = newTrade.branchCode;
      }
    }
  }
);
watch(
  () => tradeStatus.value,
  () => {
    if (tradeStatus.value === 'BankAccountAssigned') {
      startCountdown('WAITING_FOR_CONFIRMATION');
    } else if (tradeStatus.value === 'Pending') {
      startCountdown('WAITING_FOR_AGENT_RESPONSE');
    } else if (tradeStatus.value === 'PaymentMade') {
      startCountdown('WAITING_FOR_AGENT_CONFIRMATION');
    }
    if (tradeStatus.value === 'Expired') {
      confirmedPaid.value = true;
      confirmedProof.value = true;
    }
  },
  { immediate: true }
);
// Notes from the screenshot
const notes = computed(() => [
  t('externalPaymentDetailPage.note1'),
  t('externalPaymentDetailPage.note2'),
  t('externalPaymentDetailPage.note3'),
  t('externalPaymentDetailPage.note4'),
  t('externalPaymentDetailPage.note5'),
  t('externalPaymentDetailPage.note6'),
  t('externalPaymentDetailPage.note7'),
]);
const openPaymentProofDialog = () => {
  nextTick(() => {
    if (paymentProofInput.value) {
      paymentProofInput.value.click();
    } else {
      console.error('Payment proof input element not found');
      q.notify({
        color: 'negative',
        message: t('externalPaymentDetailPage.imageUploadError'),
        icon: 'error',
      });
    }
  });
};
// const shouldShowPaymentDetailsCard = computed(() => {
//   const validStatuses = [
//     'BankAccountAssigned',
//     'PaymentMade',
//     'TradeStatusEnum',
//     'Completed',
//   ];
//   return paymentDetails.value && validStatuses.includes(orderData.value.orderStatus);
// });

const shouldNotCountdown = computed(() => {
  return (
    orderData.value.orderStatus === 'Completed' ||
    orderData.value.orderStatus === 'Cancelled' ||
    orderData.value.orderStatus === 'Expired'
  );
});

// Handle payment proof file selection
const handlePaymentProofSelected = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (!target.files || !target.files.length) return;

  const file = target.files[0];

  // Validate file type
  if (!file.type.startsWith('image/')) {
    q.notify({
      color: 'negative',
      message: t('externalPaymentDetailPage.invalidFileType'),
      icon: 'error',
    });
    return;
  }

  // Validate file size (max 5MB)
  if (file.size > 5 * 1024 * 1024) {
    q.notify({
      color: 'negative',
      message: t('externalPaymentDetailPage.fileTooLarge'),
      icon: 'error',
    });
    return;
  }

  uploadingPaymentProof.value = true;

  try {
    // Call usePaymentProof API
    paymentProof({
      tradeId: orderData.value.chatRoomId,
      file,
    });

    q.notify({
      color: 'positive',
      message: t('externalPaymentDetailPage.paymentProofUploaded'),
      icon: 'check_circle',
    });
    confirmedProof.value = true;
    // Reset file input
    if (paymentProofInput.value) {
      paymentProofInput.value.value = '';
    }
  } catch (error) {
    console.error('Error uploading payment proof:', error);
    q.notify({
      color: 'negative',
      message: t('externalPaymentDetailPage.paymentProofUploadError'),
      icon: 'error',
    });
  } finally {
    uploadingPaymentProof.value = false;
  }
};

// Show uploaded payment proof in a dialog
const showPaymentProof = () => {
  if (!paymentProofUrl.value) return;

  q.dialog({
    title: t('externalPaymentDetailPage.paymentProofDialogTitle'),
    message: `
      <div class="q-pa-md">
        <q-img
          src="${paymentProofUrl.value}"
          spinner-color="primary"
          style="max-width: 100%; max-height: 400px; border-radius: 4px;"
          fit="contain"
        />
      </div>
    `,
    html: true,
    style: 'max-width: 600px; width: 100%;',
    ok: {
      label: t('externalPaymentDetailPage.closeButton'),
      color: 'primary',
    },
  });
};
// Function to verify entry code from URL
const verifyEntryCodeFromUrl = async () => {
  verifying.value = true;
  verificationError.value = false;

  // Get entry code from URL path parameter
  const { entryCode: routeEntryCode } = route.params;
  if (routeEntryCode && typeof routeEntryCode === 'string') {
    entryCode.value = routeEntryCode;
    verifyEntryCode({
      entryCode: entryCode.value,
    });
  } else {
    // No entry code provided
    console.error('No entry code provided');
    verifying.value = false;
    verificationError.value = true;
  }
};

onMounted(() => {
  // Verify entry code when the page loads

  verifyEntryCodeFromUrl();
});

// Clean up interval on component unmount
onBeforeUnmount(() => {
  if (countdownInterval.value) {
    clearInterval(countdownInterval.value);
    countdownInterval.value = null;
  }
});

const scrollToBottom = () => {
  nextTick(() => {
    if (chatMessagesRef.value) {
      const element = chatMessagesRef.value as HTMLDivElement;
      element.scrollTop = element.scrollHeight;
    }
  });
};

const copyRecipientAccount = () => {
  copyToClipboard(orderData.value.recipientAccount)
    .then(() => {
      q.notify({
        color: 'positive',
        message: t('externalPaymentDetailPage.copiedAccountNumber'),
        icon: 'content_copy',
      });
    })
    .catch(() => {
      q.notify({
        color: 'negative',
        message: t('externalPaymentDetailPage.copyFailed'),
        icon: 'error',
      });
    });
};
const copyPayerBankAccountName = () => {
  copyToClipboard(orderData.value.recipientAccount)
    .then(() => {
      q.notify({
        color: 'positive',
        message: t('externalPaymentDetailPage.copiedPayerBankAccountName'),
        icon: 'content_copy',
      });
    })
    .catch(() => {
      q.notify({
        color: 'negative',
        message: t('externalPaymentDetailPage.copyFailed'),
        icon: 'error',
      });
    });
};
const copyFiatAmount = () => {
  copyToClipboard(orderData.value.recipientAccount)
    .then(() => {
      q.notify({
        color: 'positive',
        message: t('externalPaymentDetailPage.copiedFiatAmount'),
        icon: 'content_copy',
      });
    })
    .catch(() => {
      q.notify({
        color: 'negative',
        message: t('externalPaymentDetailPage.copyFailed'),
        icon: 'error',
      });
    });
};
const openFileDialog = () => {
  // Use nextTick to ensure the DOM is updated
  nextTick(() => {
    if (fileInput.value) {
      fileInput.value.click();
    } else {
      console.error('File input element not found');
      q.notify({
        color: 'negative',
        message: t('externalPaymentDetailPage.imageUploadError'),
        icon: 'error',
      });
    }
  });
};

const handleFileSelected = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (!target.files || !target.files.length) return;

  const file = target.files[0];

  // Check if file is an image
  if (!file.type.startsWith('image/')) {
    q.notify({
      color: 'negative',
      message: t('externalPaymentDetailPage.invalidFileType'),
      icon: 'error',
    });
    return;
  }

  // Check file size (max 5MB)
  if (file.size > 5 * 1024 * 1024) {
    q.notify({
      color: 'negative',
      message: t('externalPaymentDetailPage.fileTooLarge'),
      icon: 'error',
    });
    return;
  }

  try {
    // Use the API to send the image
    sendChatImage({
      tradeId: orderData.value.chatRoomId,
      file: file,
    });

    // Reset file input
    if (fileInput.value) {
      fileInput.value.value = '';
    }

    // The new message will be received via SignalR and added to the chat
  } catch (error) {
    console.error('Error sending image:', error);
    q.notify({
      color: 'negative',
      message: t('externalPaymentDetailPage.imageUploadError'),
      icon: 'error',
    });
  }
};

const sendMessage = async () => {
  if (!messageInput.value.trim()) return;

  try {
    // Use the sendChatMessage function from useClientHub
    if (isConnected.value) {
      await chatStore.sendMessage({
        tradeId: orderData.value.chatRoomId,
        payload: messageInput.value,
        messageType: 0, // Text message
      });

      // Clear the input field
      messageInput.value = '';

      // The message will be received via SignalR and added to the chat
    } else {
      q.notify({
        color: 'negative',
        message: t('externalPaymentDetailPage.connectionError'),
        icon: 'error',
      });
    }
  } catch (error) {
    console.error('Error sending message:', error);
    q.notify({
      color: 'negative',
      message: t('externalPaymentDetailPage.messageSendError'),
      icon: 'error',
    });
  }
};

// Import the payment made API

const handleConfirmPayment = async () => {
  q.dialog({
    title: t('externalPaymentDetailPage.confirmPaymentDialogTitle'),
    message: t('externalPaymentDetailPage.confirmPaymentDialogMessage'),
    cancel: {
      label: t('externalPaymentDetailPage.cancelButtonn'),
      color: 'grey',
      flat: true,
    },
    ok: {
      label: t('externalPaymentDetailPage.confirmButton'),
      color: 'primary',
    },
    persistent: true,
    class: 'confirm-payment-dialog',
  })
    .onOk(async () => {
      loading.value = true;

      try {
        // Call the API to confirm payment
        confirmPayment({
          entryCode: entryCode.value,
        });

        q.notify({
          color: 'positive',
          message: t('externalPaymentDetailPage.paymentConfirmed'),
          icon: 'check_circle',
        });
        confirmedPaid.value = true;
        // The order status will be updated via SignalR
        // We'll wait for the status to change to PaymentMade or Completed
      } catch (error) {
        console.error('Error confirming payment:', error);
        q.notify({
          color: 'negative',
          message: t('externalPaymentDetailPage.paymentError'),
          icon: 'error',
        });
      } finally {
        loading.value = false;
      }
    })
    .onCancel(() => {
      // User cancelled, do nothing
      console.log('Payment confirmation cancelled');
    });
};

// const handleCancel = () => {
//   q.dialog({
//     title: t('externalPaymentDetailPage.cancelConfirmTitle'),
//     message: t('externalPaymentDetailPage.cancelConfirmMessage'),
//     cancel: true,
//     persistent: true,
//   }).onOk(() => {
//     // Redirect back to merchant site or previous page
//     router.push('/external');
//   });
// };
</script>

<style scoped>
.container {
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.wrapper {
  width: 100%;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.payment-notice {
  background-color: rgba(255, 152, 0, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 152, 0, 0.3);
}

body.isDark .payment-notice {
  background-color: rgba(255, 152, 0, 0.05);
}

.chat-container {
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e0;
  height: 100%;

  border-radius: 8px;
  overflow: hidden;
}
.is-completed {
  max-height: 860px;
}
.is-not-completed {
  max-height: 680px;
}
body.isDark .chat-container {
  border-color: #424242;
}

.chat-header {
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

body.isDark .chat-header {
  background-color: #333333;
  border-color: #424242;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;

  background-color: #f9f9f9;
  padding-right: 8px; /* Added padding to fix scrollbar overlap */
  scrollbar-width: thin;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

body.isDark .chat-messages::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

body.isDark .chat-messages {
  background-color: #1e1e1e;
}

.chat-input {
  border-top: 1px solid #e0e0e0;
  background-color: #f5f5f5;
}

body.isDark .chat-input {
  border-color: #424242;
  background-color: #333333;
}

.message-item {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 12px; /* Increased spacing between messages */
}

.message-content {
  border-radius: 12px;
  word-break: break-word;
  width: fit-content;
  max-width: 70%; /* Ensure content doesn't overflow */
  overflow: hidden; /* Hide overflow content */
}
.customer-message {
  align-items: flex-start;
}
.user-message {
  align-items: flex-end;
}
.customer-message .message-content {
  background-color: #e0e0e0;
  color: #000;
  border-top-left-radius: 4px;
}

.user-message .message-content {
  background-color: #2196f3;
  color: white;
  border-top-right-radius: 4px;
}

body.isDark .customer-message .message-content {
  background-color: #424242;
  color: #fff;
}

.message-time {
  margin-top: 2px;
  font-size: 0.7rem;
}

.countdown-container {
  text-align: center;
  padding: 10px;
  border-radius: 8px;
  background-color: rgba(255, 152, 0, 0.05);
  border: 1px solid rgba(255, 152, 0, 0.2);
}

.countdown-label {
  font-size: 0.9rem;
  color: #ff9800;
  margin-bottom: 5px;
}

.countdown-timer {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ff9800;
  margin-bottom: 5px;
}

/* Payment Details Card */

@keyframes slideIn {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Dark mode adjustments */

.waiting-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: rgba(10, 10, 10, 0.35);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.waiting-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.4s ease;
}

.waiting-card {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 40px 30px;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.waiting-title {
  margin-top: 20px;
  font-size: 1.25rem;
  color: #ffffff;
  font-weight: 600;
}

.waiting-subtitle {
  margin-top: 10px;
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.8);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
