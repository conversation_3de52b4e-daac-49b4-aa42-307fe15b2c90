import { useAuthStore } from 'src/stores';
import { forage } from 'src/utils/foragePkg';
import { requestProvider } from 'src/utils/requestProvider';
import { externalAxiosProvider } from '../../../utils/externalAxiosProvider';

export type VerifyEntryCodeProps = {
  entryCode: string;
};

export type VerifyEntryCodeRes = {
  token: string;
  expiresAt: string;
};

const useVerifyEntryCode = ({ ...useProps }: UseProps<VerifyEntryCodeRes>) => {
  const auth = useAuthStore();
  const vueRequest = requestProvider<VerifyEntryCodeRes, VerifyEntryCodeProps>(
    async (props: VerifyEntryCodeProps) => {
      const existingToken = await forage<string>().getItem('proxyToken');
      const lastEntryCode = await forage<string>().getItem('lastEntryCode');
      if (existingToken && lastEntryCode === props.entryCode) {
        // If token exists, return success directly without API call
        await auth.setProxyToken(existingToken);
        return {
          token: existingToken,
        };
      }

      const response = await externalAxiosProvider.post(
        '/proxy-orders/entry-code/verify',
        props
      );
      await auth.setProxyToken(response.data.token);
      await forage<string>().setItem('lastEntryCode', props.entryCode);
      return response.data;
    },
    {
      ...useProps,
      manual: true,
      onSuccess: (res) => {
        if (useProps.onSuccess) useProps.onSuccess(res);
      },
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
  return vueRequest;
};

export { useVerifyEntryCode };
