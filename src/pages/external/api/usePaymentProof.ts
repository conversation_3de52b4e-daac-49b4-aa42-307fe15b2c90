import { requestProvider } from 'src/utils/requestProvider';
import { externalAxiosProvider } from '../../../utils/externalAxiosProvider';

export type PaymentProofProps = {
  tradeId: number;
  file: File;
};

export type PaymentProofRes = {
  url: string;
};

const usePaymentProof = ({ ...useProps }: UseProps<PaymentProofRes>) => {
  const vueRequest = requestProvider<PaymentProofRes, PaymentProofProps>(
    async (props: PaymentProofProps) => {
      const formData = new FormData();
      formData.append('TradeId', String(props.tradeId));
      formData.append('File', props.file);

      const response = await externalAxiosProvider.post(
        '/trade-orders/payment-proof',
        formData
      );
      return response.data;
    },
    {
      ...useProps,
      manual: true,
      onSuccess: (res) => {
        if (useProps.onSuccess) useProps.onSuccess(res);
      },
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
  return vueRequest;
};

export { usePaymentProof };
