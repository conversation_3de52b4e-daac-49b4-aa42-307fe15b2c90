import { SenderRole, ChatMessageType } from 'src/utils/enums';
import { requestProvider } from 'src/utils/requestProvider';
import { externalAxiosProvider } from '../../../utils/externalAxiosProvider';

export interface Message {
  tradeId: number;
  messageId: number;
  senderId: string;
  senderName: string;
  senderRole: SenderRole;
  messageType: ChatMessageType;
  payload: string;
  caption: string | null;
  sentAt: string;
}

type MessagesRes = Array<Message>;

interface MessagesProps {
  tradeId?: number;
}

const useGetMessages = (useProps: MessagesProps) => {
  const vueRequest = requestProvider<MessagesRes, MessagesProps>(
    ({ tradeId }) => {
      const request = externalAxiosProvider
        .get(`/chat/${tradeId}/messages`)
        .then(({ data }) => data);

      return request;
    },
    {
      defaultParams: [useProps],
      manual: true,
    },
    {
      noFeedback: true,
      noErrorNotify: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export { useGetMessages };
