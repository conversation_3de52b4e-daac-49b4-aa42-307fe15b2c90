import { requestProvider } from 'src/utils/requestProvider';
import { externalAxiosProvider } from '../../../utils/externalAxiosProvider';

export type SendPaymentProofProps = {
  tradeId: number;
  file: File;
};

type SendPaymentProofRes = unknown;

const useSendPaymentProof = ({
  ...useProps
}: UseProps<SendPaymentProofRes>) => {
  const vueRequest = requestProvider<
    SendPaymentProofRes,
    SendPaymentProofProps
  >(
    (props: SendPaymentProofProps) => {
      const formData = new FormData();
      formData.append('TradeId', String(props.tradeId));
      formData.append('File', props.file);

      const request = externalAxiosProvider
        .post('/trade-orders/payment-proof', formData)
        .then(({ data }) => data);

      return request;
    },
    {
      ...useProps,
      manual: true,
      onSuccess: (res) => {
        if (useProps.onSuccess) useProps.onSuccess(res);
      },
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export { useSendPaymentProof };
