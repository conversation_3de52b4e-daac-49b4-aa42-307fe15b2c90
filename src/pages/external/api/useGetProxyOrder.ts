import {
  TradeDisputeStatusEnum,
  TradeModeEnum,
  TradeStatusEnum,
  TradeTypeEnum,
  WalletNetworkEnum,
} from 'src/utils/enums';
import { requestProvider } from 'src/utils/requestProvider';
import { externalAxiosProvider } from '../../../utils/externalAxiosProvider';

// Define the proxy order interface based on the actual API response
export interface ProxyOrder {
  id: number;
  fiatCurrency: string;
  fiatAmount: number;
  network: WalletNetworkEnum;
  networkDesc: string;
  cryptoSymbol: string;
  cryptoAmount: number;
  rate: number;
  agentFeeAmount: number;
  userPaidAt: string | null;
  completedAt: string | null;
  createdAt: string;
  updatedAt: string;
  userId: string;
  agentId: string;
  tradeNo: string;
  type: TradeTypeEnum;
  typeDesc: string;
  disputeStatus: TradeDisputeStatusEnum;
  disputeStatusDesc: string;
  mode: TradeModeEnum;
  modeDesc: string;
  status: TradeStatusEnum;
  statusDesc: string;
  bankName: string;
  accountName: string;
  accountNumber: string;
  branchCode: string;
  payerBankAccountName: string;
}

type ProxyOrderRes = ProxyOrder;

const useGetProxyOrder = () => {
  const vueRequest = requestProvider<ProxyOrderRes>(
    () => {
      const request = externalAxiosProvider
        .get('/proxy-orders/my')
        .then(({ data }) => data);

      return request;
    },
    {
      manual: true,
    },
    {
      noFeedback: true,
      noErrorNotify: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export { useGetProxyOrder };
