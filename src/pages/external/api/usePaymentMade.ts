import { requestProvider } from 'src/utils/requestProvider';
import { externalAxiosProvider } from '../../../utils/externalAxiosProvider';

export type PaymentMadeProps = {
  entryCode: string;
};

type PaymentMadeRes = unknown;

const usePaymentMade = ({ ...useProps }: UseProps<PaymentMadeRes>) => {
  const vueRequest = requestProvider<PaymentMadeRes, PaymentMadeProps>(
    (props: PaymentMadeProps) => {
      const request = externalAxiosProvider
        .put('/proxy-orders/payment-made', props)
        .then(({ data }) => data);

      return request;
    },
    {
      ...useProps,
      manual: true,
      onSuccess: (res) => {
        if (useProps.onSuccess) useProps.onSuccess(res);
      },
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
  return vueRequest;
};

export { usePaymentMade };
