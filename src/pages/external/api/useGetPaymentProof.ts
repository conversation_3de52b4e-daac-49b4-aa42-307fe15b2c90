import { requestProvider } from 'src/utils/requestProvider';
import { externalAxiosProvider } from '../../../utils/externalAxiosProvider';

interface PaymentProofRes {
  url: string;
}

interface PaymentProofProps {
  tradeId: number;
}

const useGetPaymentProof = (useProps: PaymentProofProps) => {
  const vueRequest = requestProvider<PaymentProofRes, PaymentProofProps>(
    (props) => {
      const request = externalAxiosProvider
        .get(`/trade-orders/${props.tradeId}/payment-proof`)
        .then(({ data }) => data);

      return request;
    },
    {
      defaultParams: [useProps],
      manual: true,
    },
    {
      noFeedback: true,
      noErrorNotify: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export { useGetPaymentProof };
export type { PaymentProofRes };
