import { requestProvider } from 'src/utils/requestProvider';
import { externalAxiosProvider } from '../../../utils/externalAxiosProvider';

export type SendChatImageProps = {
  tradeId: number;
  file: File;
  caption?: string;
};

type SendChatImageRes = unknown;

const useSendChatImage = ({ ...useProps }: UseProps<SendChatImageRes>) => {
  const vueRequest = requestProvider<SendChatImageRes, SendChatImageProps>(
    (props: SendChatImageProps) => {
      const formData = new FormData();
      formData.append('TradeId', String(props.tradeId));
      formData.append('File', props.file);
      if (props.caption) {
        formData.append('Caption', props.caption);
      }

      const request = externalAxiosProvider
        .post('/chat/upload-image', formData)
        .then(({ data }) => data);

      return request;
    },
    {
      ...useProps,
      manual: true,
      onSuccess: (res) => {
        if (useProps.onSuccess) useProps.onSuccess(res);
      },
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export { useSendChatImage };
