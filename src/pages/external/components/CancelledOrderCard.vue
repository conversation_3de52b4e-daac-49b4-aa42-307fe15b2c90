<template>
  <div
    class="cancelled-card q-mx-auto bg-white shadow-8 rounded-borders q-pa-none"
    style="max-width: 700px; border-radius: 16px; overflow: hidden; min-height: 680px;"
  >
    <!-- Cancelled Banner -->
    <div
      class="cancelled-banner bg-gradient text-white q-pa-xl text-center relative-position"
      style="
        background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        height: 220px;
      "
    >
      <div class="absolute-center">
        <q-icon name="cancel" size="5rem" class="q-mb-md" />
        <div class="text-h4 text-weight-bold q-mb-sm">
          {{ t('externalPaymentDetailPage.orderCancelled') }}
        </div>
        <div class="text-body1 q-mt-sm opacity-90">
          {{ t('externalPaymentDetailPage.orderCancelledSubtitle') }}
        </div>
      </div>
    </div>

    <!-- Cancelled Details Container -->
    <div 
      class="cancelled-container q-pa-xl" 
      style="min-height: 460px; display: flex; flex-direction: column; justify-content: space-between;"
    >
      <!-- Order Information Section -->
      <div class="order-info-section">
        <!-- Order Number Card -->
        <div class="order-reference-card bg-grey-1 rounded-borders q-pa-lg q-mb-xl">
          <div class="row items-center justify-between">
            <div class="col">
              <div class="text-caption text-grey-7 q-mb-xs">
                {{ t('externalPaymentDetailPage.orderNumber') }}
              </div>
              <div class="text-h6 text-weight-bold text-grey-9">
                {{ orderData.orderNumber }}
              </div>
            </div>
            <div class="col-auto">
              <q-btn
                flat
                round
                color="grey-7"
                icon="content_copy"
                size="md"
                @click="copyOrderNumber"
              >
                <q-tooltip>{{ t('externalPaymentDetailPage.copyText') }}</q-tooltip>
              </q-btn>
            </div>
          </div>
        </div>

        <!-- Cancellation Info -->
        <div class="cancellation-info text-center q-mb-xl">
          <div class="text-h6 text-grey-8 q-mb-md">
            {{ t('externalPaymentDetailPage.whatHappened') }}
          </div>
          <div class="text-body1 text-grey-6 line-height-lg">
            {{ t('externalPaymentDetailPage.orderCancelledExplanation') }}
          </div>
        </div>
      </div>

      <!-- Help Section -->
      <div class="help-section-modern text-center">
        <div class="help-card bg-blue-1 rounded-borders q-pa-lg">
          <q-icon
            name="support_agent"
            color="blue-8"
            size="2.5rem"
            class="q-mb-md"
          />
          <div class="text-subtitle1 text-weight-medium text-blue-9 q-mb-sm">
            {{ t('externalPaymentDetailPage.needAssistance') }}
          </div>
          <div class="text-body2 text-blue-7">
            {{ t('externalPaymentDetailPage.contactSupport') }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { copyToClipboard, useQuasar } from 'quasar';

interface OrderData {
  orderNumber: string;
}

interface Props {
  orderData: OrderData;
}

defineProps<Props>();
defineEmits<{
  copyOrderNumber: [];
}>();

const { t } = useI18n();
const q = useQuasar();

const copyOrderNumber = () => {
  // This will be handled by parent component
};
</script>

<style scoped>
.line-height-lg {
  line-height: 1.6;
}
</style>
