<template>
  <div
    class="order-summary-card bg-white shadow-8 rounded-borders q-mb-lg"
    style="border-radius: 16px; overflow: hidden"
  >
    <!-- Header Banner -->
    <div
      class="order-header bg-gradient text-white q-pa-md"
      style="
        background: linear-gradient(135deg, #3949ab 0%, #5c6bc0 100%);
        position: relative;
        overflow: hidden;
      "
    >
      <div class="row items-center justify-between q-py-sm">
        <div class="col-auto">
          <div class="text-h6 text-weight-medium">
            {{ t('externalPaymentDetailPage.orderDetails') }}
          </div>
          <div class="text-caption q-mt-xs">
            {{ orderData.orderTime }}
          </div>
        </div>

        <div class="col-auto flex justify-center items-center gap-md">
          <div v-if="orderData.disputeStatus !== 'None'">
            <q-icon name="warning" color="warning">
              <q-tooltip anchor="top middle" self="bottom middle">
                There is a dispute on this transaction.
              </q-tooltip>
            </q-icon>
          </div>
          <q-badge
            :color="getStatusColor(orderData.orderStatus)"
            text-color="white"
            class="q-pa-sm"
          >
            <trade-status-label
              :status="
                stringToEnumValue(
                  TradeStatusEnum,
                  orderData.orderStatus
                )
              "
            />
          </q-badge>
        </div>
      </div>
      <!-- Decorative elements -->
      <div
        class="header-decoration"
        style="
          position: absolute;
          top: -30px;
          right: -30px;
          width: 120px;
          height: 120px;
          border-radius: 50%;
          opacity: 0.1;
          background: radial-gradient(
            circle,
            #ffffff 0%,
            transparent 70%
          );
        "
      ></div>
      <div
        class="header-decoration"
        style="
          position: absolute;
          bottom: -40px;
          left: -40px;
          width: 100px;
          height: 100px;
          border-radius: 50%;
          opacity: 0.1;
          background: radial-gradient(
            circle,
            #ffffff 0%,
            transparent 70%
          );
        "
      ></div>
    </div>

    <!-- Order Overview -->
    <div class="order-overview q-pa-lg">
      <!-- Amount Cards -->
      <div class="row q-col-gutter-md q-mb-md">
        <!-- Payment amount -->
        <div class="col-12 col-md-6">
          <div
            class="amount-card bg-indigo-1 rounded-borders q-pa-md"
            style="border-radius: 12px"
          >
            <div class="row items-center">
              <div class="col">
                <div class="text-caption text-grey-8">
                  {{ t('externalPaymentDetailPage.paymentAmount') }}
                </div>
                <div
                  class="text-h5 flex items-center text-weight-bold text-indigo-9 q-mt-xs"
                >
                  {{ orderData.paymentAmount }}
                  <q-btn
                    flat
                    round
                    dense
                    color="grey-7"
                    icon="content_copy"
                    size="sm"
                    class="q-ml-xs"
                    @click="copyFiatAmount"
                  />
                </div>
              </div>

              <div class="col-auto">
                <q-icon
                  name="payments"
                  color="indigo-5"
                  size="2rem"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Crypto amount -->
        <div class="col-12 col-md-6">
          <div
            class="amount-card bg-blue-1 rounded-borders q-pa-md"
            style="border-radius: 12px"
          >
            <div class="row items-center">
              <div class="col">
                <div class="text-caption text-grey-8">
                  {{ t('externalPaymentDetailPage.cryptoAmount') }}
                </div>
                <div
                  class="text-h5 text-weight-bold text-blue-9 q-mt-xs"
                >
                  {{ orderData.cryptoAmount }}
                </div>
              </div>
              <div class="col-auto">
                <q-icon
                  name="currency_bitcoin"
                  color="blue-5"
                  size="2rem"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Divider -->
      <q-separator class="q-my-md" />

      <!-- Transaction Details -->
      <div class="transaction-details q-pa-md">
        <div class="details-list">
          <!-- Header -->
          <div class="payment-header q-pt-md q-mb-md">
            <div class="row items-center">
              <q-icon
                name="account_balance"
                color="primary"
                size="1.8rem"
                class="q-mr-sm"
              />
              <div class="text-h6 text-weight-medium text-primary">
                {{
                  t('externalPaymentDetailPage.paymentDetailsTitle')
                }}
              </div>
            </div>
            <div class="text-caption text-grey-7 q-mt-sm">
              {{
                t('externalPaymentDetailPage.paymentDetailsSubtitle')
              }}
            </div>
          </div>

          <!-- Bank Info Card -->
          <div
            class="bank-info bg-blue-1 rounded-borders q-pa-md q-mb-md"
            style="border-radius: 12px"
          >
            <div class="row q-col-gutter-md">
              <div class="col-12 col-sm-6">
                <!-- Account Number Card with Copy Button -->
                <div
                  class="account-number-card rounded-borders"
                  style="border-radius: 12px"
                >
                  <div class="text-caption text-grey-7">
                    Bank Name
                  </div>
                  <div
                    class="row items-center justify-between q-mt-sm"
                  >
                    <div class="text-body1 text-weight-bold">
                      {{ orderData.recipientBank }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-12 col-sm-6">
                <!-- Account Number Card with Copy Button -->
                <div
                  class="account-number-card rounded-borders"
                  style="border-radius: 12px"
                >
                  <div class="text-caption text-grey-7">
                    {{
                      t(
                        'externalPaymentDetailPage.paymentDetailsAccountNumber'
                      )
                    }}
                  </div>
                  <div
                    class="row items-center justify-between q-mt-sm"
                  >
                    <div class="text-body1 text-weight-bold">
                      {{ orderData.recipientAccount }}
                    </div>
                    <div class="col-auto">
                      <q-btn
                        flat
                        round
                        dense
                        icon="content_copy"
                        size="xs"
                        color="grey-7"
                        @click="copyRecipientAccount"
                      >
                        <q-tooltip>{{
                          t(
                            'externalPaymentDetailPage.copyRecipientAccount'
                          )
                        }}</q-tooltip>
                      </q-btn>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="row q-col-gutter-md">
              <div class="col-12 col-sm-6">
                <div class="text-caption text-grey-7">
                  {{
                    t(
                      'externalPaymentDetailPage.paymentDetailsBranchCode'
                    )
                  }}
                </div>
                <div class="text-body2 text-weight-medium q-mt-xs">
                  {{ orderData.recipientBranchCode }}
                </div>
              </div>
              <div class="col-12 col-sm-6">
                <div class="text-caption text-grey-7">
                  {{
                    t(
                      'externalPaymentDetailPage.paymentDetailsAccountName'
                    )
                  }}
                </div>
                <div
                  class="text-body2 text-weight-medium q-mt-xs text-truncate"
                >
                  {{ orderData.recipientName }}
                </div>
              </div>
            </div>
          </div>

          <!-- Recipient Information -->
          <div class="recipient-info q-mt-md q-pb-md">
            <div
              class="text-subtitle1 text-weight-medium text-grey-8 q-mb-sm"
            >
              {{ t('externalPaymentDetailPage.recipientInfo') }}
            </div>

            <div class="row q-col-gutter-md">
              <!-- Recipient Name -->
              <div class="col-12">
                <div
                  class="recipient-account bg-grey-1 rounded-borders q-pa-md"
                >
                  <div class="row items-center justify-between">
                    <div class="col">
                      <div class="text-caption text-grey-7">
                        {{
                          t('externalPaymentDetailPage.recipientName')
                        }}
                      </div>
                      <div
                        class="text-body2 text-weight-medium q-mt-xs"
                      >
                        {{ orderData.payerBankAccountName }}
                      </div>
                    </div>
                    <div class="col-auto">
                      <q-btn
                        flat
                        round
                        dense
                        icon="content_copy"
                        size="xs"
                        color="grey-7"
                        @click="copyPayerBankAccountName"
                      >
                        <q-tooltip>{{
                          t(
                            'externalPaymentDetailPage.copyPayerBankAccountName'
                          )
                        }}</q-tooltip>
                      </q-btn>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { copyToClipboard, useQuasar } from 'quasar';
import { stringToEnumValue, TradeStatusEnum } from 'src/utils/enums';
import TradeStatusLabel from 'src/components/TradeStatusLabel.vue';

interface OrderData {
  orderTime: string;
  paymentAmount: string;
  cryptoAmount: string;
  recipientBank: string;
  recipientAccount: string;
  recipientName: string;
  recipientBranchCode: string;
  payerBankAccountName: string;
  orderStatus: string;
  disputeStatus: string;
}

interface Props {
  orderData: OrderData;
}

defineProps<Props>();

const { t } = useI18n();
const q = useQuasar();

const getStatusColor = (status: string) => {
  switch (status) {
    case 'Completed':
      return 'positive';
    case 'BankAccountAssigned':
      return 'blue-7';
    case 'Pending':
    case 'PaymentMade':
      return 'orange-8';
    case 'Cancelled':
    case 'Expired':
      return 'negative';
    default:
      return 'grey-7';
  }
};

const copyFiatAmount = () => {
  // Implementation will be passed from parent
};

const copyRecipientAccount = () => {
  // Implementation will be passed from parent
};

const copyPayerBankAccountName = () => {
  // Implementation will be passed from parent
};
</script>
