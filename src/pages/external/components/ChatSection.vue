<template>
  <div
    class="chat-section bg-white shadow-8 rounded-borders q-pa-none"
    style="border-radius: 16px; overflow: hidden; min-height: 680px"
  >
    <!-- Chat Header -->
    <div
      class="chat-header bg-gradient text-white q-pa-md"
      style="
        background: linear-gradient(135deg, #607d8b 0%, #78909c 100%);
        position: relative;
        overflow: hidden;
      "
    >
      <div class="row items-center">
        <q-icon name="chat" size="1.5rem" class="q-mr-sm" />
        <div class="text-h6 text-weight-medium">
          {{ t('externalPaymentDetailPage.chatTitle') }}
        </div>
      </div>
      <div class="text-caption q-mt-xs opacity-80">
        {{ t('externalPaymentDetailPage.chatSubtitle') }}
      </div>
    </div>

    <!-- Chat Messages Container -->
    <div class="chat-container" style="height: 500px; display: flex; flex-direction: column;">
      <!-- Messages Area -->
      <div class="chat-messages q-px-sm q-py-md" ref="chatMessagesRef" style="flex: 1; overflow-y: auto;">
        <div
          v-for="(message, index) in chatMessages"
          :key="index"
          :class="[
            'message-item q-mb-md',
            getMessageClass(message.senderRole),
          ]"
        >
          <!-- Support Team Label -->
          <div
            v-if="message.senderRole === SenderRole.SupportTeam"
            class="support-team-label q-mb-xs"
          >
            <q-chip
              color="purple"
              text-color="white"
              icon="support_agent"
              size="sm"
              class="support-chip"
            >
              Support Team
            </q-chip>
          </div>
          
          <!-- Agent Label -->
          <div
            v-else-if="message.senderRole === SenderRole.Agent"
            class="agent-label q-mb-xs"
          >
            <q-chip
              color="green"
              text-color="white"
              icon="person"
              size="sm"
              class="agent-chip"
            >
              Agent
            </q-chip>
          </div>

          <div class="message-content q-pa-md">
            <!-- Text message -->
            <div v-if="!message.isImage" class="message-text">
              {{ message.content }}
            </div>
            <!-- Image message -->
            <div v-else class="image-message">
              <q-img
                :src="message.content"
                spinner-color="primary"
                style="width: 200px; height: 200px; border-radius: 8px; cursor: pointer"
                @load="scrollToBottom"
                @click="openImagePreview(message.content)"
                fit="contain"
                class="message-image"
              >
                <div class="absolute-bottom text-subtitle2 text-center">
                  <q-icon name="zoom_in" size="sm" />
                  Click to view
                </div>
              </q-img>
            </div>
          </div>
          <div class="message-time text-caption">
            {{ message.time }}
          </div>
        </div>
      </div>

      <!-- Chat Input Area -->
      <div class="chat-input bg-grey-1 q-pa-md">
        <div class="row q-col-gutter-sm">
          <div class="col">
            <q-input
              v-model="newMessage"
              :placeholder="t('externalPaymentDetailPage.chatPlaceholder')"
              outlined
              dense
              @keyup.enter="sendMessage"
              style="border-radius: 20px"
            />
          </div>
          <div class="col-auto">
            <q-btn
              color="primary"
              icon="send"
              round
              @click="sendMessage"
              :disable="!newMessage.trim()"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';

enum SenderRole {
  User = 'User',
  Agent = 'Agent',
  SupportTeam = 'SupportTeam',
}

interface ChatMessage {
  content: string;
  time: string;
  senderRole: SenderRole;
  isImage: boolean;
}

interface Props {
  chatMessages: ChatMessage[];
}

defineProps<Props>();
defineEmits<{
  sendMessage: [message: string];
  imagePreview: [imageUrl: string];
}>();

const { t } = useI18n();
const q = useQuasar();

const newMessage = ref('');
const chatMessagesRef = ref<HTMLElement>();

// Get message class based on sender role
const getMessageClass = (senderRole: SenderRole) => {
  switch (senderRole) {
    case SenderRole.SupportTeam:
      return 'support-team-message';
    case SenderRole.Agent:
      return 'agent-message';
    case SenderRole.User:
      return 'user-message';
    default:
      return 'user-message';
  }
};

// Scroll to bottom of chat
const scrollToBottom = () => {
  nextTick(() => {
    if (chatMessagesRef.value) {
      chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight;
    }
  });
};

// Send message
const sendMessage = () => {
  if (newMessage.value.trim()) {
    // Emit to parent
    newMessage.value = '';
  }
};

// Open image preview
const openImagePreview = (imageUrl: string) => {
  q.dialog({
    title: t('externalPaymentDetailPage.imagePreview'),
    message: `
      <div class="q-pa-md text-center">
        <img
          src="${imageUrl}"
          style="max-width: 100%; max-height: 70vh; border-radius: 8px; object-fit: contain;"
          alt="Preview"
        />
      </div>
    `,
    html: true,
    style: 'max-width: 90vw; width: 100%;',
    ok: {
      label: t('externalPaymentDetailPage.closeButton'),
      color: 'primary',
    },
  });
};

// Expose methods to parent
defineExpose({
  scrollToBottom,
});
</script>

<style scoped>
.message-item {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 16px;
}

.message-content {
  border-radius: 16px;
  word-break: break-word;
  width: fit-content;
  max-width: 75%;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.message-content:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.message-text {
  line-height: 1.4;
  font-size: 0.95rem;
}

.image-message {
  position: relative;
}

.message-image {
  transition: transform 0.2s ease;
}

.message-image:hover {
  transform: scale(1.02);
}

/* Support Team Messages */
.support-team-message {
  align-items: flex-start;
}

.support-team-message .message-content {
  background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
  color: white;
  border-top-left-radius: 4px;
  box-shadow: 0 3px 12px rgba(156, 39, 176, 0.4);
  border: 2px solid rgba(156, 39, 176, 0.3);
}

.support-chip {
  font-size: 0.75rem;
  font-weight: 600;
}

/* Agent Messages */
.agent-message {
  align-items: flex-start;
}

.agent-message .message-content {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  color: white;
  border-top-left-radius: 4px;
  box-shadow: 0 2px 10px rgba(76, 175, 80, 0.3);
  border: 1px solid rgba(76, 175, 80, 0.2);
}

.agent-chip {
  font-size: 0.75rem;
  font-weight: 600;
}

/* User Messages */
.user-message {
  align-items: flex-end;
}

.user-message .message-content {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  border-top-right-radius: 4px;
  box-shadow: 0 2px 10px rgba(33, 150, 243, 0.3);
}

/* Dark mode adjustments */
body.isDark .support-team-message .message-content {
  background: linear-gradient(135deg, #7b1fa2 0%, #4a148c 100%);
  box-shadow: 0 3px 12px rgba(123, 31, 162, 0.5);
}

body.isDark .agent-message .message-content {
  background: linear-gradient(135deg, #388e3c 0%, #2e7d32 100%);
  box-shadow: 0 2px 10px rgba(56, 142, 60, 0.4);
}

body.isDark .user-message .message-content {
  background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
  box-shadow: 0 2px 10px rgba(25, 118, 210, 0.4);
}
</style>
