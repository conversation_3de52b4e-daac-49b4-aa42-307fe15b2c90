<template>
  <div class="action-buttons-section">
    <!-- Confirm Payment Button -->
    <q-btn
      :color="getConfirmButtonColor()"
      class="full-width q-py-sm q-mb-md modern-submit-btn"
      style="border-radius: 12px; font-weight: 600; height: 56px; position: relative; overflow: hidden;"
      :label="getConfirmButtonLabel()"
      :icon="getConfirmButtonIcon()"
      @click="handleConfirmPayment"
      :disable="confirmedPaid"
      :loading="getConfirmButtonLoading()"
      no-caps
    >
      <!-- Loading overlay with text -->
      <div 
        v-if="getConfirmButtonLoading()" 
        class="loading-overlay absolute-full flex items-center justify-center"
        style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(2px);"
      >
        <q-spinner-dots color="white" size="1.5em" class="q-mr-sm" />
        <span class="text-body2 text-weight-medium">{{ getLoadingText() }}</span>
      </div>
      
      <!-- Success animation overlay -->
      <div 
        v-if="confirmedPaid && !getConfirmButtonLoading()" 
        class="success-overlay absolute-full flex items-center justify-center"
        style="background: rgba(76, 175, 80, 0.1);"
      >
        <q-icon name="check_circle" size="1.2em" class="q-mr-xs" />
        <span class="text-body2 text-weight-medium">{{ t('externalPaymentDetailPage.confirmed') }}</span>
      </div>
    </q-btn>

    <!-- Upload Payment Proof Button -->
    <q-btn
      color="secondary"
      outline
      class="full-width q-py-sm"
      style="border-radius: 8px; font-weight: 500; height: 48px"
      :label="t('externalPaymentDetailPage.uploadPaymentProofButton')"
      icon="cloud_upload"
      @click="handleUploadPaymentProof"
      :disable="!confirmedPaid"
      no-caps
    />
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

interface Props {
  tradeStatus: string;
  confirmedPaid: boolean;
  loading: boolean;
}

defineProps<Props>();
defineEmits<{
  confirmPayment: [];
  uploadPaymentProof: [];
}>();

const { t } = useI18n();

// Get confirm button label based on status
const getConfirmButtonLabel = () => {
  const props = defineProps<Props>();
  if (props.tradeStatus === 'PaymentMade') {
    return t('externalPaymentDetailPage.waitingForConfirmation');
  }
  return !props.confirmedPaid
    ? t('externalPaymentDetailPage.confirmPaymentButton')
    : t('externalPaymentDetailPage.confirmed');
};

// Get confirm button icon based on status
const getConfirmButtonIcon = () => {
  const props = defineProps<Props>();
  if (props.tradeStatus === 'PaymentMade') {
    return 'hourglass_empty';
  }
  return props.confirmedPaid ? 'check_circle' : '';
};

// Get confirm button loading state
const getConfirmButtonLoading = () => {
  const props = defineProps<Props>();
  if (props.tradeStatus === 'PaymentMade') {
    return true; // Show loading animation for waiting state
  }
  return props.loading;
};

// Get confirm button color based on status
const getConfirmButtonColor = () => {
  const props = defineProps<Props>();
  if (props.confirmedPaid) {
    return 'positive';
  }
  if (props.tradeStatus === 'PaymentMade') {
    return 'orange';
  }
  return 'primary';
};

// Get loading text based on status
const getLoadingText = () => {
  const props = defineProps<Props>();
  if (props.tradeStatus === 'PaymentMade') {
    return t('externalPaymentDetailPage.processingConfirmation');
  }
  return t('externalPaymentDetailPage.processing');
};

const handleConfirmPayment = () => {
  // Emit to parent
};

const handleUploadPaymentProof = () => {
  // Emit to parent
};
</script>

<style scoped>
/* Modern Submit Button Styles */
.modern-submit-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modern-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.modern-submit-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.loading-overlay {
  animation: pulse 2s infinite;
}

.success-overlay {
  animation: successPulse 0.6s ease-out;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes successPulse {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
