<template>
  <!-- Payment Notice and Time Remaining -->
  <div
    :class="[
      'payment-notice rounded-borders q-pa-lg q-mb-lg',
      getPaymentNoticeClass()
    ]"
    style="border-radius: 16px; position: relative"
  >
    <div class="row">
      <div class="col-auto q-mr-md">
        <q-icon 
          :name="getPaymentNoticeIcon()" 
          :color="getPaymentNoticeIconColor()" 
          size="2rem" 
        />
      </div>
      <div class="col">
        <div 
          class="text-subtitle1 text-weight-bold" 
          :class="getPaymentNoticeTitleColor()"
        >
          {{ getPaymentNoticeTitle() }}
        </div>
        <div class="text-body2 q-mt-sm text-grey-8">
          {{ getStatusMessage() }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

interface Props {
  tradeStatus: string;
}

defineProps<Props>();

const { t } = useI18n();

// Get payment notice class based on status
const getPaymentNoticeClass = () => {
  const props = defineProps<Props>();
  switch (props.tradeStatus) {
    case 'PaymentMade':
      return 'bg-blue-1';
    case 'Completed':
      return 'bg-green-1';
    case 'Cancelled':
    case 'Expired':
      return 'bg-red-1';
    default:
      return 'bg-orange-1';
  }
};

// Get payment notice icon based on status
const getPaymentNoticeIcon = () => {
  const props = defineProps<Props>();
  switch (props.tradeStatus) {
    case 'PaymentMade':
      return 'hourglass_empty';
    case 'Completed':
      return 'check_circle';
    case 'Cancelled':
    case 'Expired':
      return 'cancel';
    default:
      return 'schedule';
  }
};

// Get payment notice icon color based on status
const getPaymentNoticeIconColor = () => {
  const props = defineProps<Props>();
  switch (props.tradeStatus) {
    case 'PaymentMade':
      return 'blue-8';
    case 'Completed':
      return 'green-8';
    case 'Cancelled':
    case 'Expired':
      return 'red-8';
    default:
      return 'orange-8';
  }
};

// Get payment notice title color based on status
const getPaymentNoticeTitleColor = () => {
  const props = defineProps<Props>();
  switch (props.tradeStatus) {
    case 'PaymentMade':
      return 'text-blue-9';
    case 'Completed':
      return 'text-green-9';
    case 'Cancelled':
    case 'Expired':
      return 'text-red-9';
    default:
      return 'text-orange-9';
  }
};

// Get payment notice title based on status
const getPaymentNoticeTitle = () => {
  const props = defineProps<Props>();
  switch (props.tradeStatus) {
    case 'PaymentMade':
      return t('externalPaymentDetailPage.waitingForConfirmation');
    case 'Completed':
      return t('externalPaymentDetailPage.orderCompleted');
    case 'Cancelled':
      return t('externalPaymentDetailPage.orderCancelled');
    case 'Expired':
      return t('externalPaymentDetailPage.orderExpired');
    default:
      return t('externalPaymentDetailPage.paymentNotice');
  }
};

// Get status-based message
const getStatusMessage = () => {
  const props = defineProps<Props>();
  switch (props.tradeStatus) {
    case 'PaymentMade':
      return t('externalPaymentDetailPage.waitingForConfirmationMessage');
    case 'Cancelled':
      return t('externalPaymentDetailPage.orderCancelledMessage');
    case 'Completed':
      return t('externalPaymentDetailPage.orderCompletedMessage');
    default:
      return t('externalPaymentDetailPage.paymentInstructions');
  }
};
</script>
