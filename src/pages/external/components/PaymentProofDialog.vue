<template>
  <q-dialog v-model="isOpen" persistent>
    <q-card style="min-width: 400px; max-width: 500px">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">
          {{ t('externalPaymentDetailPage.uploadPaymentProofTitle') }}
        </div>
        <q-space />
        <q-btn icon="close" flat round dense @click="closeDialog" />
      </q-card-section>

      <q-card-section>
        <div class="text-body2 text-grey-7 q-mb-md">
          {{ t('externalPaymentDetailPage.uploadPaymentProofDescription') }}
        </div>

        <!-- File Upload Area -->
        <div
          class="upload-area q-pa-lg text-center"
          :class="{ 'drag-over': isDragOver }"
          @dragover.prevent="isDragOver = true"
          @dragleave.prevent="isDragOver = false"
          @drop.prevent="handleFileDrop"
          style="
            border: 2px dashed #ccc;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
          "
          @click="triggerFileInput"
        >
          <q-icon
            name="cloud_upload"
            size="3rem"
            color="grey-5"
            class="q-mb-md"
          />
          <div class="text-body1 text-grey-7 q-mb-sm">
            {{ t('externalPaymentDetailPage.dragDropOrClick') }}
          </div>
          <div class="text-caption text-grey-5">
            {{ t('externalPaymentDetailPage.supportedFormats') }}
          </div>
        </div>

        <!-- Hidden File Input -->
        <input
          ref="fileInput"
          type="file"
          accept="image/*"
          style="display: none"
          @change="handleFileSelect"
        />

        <!-- Preview Area -->
        <div v-if="selectedFile" class="preview-area q-mt-md">
          <div class="text-subtitle2 q-mb-sm">
            {{ t('externalPaymentDetailPage.preview') }}
          </div>
          <div class="preview-container bg-grey-1 rounded-borders q-pa-md">
            <div class="row items-center">
              <div class="col-auto q-mr-md">
                <q-img
                  :src="previewUrl"
                  style="width: 80px; height: 80px; border-radius: 4px"
                  fit="cover"
                />
              </div>
              <div class="col">
                <div class="text-body2 text-weight-medium">
                  {{ selectedFile.name }}
                </div>
                <div class="text-caption text-grey-6">
                  {{ formatFileSize(selectedFile.size) }}
                </div>
              </div>
              <div class="col-auto">
                <q-btn
                  flat
                  round
                  dense
                  icon="close"
                  color="grey-7"
                  @click="removeFile"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Upload Progress -->
        <div v-if="isUploading" class="upload-progress q-mt-md">
          <div class="text-body2 q-mb-sm">
            {{ t('externalPaymentDetailPage.uploading') }}...
          </div>
          <q-linear-progress
            :value="uploadProgress"
            color="primary"
            size="8px"
            rounded
          />
          <div class="text-caption text-center q-mt-xs">
            {{ Math.round(uploadProgress * 100) }}%
          </div>
        </div>
      </q-card-section>

      <q-card-actions align="right" class="q-pa-md">
        <q-btn
          flat
          :label="t('externalPaymentDetailPage.cancelButton')"
          @click="closeDialog"
          :disable="isUploading"
        />
        <q-btn
          color="primary"
          :label="t('externalPaymentDetailPage.uploadButton')"
          @click="uploadFile"
          :disable="!selectedFile || isUploading"
          :loading="isUploading"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';

interface Props {
  modelValue: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  upload: [file: File];
}>();

const { t } = useI18n();

const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const selectedFile = ref<File | null>(null);
const previewUrl = ref<string>('');
const isDragOver = ref(false);
const isUploading = ref(false);
const uploadProgress = ref(0);
const fileInput = ref<HTMLInputElement>();

const triggerFileInput = () => {
  fileInput.value?.click();
};

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    setSelectedFile(file);
  }
};

const handleFileDrop = (event: DragEvent) => {
  isDragOver.value = false;
  const file = event.dataTransfer?.files[0];
  if (file && file.type.startsWith('image/')) {
    setSelectedFile(file);
  }
};

const setSelectedFile = (file: File) => {
  selectedFile.value = file;
  previewUrl.value = URL.createObjectURL(file);
};

const removeFile = () => {
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value);
  }
  selectedFile.value = null;
  previewUrl.value = '';
  if (fileInput.value) {
    fileInput.value.value = '';
  }
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const uploadFile = async () => {
  if (!selectedFile.value) return;

  isUploading.value = true;
  uploadProgress.value = 0;

  try {
    // Simulate upload progress
    const interval = setInterval(() => {
      uploadProgress.value += 0.1;
      if (uploadProgress.value >= 1) {
        clearInterval(interval);
        emit('upload', selectedFile.value!);
        closeDialog();
      }
    }, 100);
  } catch (error) {
    console.error('Upload failed:', error);
    isUploading.value = false;
  }
};

const closeDialog = () => {
  if (!isUploading.value) {
    removeFile();
    isOpen.value = false;
  }
};
</script>

<style scoped>
.upload-area.drag-over {
  border-color: #1976d2;
  background-color: rgba(25, 118, 210, 0.05);
}

.upload-area:hover {
  border-color: #1976d2;
  background-color: rgba(25, 118, 210, 0.02);
}
</style>
