import { CurrencyCode } from 'src/api';
import { axiosProvider } from 'src/utils/axiosProvider';
import {
  FlowTypeEnum,
  LedgerSourceTypeEnum,
  WalletNetworkEnum,
} from 'src/utils/enums';
import { requestProvider } from 'src/utils/requestProvider';

interface HistoryProps {
  pageNumber?: number | null;
  pageSize?: number | null;
  orderBy?: 'Id' | null;
  orderByDescending?: boolean | null;
  startTime?: string;
  endTime?: string;
  flowTypes?: Array<FlowTypeEnum>;
}
interface History {
  id: number;
  ledgerSourceType: LedgerSourceTypeEnum;
  sourceId: string;
  changeAmount: number;
  balanceAfter: number;
  flowType: FlowTypeEnum;
  flowTypeDesc: string;
  timestamp: string;
  network: WalletNetworkEnum;
  cryptoSymbol: string;
  note: string;
  fiatSymbol: CurrencyCode['code'];
  fiatAmount: number;
  counterpartyName: string;
  userRate: number;
}
interface HistoryRes {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: Array<History>;
}
const useHistory = () => {
  const vueRequest = requestProvider<HistoryRes, HistoryProps>(
    (props) => {
      const flowTypesQuery = props.flowTypes
        ?.map((flowType) => `flowTypes=${flowType}`)
        .join('&');
      const request = axiosProvider
        .get(`/usdtledger/my?${flowTypesQuery}`, {
          params: { ...props, flowTypes: undefined },
        })
        .then(({ data }) => data);

      return request;
    },
    {
      manual: true,
    },
    {
      noFeedback: true,
      noErrorNotify: true,
      noTempData: true,
    }
  );
  return vueRequest;
};
export { useHistory };
export type { History };
