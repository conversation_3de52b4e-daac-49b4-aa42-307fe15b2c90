import { Trade } from 'src/pages/trade/api';
import { axiosProvider } from 'src/utils/axiosProvider';
import {
  ChainTransferDirectionEnum,
  ChainTxStatusEnum,
  LedgerSourceTypeEnum,
  WalletNetworkEnum,
} from 'src/utils/enums';
import { requestProvider } from 'src/utils/requestProvider';

interface HistoryDetailsProps {
  id: number;
}

interface ChainTxLog {
  email: string;
  id: number;
  orderNumber: string;
  txHash: string;
  fromAddress: string;
  toAddress: string;
  amount: number;
  fee: number;
  symbol: string;
  network: WalletNetworkEnum;
  confirmedAt: string;
  direction: ChainTransferDirectionEnum;
  status: ChainTxStatusEnum;
  createdAt: string;
  updatedAt: string;
}

interface HistoryDetailsRes {
  ledgerSourceType: LedgerSourceTypeEnum;
  trade: Trade | null;
  chainTxLog: ChainTxLog | null;
}

const useHistoryDetails = () => {
  const vueRequest = requestProvider<HistoryDetailsRes, HistoryDetailsProps>(
    (props) => {
      const request = axiosProvider
        .get(`/usdtledger/my/${props.id}`)
        .then(({ data }) => data);

      return request;
    },
    {
      manual: true,
    },
    {
      noFeedback: true,
      noErrorNotify: true,
      noTempData: true,
    }
  );
  return vueRequest;
};
export { useHistoryDetails };
export type { HistoryDetailsRes };
