<template>
  <q-page padding class="container q-py-lg flex items-center column gap-y-md">
    <breadcrumb-component :label="t('historyPage.title')" class="self-start" />

    <div
      class="self-start"
      :class="q.screen.lt.sm ? 'full-width q-px-sm' : 'date-picker-wrapper'"
    >
      <date-picker
        :from="dateRange.from"
        :to="dateRange.to"
        @submit="
          (newRange: DateRange) => {
            dateRange.from = newRange.from;
            dateRange.to = newRange.to;
            router.replace({
              query: {
                ...route.query,
                from: newRange.from.format('YYYY-MM-DD'),
                to: newRange.to.format('YYYY-MM-DD'),
              },
            });
          }
        "
      />
    </div>

    <PaginationComponent
      v-model="currentPage"
      :page-size="pageSize"
      :total-pages="historyList?.totalPages || 1"
      :total-count="historyList?.totalCount"
      @update:modelValue="setPage"
      @update:pageSize="setPageSize"
      class="full-width"
    />

    <div :class="['full-width', q.screen.lt.sm ? 'q-px-sm' : '']">
      <q-card :class="['column', q.screen.lt ? 'q-pa-sm' : 'q-pa-md']">
        <q-tabs v-model="tabs" align="justify" class="full-width">
          <q-tab name="all" class="full-width">{{
            t('historyPage.all')
          }}</q-tab>
          <q-tab name="buy" class="full-width text-info">{{
            t('historyPage.buy')
          }}</q-tab>
          <q-tab name="sell" class="full-width text-negative">{{
            t('historyPage.sell')
          }}</q-tab>
          <q-tab name="deposit" class="full-width text-accent">{{
            t('historyPage.deposit')
          }}</q-tab>
          <q-tab name="withdrawal" class="full-width text-accent">{{
            t('historyPage.withdrawal')
          }}</q-tab>
        </q-tabs>
        <q-separator />
        <q-tab-panels v-model="tabs" animated class="q-pt-sm">
          <q-tab-panel
            v-for="tabName in ['all', 'buy', 'sell', 'deposit', 'withdrawal'] as Array<Tabs>"
            :key="tabName"
            :name="tabName"
            class="q-pa-none"
          >
            <template v-if="tabName === tabs">
              <loading-component v-if="loadingHistoryList" />
              <empty-master
                v-else-if="!historyList.items?.length"
                :message="t('historyPage.emptyHistory')"
                icon="data_object"
              />
              <HistoryList v-else :historyList="historyList.items" />
            </template>
          </q-tab-panel>
        </q-tab-panels>
      </q-card>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { reactive, ref, watch, type Ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import dayjs from 'dayjs';
import usePagination from 'src/hooks/usePagination';
import { useHistory } from './api';
import { FlowTypeEnum } from 'src/utils/enums';
import DatePicker from 'src/components/DatePicker.vue';
import EmptyMaster from 'src/components/EmptyMaster.vue';
import BreadcrumbComponent from 'src/components/BreadcrumbComponent.vue';
import LoadingComponent from 'src/components/LoadingComponent.vue';
import PaginationComponent from 'src/components/PaginationComponent.vue';
import HistoryList from 'src/components/HistoryList.vue';

const { t } = useI18n();
const q = useQuasar();
const route = useRoute();
const router = useRouter();
const { currentPage, setPage, pageSize, setPageSize } = usePagination();
const {
  data: historyList,
  loading: loadingHistoryList,
  run: getHistoryList,
} = useHistory();

interface DateRange {
  from: dayjs.Dayjs;
  to: dayjs.Dayjs;
}
type Tabs = 'all' | 'buy' | 'sell' | 'deposit' | 'withdrawal';
const tabs: Ref<Tabs> = ref(
  route.query.tab ? (route.query.tab as Tabs) : 'all'
);
const dateRange = reactive({
  from:
    route.query.from && dayjs(route.query.from as string).isValid()
      ? dayjs(route.query.from as string)
      : dayjs().startOf('week'),
  to:
    route.query.to && dayjs(route.query.to as string).isValid()
      ? dayjs(route.query.to as string)
      : dayjs().startOf('week').add(1, 'week'),
});

const getFlowType = (tab: Tabs) => {
  switch (tab) {
    case 'all':
      return [];
    case 'buy':
      return [FlowTypeEnum.Buy];
    case 'sell':
      return [FlowTypeEnum.Sell];
    case 'deposit':
      return [FlowTypeEnum.Deposit];
    case 'withdrawal':
      return [FlowTypeEnum.Withdrawal];
    default:
      return [];
  }
};

watch(
  [tabs, currentPage, pageSize, dateRange],
  ([newTab, newCurrentPage, newPageSize, newDateRange]) => {
    router.replace({
      query: {
        ...route.query,
        tab: newTab,
        currentPage: newCurrentPage,
        pageSize: newPageSize,
        startTime: newDateRange.from.toISOString(),
        endTime: newDateRange.to.toISOString(),
      },
    });

    getHistoryList({
      pageSize: newPageSize,
      pageNumber: newCurrentPage,
      startTime: newDateRange.from.toISOString(),
      endTime: newDateRange.to.toISOString(),
      flowTypes: getFlowType(newTab),
    });
  },
  {
    immediate: true,
  }
);
watch(
  () => route.query,
  (newQuery) => {
    if (newQuery.tab && newQuery.tab !== tabs.value) {
      tabs.value = newQuery.tab as Tabs;
    }
    if (newQuery.from && dayjs(newQuery.from as string).isValid()) {
      const newFrom = dayjs(newQuery.from as string);
      if (!newFrom.isSame(dateRange.from)) dateRange.from = newFrom;
    }
    if (newQuery.to && dayjs(newQuery.to as string).isValid()) {
      const newTo = dayjs(newQuery.to as string);
      if (!newTo.isSame(dateRange.to)) dateRange.to = newTo;
    }
    if (
      newQuery.currentPage &&
      Number(newQuery.currentPage) &&
      Number(newQuery.currentPage) !== currentPage.value
    ) {
      currentPage.value = Number(newQuery.currentPage);
    }
    if (
      newQuery.pageSize &&
      Number(newQuery.pageSize) &&
      Number(newQuery.pageSize) !== pageSize.value
    ) {
      pageSize.value = Number(newQuery.pageSize);
    }
  }
);
</script>

<style scoped>
.date-picker-wrapper {
  max-width: 22.5rem;
}
</style>
