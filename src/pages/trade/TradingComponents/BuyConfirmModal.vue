<template>
  <q-dialog v-model="model">
    <q-card class="wrapper column gap-y-md q-pa-md">
      <div class="flex items-center justify-between">
        <span class="title text-bold text-center">{{
          t('buyConfirmModal.title')
        }}</span>
        <q-btn flat round icon="close" color="grey" @click="handleClose" />
      </div>

      <span class="text-grey">{{ t('buyConfirmModal.instruction') }}</span>

      <div class="column gap-y-sm">
        <span class="sub-title text-bold text-info">{{
          t('buyConfirmModal.bankTransferDetails')
        }}</span>
        <div
          :class="[
            'flex gap-x-xs q-pa-md rounded-borders',
            themeStore.isDark ? 'bg-grey-9' : 'bg-grey-2',
          ]"
        >
          <div class="column text-grey text-bold">
            <span>{{ t('buyConfirmModal.amount') }}:</span>
            <span>{{ t('buyConfirmModal.accountName') }}:</span>
            <span>{{ t('buyConfirmModal.accountNumber') }}:</span>
            <span>{{ t('buyConfirmModal.bankName') }}:</span>
            <span>{{ t('buyConfirmModal.branchCode') }}:</span>
          </div>
          <div class="column">
            <span>{{
              thousandTool(trade.fiatAmount, trade.fiatCurrency as DigitTypes)
            }}</span>
            <span>{{ trade.accountName }}</span>
            <span>{{ trade.accountNumber }}</span>
            <span>{{ trade.bankName }}</span>
            <span>{{ trade.branchCode }}</span>
          </div>
        </div>

        <q-checkbox
          v-model="agreement"
          :label="t('buyConfirmModal.agreementLabel')"
          @update:model-value="(val: boolean) => {
          if (val === true) {
            agreementError = ''
          }
        }"
        />
        <span v-if="agreementError" class="text-negative">
          <q-icon name="error" color="negative" size="1.5rem" />
          {{ agreementError }}</span
        >
        <input
          ref="fileInputRef"
          type="file"
          accept="image/*"
          class="hidden"
          @change="handleFileSelected"
        />

        <div class="btn-wrapper row gap-x-sm">
          <q-btn
            outline
            dense
            color="accent"
            :label="t('buyConfirmModal.btnSkip')"
            :loading="loadingBuyPaymentMade || loadingSellPaymentMade"
            class="col"
            @click="handleSkip"
          />
          <q-btn
            color="accent"
            :label="t('buyConfirmModal.btnUpload')"
            :loading="
              loadingBuyPaymentMade ||
              loadingSellPaymentMade ||
              loadingSendPaymentProof
            "
            class="col"
            @click="handleUploadImage"
          />
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { useAuthStore, useThemeStore } from 'src/stores';
import { DigitTypes, thousandTool } from 'src/utils/NumberTool';
import { computed, inject, ref, Ref } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  Trade,
  useBuyPaymentMade,
  useSellPaymentMade,
  useSendPaymentProof,
} from '../api';

const { modelValue } = defineProps<{
  modelValue: boolean;
}>();
const emit = defineEmits(['update:modelValue']);
const model = computed({
  get: () => modelValue,
  set: (val: boolean) => emit('update:modelValue', val),
});

const { t } = useI18n();
const auth = useAuthStore();
const themeStore = useThemeStore();
const { run: buyPaymentMade, loading: loadingBuyPaymentMade } =
  useBuyPaymentMade();
const { run: sellPaymentMade, loading: loadingSellPaymentMade } =
  useSellPaymentMade();
const { run: sendPaymentProof, loading: loadingSendPaymentProof } =
  useSendPaymentProof({
    onSuccess: () => {
      if (auth.isAgent) {
        sellPaymentMade({ id: trade.value.id });
      } else {
        buyPaymentMade({ id: trade.value.id });
      }
    },
  });

const trade = inject('trade') as Ref<Trade>;
const agreement = ref<boolean>(false);
const agreementError = ref('');
const fileInputRef = ref<HTMLInputElement | null>(null);

const validateConfirm = () => {
  if (agreement.value === false) {
    agreementError.value = t('buyConfirmModal.agreementError');
    return false;
  }
  return true;
};

const handleFileSelected = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const selectedFile = target.files?.[0];
  if (!selectedFile) return;

  sendPaymentProof({
    tradeId: trade.value.id,
    file: selectedFile,
  });
};
const handleClose = () => {
  emit('update:modelValue', false);
};
const handleSkip = () => {
  if (!validateConfirm()) return;

  if (auth.isAgent) {
    sellPaymentMade({ id: trade.value.id });
  } else {
    buyPaymentMade({ id: trade.value.id });
  }
};
const handleUploadImage = () => {
  if (!validateConfirm()) return;

  fileInputRef.value?.click();
};
</script>

<style scoped>
.wrapper {
  width: 25rem;
  max-width: 100%;
}
.title {
  font-size: 1.5rem;
}
.sub-title {
  font-size: 1rem;
}
.btn-wrapper {
  height: 2rem;
}
</style>
