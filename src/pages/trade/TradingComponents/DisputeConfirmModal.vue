<template>
  <confirm-dialog
    v-model="model"
    type="warning"
    :title="t('disputeConfirmModal.title')"
    :message="t('disputeConfirmModal.message')"
    :loading="disputing"
    :on-confirm="handleDispute"
  />
</template>

<script setup lang="ts">
import { computed, inject, Ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { AxiosError } from 'axios';
import { Trade, useDispute } from '../api';
import ConfirmDialog from 'src/components/ConfirmDialog.vue';
import hooks from 'src/hooks';
import { secondsToMinutes } from 'src/utils/dateTool';
import { ErrorRes } from 'src/utils/requestProvider';

const { modelValue } = defineProps<{
  modelValue: boolean;
}>();
const emit = defineEmits(['update:modelValue', 'error']);
const model = computed({
  get: () => modelValue,
  set: (val: boolean) => emit('update:modelValue', val),
});

const { t } = useI18n();
const { run: dispute, loading: disputing } = useDispute({
  onError: (error) => {
    const axiosError = error as AxiosError<ErrorRes>;
    const errorMessage = axiosError.response?.data?.title;

    const match = errorMessage?.match(/SECONDS_LEFT:(\d+)/);
    if (match) {
      const secondsLeft = parseInt(match[1]);
      emit('error', secondsLeft);

      hooks.useErrorNotify(
        `${t('disputeConfirmModal.waitMessage')} ${secondsToMinutes(
          secondsLeft
        )} ${t('disputeConfirmModal.minutes')}`
      );
    }
  },
});

const trade = inject('trade') as Ref<Trade>;

const handleDispute = () => {
  dispute({
    id: trade.value.id,
  });
};
</script>

<style scoped></style>
