<template>
  <q-card class="wrapper q-mx-auto q-pt-sm">
    <q-stepper
      v-model="step"
      alternative-labels
      :contracted="q.screen.lt.sm"
      header-class="no-border no-wrap"
      done-color="accent"
      active-color="accent"
    >
      <q-step
        :name="1"
        prefix="1"
        :title="t('tradePage.create')"
        :done="step > 1"
      />
      <q-step
        :name="2"
        prefix="2"
        :title="t('tradePage.pay')"
        :done="step > 2"
      />
      <q-step
        :name="3"
        prefix="3"
        :title="t('tradePage.confirm')"
        :done="step > 3"
      />
      <q-step
        :name="4"
        prefix="4"
        :title="t('tradePage.complete')"
        :done="step > 4"
        color="accent"
      >
        <div class="column items-center gap-y-md">
          <div class="column gap-y-sm items-center">
            <q-icon class="text-green-6 text-h1" name="check_circle_outline" />
            <span class="title text-bold text-green-6">{{
              t('tradeCompleted.title')
            }}</span>
          </div>

          <div class="column items-center">
            <div class="flex gap-x-xs text-bold">
              <span class="text-grey"
                >{{ t('tradeCompleted.orderNumber') }}:</span
              >
              <span>{{ trade.tradeNo }}</span>
            </div>
            <div class="flex gap-x-xs text-bold">
              <span class="text-grey"
                >{{ t('tradeCompleted.createdAt') }}:</span
              >
              <span>{{
                dayjs(trade.createdAt).format(dateFormator.accurate)
              }}</span>
            </div>
            <div class="flex gap-x-xs text-bold">
              <span class="text-grey"
                >{{ t('tradeCompleted.completedAt') }}:</span
              >
              <span>{{
                dayjs(trade.completedAt).format(dateFormator.accurate)
              }}</span>
            </div>
          </div>

          <q-separator class="full-width" />

          <div class="full-width column gap-y-sm">
            <q-btn
              color="accent"
              :to="{ name: 'home' }"
              :label="t('tradeCompleted.btnGoHome')"
              class="full-width"
            />
            <q-btn
              outline
              color="accent"
              :label="t('tradeCompleted.btnDetails')"
              class="full-width"
              @click="handleOpenDetailsModal"
            />
          </div>
        </div>
      </q-step>
    </q-stepper>
  </q-card>
  <transaction-details-modal v-model="showDetailsModal" :transaction="trade" />
</template>

<script setup lang="ts">
import { inject, Ref, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import dayjs from 'dayjs';
import { dateFormator } from 'src/utils/dateTool';
import TransactionDetailsModal from 'src/components/TransactionDetailsModal.vue';
import { Trade } from '../api';

const q = useQuasar();
const { t } = useI18n();

const step = ref(4);
const trade = inject('trade') as Ref<Trade>;
const showDetailsModal = ref(false);

const handleOpenDetailsModal = () => {
  showDetailsModal.value = true;
};
</script>

<style scoped>
.wrapper {
  max-width: 50rem;
}
.title {
  font-size: 1.5rem;
}
</style>
