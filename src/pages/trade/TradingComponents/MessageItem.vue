<template>
  <div
    v-if="message.messageType === ChatMessageType.System"
    class="text-grey text-center message"
  >
    <div>{{ dayjs(message.sentAt).format(dateFormator.accurate) }}</div>
    <div class="text-bold">
      {{ message.payload }}
    </div>
  </div>
  <div
    v-else
    class="wrapper q-pa-sm"
    :class="
      message.senderRole === SenderRole.SupportTeam
        ? 'bg-warning text-white border-bottom-left'
        : auth.loginRes?.email === message.senderName
        ? 'bg-accent text-white q-ml-auto border-bottom-right'
        : 'bg-grey-4 text-black border-bottom-left'
    "
  >
    <span
      v-if="message.senderRole === SenderRole.SupportTeam"
      class="text-center text-bold"
      >{{ t('messageItem.supportTeam') }}</span
    >
    <span v-if="message.messageType === ChatMessageType.Text" class="message">{{
      message.payload
    }}</span>
    <div
      v-else-if="message.messageType === ChatMessageType.Image"
      class="image-wrapper column bg-grey rounded-borders"
    >
      <image-master
        width="240px"
        :src="message.payload"
        @load="$emit('load')"
      />
      <div v-if="message.caption" class="text-caption text-bold q-px-xs">
        {{ message.caption }}
      </div>
    </div>
    <span
      class="q-ml-auto text-caption"
      :class="auth.loginRes?.email === message.senderName ? 'text-white' : ''"
      >{{ dayjs(message.sentAt).format(dateFormator.accurate) }}</span
    >
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { useI18n } from 'vue-i18n';
import { Message } from '../api';
import { useAuthStore } from 'src/stores';
import { SenderRole, ChatMessageType } from 'src/utils/enums';
import { dateFormator } from 'src/utils/dateTool';
import ImageMaster from 'src/components/ImageMaster.vue';

const { message } = defineProps<{
  message: Message;
}>();

const { t } = useI18n();
const auth = useAuthStore();
</script>

<style scoped>
.wrapper {
  display: inline-flex;
  flex-direction: column;
  row-gap: 0.25rem;
  width: fit-content;
  border-radius: 16px;
}
.border-bottom-left {
  border-bottom-left-radius: 4px;
}
.border-bottom-right {
  border-bottom-right-radius: 4px;
}
.message {
  display: block;
  word-break: break-word;
  overflow-wrap: break-word;
}
.image-wrapper {
  max-width: 240px;
}
</style>
