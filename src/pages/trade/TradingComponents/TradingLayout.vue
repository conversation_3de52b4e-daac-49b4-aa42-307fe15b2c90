<template>
  <div v-if="!trade || loadingTrades">
    <q-card class="wrapper q-mx-auto q-pa-md">
      <div class="text-center">
        <q-spinner-radio color="orange" size="100" class="q-mb-md" />

        <div class="text-h6 text-weight-bold">
          {{ t('tradingLayout.loadingMessage') }}
        </div>
      </div>
    </q-card>
  </div>

  <div
    v-else-if="
      [
        TradeStatusEnum.Accepted,
        TradeStatusEnum.BankAccountAssigned,
        TradeStatusEnum.PaymentMade,
      ].includes(trade.status)
    "
  >
    <q-card
      class="row"
      :style="{ 'margin-bottom': q.screen.lt.md ? CHAT_BOX_HEADER_HEIGHT : '' }"
    >
      <div class="col-md-8 col-12">
        <div v-if="route.query.type === 'buy'">
          <buy-assigned
            v-if="
              [
                TradeStatusEnum.Accepted,
                TradeStatusEnum.BankAccountAssigned,
              ].includes(trade.status)
            "
          />
          <buy-committed
            v-else-if="[TradeStatusEnum.PaymentMade].includes(trade.status)"
          />
        </div>

        <div v-if="route.query.type === 'sell'">
          <sell-assigned
            v-if="
              [
                TradeStatusEnum.Accepted,
                TradeStatusEnum.BankAccountAssigned,
              ].includes(trade.status)
            "
          />
          <sell-committed
            v-else-if="[TradeStatusEnum.PaymentMade].includes(trade.status)"
          />
        </div>
      </div>
      <div class="chat-wrapper col-md-4 col-12">
        <chat-box />
      </div>
    </q-card>
  </div>

  <div v-else>
    <in-pairing v-if="trade.status === TradeStatusEnum.Pending" />
    <cancel-success v-if="trade.status === TradeStatusEnum.Cancelled" />
    <trade-completed v-if="trade.status === TradeStatusEnum.Completed" />
    <trade-expired v-if="trade.status === TradeStatusEnum.Expired" />
  </div>
</template>

<script setup lang="ts">
import { computed, provide, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import { Trade, useGetTradeById } from '../api';
import { useTradeStore } from 'src/stores';
import { CHAT_BOX_HEADER_HEIGHT } from 'src/utils/constants';
import { TradeStatusEnum } from 'src/utils/enums';
import InPairing from './InPairing.vue';
import CancelSuccess from './CancelSuccess.vue';
import TradeCompleted from './TradeCompleted.vue';
import TradeExpired from './TradeExpired.vue';
import BuyAssigned from './BuyAssigned.vue';
import BuyCommitted from './BuyCommitted.vue';
import SellAssigned from './SellAssigned.vue';
import SellCommitted from './SellCommitted.vue';
import ChatBox from './ChatBox.vue';

const route = useRoute();
const router = useRouter();
const q = useQuasar();
const { t } = useI18n();
const tradeStore = useTradeStore();
const {
  data: trades,
  loading: loadingTrades,
  run: getTradeById,
} = useGetTradeById();

const trade = ref<Trade>();
provide('trade', trade);

const routeQuery = computed(() => route.query);

watch(
  routeQuery,
  (newRouteQuery) => {
    if (newRouteQuery.id) {
      getTradeById({ id: Number(newRouteQuery.id) });
    } else {
      router.push({ name: 'not-found' });
    }
  },
  { immediate: true }
);
watch(trades, (newTrades) => {
  if (newTrades?.items.length) {
    trade.value = newTrades.items[0];
  } else {
    router.push({ name: 'not-found' });
  }
});
watch(
  () => tradeStore.trade,
  (newTrade) => {
    if (newTrade && newTrade.id === trade.value?.id) {
      trade.value = newTrade;
    }
  }
);
</script>

<style scoped>
.wrapper {
  max-width: 50rem;
}
.chat-wrapper {
  position: relative;
}
</style>
