<template>
  <q-card class="wrapper q-mx-auto q-pa-lg column items-center gap-y-md">
    <div class="column gap-y-md items-center">
      <q-spinner-ball color="info" size="4em" />
      <span class="title text-bold text-info">{{ t('inPairing.title') }}</span>
    </div>

    <div class="flex items-center gap-x-xs text-orange text-bold">
      <q-icon name="info" size="1.2rem" />
      <span>{{ t('inPairing.expiresIn') }}</span>
      <countdown-timer
        :startAt="trade.updatedAt"
        :duration="PENDING_TIME_LIMIT"
      />
    </div>

    <div class="column items-center gap-x-sm full-width">
      <span class="sub-title text-bold"
        >{{
          trade.type === TradeTypeEnum.Buy
            ? t('inPairing.buying')
            : t('inPairing.selling')
        }}
        {{ trade.cryptoSymbol }}</span
      >
      <div class="flex gap-x-xs text-bold">
        <span class="col text-right text-grey"
          >{{ t('inPairing.amountTo') }}
          {{
            trade.type === TradeTypeEnum.Buy
              ? t('inPairing.buy')
              : t('inPairing.sell')
          }}:</span
        >
        <div class="flex gap-x-xs">
          <span>{{
            thousandTool(trade.cryptoAmount, trade.cryptoSymbol as DigitTypes)
          }}</span>
          <span>{{ trade.cryptoSymbol }}</span>
        </div>
      </div>
      <div class="flex gap-x-xs text-bold">
        <span class="text-grey"
          >{{ t('inPairing.youWill') }}
          {{
            trade.type === TradeTypeEnum.Buy
              ? t('inPairing.pay')
              : t('inPairing.receive')
          }}:</span
        >
        <div class="col full-width flex gap-x-xs text-end">
          <span>{{
            thousandTool(trade.fiatAmount, trade.fiatCurrency as DigitTypes)
          }}</span>
          <span>{{ trade.fiatCurrency }}</span>
        </div>
      </div>
    </div>

    <q-separator class="full-width" />

    <q-btn
      color="accent"
      :to="{ name: 'home' }"
      :label="t('inPairing.btnGoHome')"
      class="full-width"
    />
    <!-- <q-btn -->
    <!--   flat -->
    <!--   color="warning" -->
    <!--   :label="t('inPairing.btnCancel')" -->
    <!--   class="full-width" -->
    <!--   disable -->
    <!--   @click="handleCancel" -->
    <!-- /> -->
  </q-card>
</template>

<script setup lang="ts">
import { TradeTypeEnum } from 'src/utils/enums';
import { DigitTypes, thousandTool } from 'src/utils/NumberTool';
import { inject, Ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { Trade } from '../api';
import { PENDING_TIME_LIMIT } from 'src/utils/constants';
import CountdownTimer from 'src/components/CountdownTimer.vue';

const { t } = useI18n();
const trade = inject('trade') as Ref<Trade>;

// const handleCancel = () => {
//   //
// };
</script>

<style scoped>
.wrapper {
  max-width: 50rem;
}
.title {
  font-size: 1.5rem;
}
.sub-title {
  font-size: 1rem;
}
</style>
