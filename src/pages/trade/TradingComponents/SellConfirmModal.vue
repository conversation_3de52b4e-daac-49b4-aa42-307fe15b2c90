<template>
  <q-dialog v-model="model">
    <q-card class="wrapper column gap-y-md q-pa-md">
      <span class="title text-bold text-center">{{
        t('sellConfirmModal.title')
      }}</span>

      <div class="column gap-y-sm">
        <span class="sub-title text-bold text-info">{{
          t('sellConfirmModal.buyerInfo')
        }}</span>
        <div
          :class="[
            'flex gap-x-xs q-pa-md rounded-borders',
            themeStore.isDark ? 'bg-grey-9' : 'bg-grey-2',
          ]"
        >
          <div class="column text-grey text-bold">
            <span>{{ t('sellConfirmModal.buyerName') }}:</span>
            <span>{{ t('sellConfirmModal.transferAmount') }}:</span>
          </div>
          <div class="column">
            <span>{{ trade.payerBankAccountName }}</span>
            <span
              >{{
                thousandTool(trade.fiatAmount, trade.fiatCurrency as DigitTypes)
              }}
              {{ trade.fiatCurrency }}</span
            >
          </div>
        </div>

        <q-checkbox
          v-model="agreement"
          :label="t('sellConfirmModal.agreementLabel')"
          @update:model-value="(val: boolean) => {
          if (val === true) {
            agreementError = ''
          }
        }"
        />
        <span v-if="agreementError" class="text-negative">
          <q-icon name="error" color="negative" size="1.5rem" />
          {{ agreementError }}</span
        >

        <div class="btn-wrapper row gap-x-sm">
          <q-btn
            outline
            dense
            color="accent"
            :label="t('sellConfirmModal.btnCancel')"
            :loading="loadingSellPaymentConfirm || loadingBuyPaymentConfirm"
            class="col"
            @click="handleCancel"
          />
          <q-btn
            color="accent"
            :label="t('sellConfirmModal.btnConfirm')"
            :loading="loadingSellPaymentConfirm || loadingBuyPaymentConfirm"
            class="col"
            @click="handleConfirm"
          />
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, inject, Ref, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useAuthStore, useThemeStore } from 'src/stores';
import { DigitTypes, thousandTool } from 'src/utils/NumberTool';
import { Trade, useBuyPaymentConfirm, useSellPaymentConfirm } from '../api';

const { modelValue } = defineProps<{
  modelValue: boolean;
}>();
const emit = defineEmits(['update:modelValue']);
const model = computed({
  get: () => modelValue,
  set: (val: boolean) => emit('update:modelValue', val),
});

const { t } = useI18n();
const auth = useAuthStore();
const themeStore = useThemeStore();
const { run: sellPaymentConfirm, loading: loadingSellPaymentConfirm } =
  useSellPaymentConfirm();
const { run: buyPaymentConfirm, loading: loadingBuyPaymentConfirm } =
  useBuyPaymentConfirm();

const trade = inject('trade') as Ref<Trade>;
const agreement = ref<boolean>(false);
const agreementError = ref('');

const validateConfirm = () => {
  if (agreement.value === false) {
    agreementError.value = t('sellConfirmModal.agreementError');
    return false;
  }
  return true;
};

const handleCancel = () => {
  emit('update:modelValue', false);
};
const handleConfirm = () => {
  if (!validateConfirm()) return;

  if (auth.isAgent) {
    buyPaymentConfirm({ id: trade.value.id });
  } else {
    sellPaymentConfirm({ id: trade.value.id });
  }
};
</script>

<style scoped>
.wrapper {
  width: 25rem;
  max-width: 100%;
}
.title {
  font-size: 1.5rem;
}
.sub-title {
  font-size: 1rem;
}
.btn-wrapper {
  height: 2rem;
}
</style>
