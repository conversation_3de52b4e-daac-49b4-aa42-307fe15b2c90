<template>
  <q-card class="q-pt-sm no-shadow">
    <q-stepper
      v-model="step"
      alternative-labels
      :contracted="q.screen.lt.sm"
      header-class="no-border no-wrap"
      done-color="accent"
      active-color="accent"
    >
      <q-step
        :name="1"
        prefix="1"
        :title="t('tradePage.create')"
        :done="step > 1"
      />
      <q-step :name="2" prefix="2" :title="t('tradePage.pay')" :done="step > 2">
        <div class="column gap-y-md">
          <span class="title text-bold text-center">{{
            t('buyAssigned.title')
          }}</span>
          <span class="text-grey">{{ t('buyAssigned.instruction') }}</span>

          <div>
            <span
              :class="[
                'sub-title text-bold',
                themeStore.isDark ? 'text-blue' : 'text-info',
              ]"
              >{{ t('buyAssigned.orderSummary') }}</span
            >
            <div
              :class="[
                'column gap-y-sm q-pa-md rounded-borders items-center',
                themeStore.isDark ? 'bg-grey-9' : 'bg-grey-2',
              ]"
            >
              <div class="flex gap-x-xs">
                <span class="text-grey text-bold"
                  >{{ t('buyAssigned.orderNumber') }}:</span
                >
                <span>{{ trade.tradeNo }}</span>
              </div>
              <code class="flex gap-sm justify-between full-width">
                <div class="flex flex-1 items-center column gap-x-sm text-bold">
                  <span class="text-grey">{{ t('buyAssigned.buying') }}</span>
                  <div class="flex gap-x-xs">
                    <span>{{
                      thousandTool(
                        trade.cryptoAmount,
                        trade.cryptoSymbol as DigitTypes
                      )
                    }}</span>
                    <span>{{ trade.cryptoSymbol }}</span>
                  </div>
                </div>
                <div class="vertical-seperator"></div>
                <div class="flex flex-1 items-center column gap-x-sm text-bold">
                  <span class="text-grey">{{
                    t('buyAssigned.exchangeRate')
                  }}</span>
                  <div class="flex gap-x-xs">
                    <span>1</span>
                    <span>{{ trade.cryptoSymbol }}</span>
                    <span>=</span>
                    <span>{{ trade.rate }}</span>
                    <span>{{ trade.fiatCurrency }}</span>
                  </div>
                </div>
                <div class="vertical-seperator"></div>
                <div
                  :class="[
                    'flex flex-1 items-center column gap-x-sm text-bold',
                    themeStore.isDark ? 'text-blue' : 'text-info',
                  ]"
                >
                  <span>{{ t('buyAssigned.totalToPay') }}</span>
                  <div class="flex gap-x-xs">
                    <span>{{
                      thousandTool(
                        trade.fiatAmount,
                        trade.fiatCurrency as DigitTypes
                      )
                    }}</span>
                    <span>{{ trade.fiatCurrency }}</span>
                  </div>
                </div>
              </code>
            </div>
          </div>

          <div class="text-grey">
            <q-icon
              name="error_outline"
              color="orange-9"
              size="xs"
              class="q-mr-xs"
            />

            <span>{{ t('buyAssigned.warning') }}</span>
          </div>

          <div>
            <span
              :class="[
                'sub-title text-bold',
                themeStore.isDark ? 'text-blue' : 'text-info',
              ]"
              >{{ t('buyAssigned.bankTransferDetails') }}</span
            >
            <div
              :class="[
                'flex gap-x-sm q-pa-md rounded-borders',
                themeStore.isDark ? 'bg-grey-9' : 'bg-grey-2',
              ]"
            >
              <div class="column text-grey text-bold">
                <span>{{ t('buyAssigned.amount') }}:</span>
                <span>{{ t('buyAssigned.accountName') }}:</span>
                <span>{{ t('buyAssigned.accountNumber') }}:</span>
                <span>{{ t('buyAssigned.bankName') }}:</span>
                <span>{{ t('buyAssigned.branchCode') }}:</span>
              </div>
              <div class="column">
                <span
                  >{{
                    thousandTool(
                      trade.fiatAmount,
                      trade.fiatCurrency as DigitTypes
                    )
                  }}
                  {{ trade.fiatCurrency }}</span
                >
                <span>{{ trade.accountName }}</span>
                <span>{{ trade.accountNumber }}</span>
                <span>{{ trade.bankName }}</span>
                <span>{{ trade.branchCode }}</span>
              </div>
              <div class="column">
                <copy-button :value="trade.fiatAmount.toString()" />
                <copy-button :value="trade.accountName" />
                <copy-button :value="trade.accountNumber" />
                <copy-button :value="trade.bankName" />
                <copy-button :value="trade.branchCode" />
              </div>
            </div>
          </div>

          <div>
            <span
              :class="[
                'sub-title text-bold',
                themeStore.isDark ? 'text-blue' : 'text-info',
              ]"
              >{{ t('buyAssigned.buyerInfo') }}</span
            >
            <div
              :class="[
                'flex gap-x-sm q-pa-md rounded-borders',
                themeStore.isDark ? 'bg-grey-9' : 'bg-grey-2',
              ]"
            >
              <span class="text-grey text-bold">
                {{ t('buyAssigned.buyerName') }}:
              </span>
              <span>{{ trade.payerBankAccountName }}</span>
            </div>
          </div>

          <div
            class="flex items-center justify-end gap-x-xs text-orange text-bold"
          >
            <q-icon
              :name="isExpired || isDisputed ? 'warning' : 'info'"
              size="1.2rem"
            />
            <span v-if="isExpired || isDisputed">
              {{ t('buyAssigned.payWarning') }}
            </span>
            <template v-else>
              <span>{{ t('buyAssigned.remainingTime') }}</span>
              <countdown-timer
                :startAt="trade.updatedAt"
                :duration="PAYMENT_TIME_LIMIT"
                @countdown-finished="handleCountdownEnd"
              />
            </template>
          </div>

          <div
            v-if="isDisputed"
            class="flex items-center justify-center gap-x-xs q-pa-sm rounded-borders"
            :class="themeStore.isDark ? 'bg-orange-3' : 'bg-orange-1'"
          >
            <q-icon name="info" color="negative" size="1.5rem" />
            <span class="text-negative text-center">{{
              t('buyAssigned.disputeWarning')
            }}</span>
          </div>

          <q-separator />

          <!-- <div class="column gap-y-sm full-width"> -->
          <q-btn
            color="accent"
            class="full-width"
            :label="t('buyAssigned.btnPaid')"
            @click="handleShowConfirmModal"
          />
          <!-- <q-btn -->
          <!--   outline -->
          <!--   disable -->
          <!--   color="negative" -->
          <!--   class="full-width" -->
          <!--   :label="t('buyAssigned.btnCancel')" -->
          <!-- /> -->
          <!-- </div> -->
        </div>
      </q-step>
      <q-step
        :name="3"
        prefix="3"
        :title="t('tradePage.confirm')"
        :done="step > 3"
      />
      <q-step
        :name="4"
        prefix="4"
        :title="t('tradePage.complete')"
        :done="step > 4"
      />
    </q-stepper>
  </q-card>
  <buy-confirm-modal v-model="showConfirmModal" />
</template>

<script setup lang="ts">
import { computed, inject, Ref, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import { Trade } from '../api';
import { useThemeStore } from 'src/stores';
import { PAYMENT_TIME_LIMIT } from 'src/utils/constants';
import { TradeDisputeStatusEnum } from 'src/utils/enums';
import { DigitTypes, thousandTool } from 'src/utils/NumberTool';
import CountdownTimer from 'src/components/CountdownTimer.vue';
import CopyButton from 'src/components/CopyButton.vue';
import BuyConfirmModal from './BuyConfirmModal.vue';

const q = useQuasar();
const { t } = useI18n();
const themeStore = useThemeStore();

const step = ref(2);
const showConfirmModal = ref<boolean>(false);
const trade = inject('trade') as Ref<Trade>;
const isExpired = ref<boolean>(false);

const isDisputed = computed(
  () => trade.value.disputeStatus === TradeDisputeStatusEnum.InDispute
);

const handleShowConfirmModal = () => {
  showConfirmModal.value = true;
};
const handleCountdownEnd = () => {
  isExpired.value = true;
};
</script>

<style scoped>
.title {
  font-size: 1.5rem;
}
.sub-title {
  font-size: 1rem;
}
.vertical-seperator {
  width: 2px;
  height: 100;
  background-color: var(--border-color);
}
.body--dark .vertical-seperator {
  background-color: white;
}
</style>
