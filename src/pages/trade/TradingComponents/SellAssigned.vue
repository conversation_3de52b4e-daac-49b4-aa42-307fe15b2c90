<template>
  <q-card class="q-pt-sm no-shadow">
    <q-stepper
      v-model="step"
      alternative-labels
      :contracted="q.screen.lt.sm"
      header-class="no-border no-wrap"
      done-color="accent"
      active-color="accent"
    >
      <q-step
        :name="1"
        prefix="1"
        :title="t('tradePage.create')"
        :done="step > 1"
      />
      <q-step :name="2" prefix="2" :title="t('tradePage.pay')" :done="step > 2">
        <div class="column gap-y-md">
          <span class="title text-bold text-center">{{
            t('sellAssigned.title')
          }}</span>
          <span class="text-grey">{{ t('sellAssigned.instruction') }}</span>

          <div>
            <span
              :class="[
                'sub-title text-bold',
                themeStore.isDark ? 'text-blue' : 'text-info',
              ]"
              >{{ t('sellAssigned.orderSummary') }}</span
            >
            <div
              :class="[
                'column gap-y-sm q-pa-md rounded-borders items-center',
                themeStore.isDark ? 'bg-grey-9' : 'bg-grey-2',
              ]"
            >
              <div class="flex gap-x-xs">
                <span class="text-grey text-bold"
                  >{{ t('sellAssigned.orderNumber') }}:</span
                >
                <span>{{ trade.tradeNo }}</span>
              </div>
              <code class="flex gap-sm justify-between full-width">
                <div class="flex flex-1 items-center column gap-x-sm text-bold">
                  <span class="text-grey">{{ t('sellAssigned.selling') }}</span>
                  <div class="flex gap-x-xs">
                    <span>{{
                      thousandTool(
                        trade.cryptoAmount,
                        trade.cryptoSymbol as DigitTypes
                      )
                    }}</span>
                    <span>{{ trade.cryptoSymbol }}</span>
                  </div>
                </div>
                <div class="vertical-seperator"></div>
                <div class="flex flex-1 items-center column gap-x-sm text-bold">
                  <span class="text-grey">{{
                    t('sellAssigned.exchangeRate')
                  }}</span>
                  <div class="flex gap-x-xs">
                    <span>1</span>
                    <span>{{ trade.cryptoSymbol }}</span>
                    <span>=</span>
                    <span>{{ trade.rate }}</span>
                    <span>{{ trade.fiatCurrency }}</span>
                  </div>
                </div>
                <div class="vertical-seperator"></div>
                <div
                  :class="[
                    'flex flex-1 items-center column gap-x-sm text-bold',
                    themeStore.isDark ? 'text-blue' : 'text-info',
                  ]"
                >
                  <span>{{ t('sellAssigned.amountToReceive') }}</span>
                  <div class="flex gap-x-xs">
                    <span>{{
                      thousandTool(
                        trade.fiatAmount,
                        trade.fiatCurrency as DigitTypes
                      )
                    }}</span>
                    <span>{{ trade.fiatCurrency }}</span>
                  </div>
                </div>
              </code>
            </div>
          </div>

          <div>
            <span
              :class="[
                'sub-title text-bold',
                themeStore.isDark ? 'text-blue' : 'text-info',
              ]"
              >{{ t('sellAssigned.buyerInfo') }}</span
            >
            <div
              :class="[
                'flex gap-x-sm q-pa-md rounded-borders',
                themeStore.isDark ? 'bg-grey-9' : 'bg-grey-2',
              ]"
            >
              <div class="column text-grey text-bold">
                <span>{{ t('sellAssigned.buyerName') }}:</span>
                <span>{{ t('sellAssigned.transferAmount') }}:</span>
              </div>
              <div class="column">
                <span>{{ trade.payerBankAccountName }}</span>
                <span
                  >{{
                    thousandTool(
                      trade.fiatAmount,
                      trade.fiatCurrency as DigitTypes
                    )
                  }}
                  {{ trade.fiatCurrency }}</span
                >
              </div>
            </div>
          </div>

          <div>
            <span
              :class="[
                'sub-title text-bold',
                themeStore.isDark ? 'text-blue' : 'text-info',
              ]"
              >{{ t('sellAssigned.bankTransferDetails') }}</span
            >
            <div
              :class="[
                'flex gap-x-sm q-pa-md rounded-borders',
                themeStore.isDark ? 'bg-grey-9' : 'bg-grey-2',
              ]"
            >
              <div class="column text-grey text-bold">
                <span>{{ t('sellAssigned.accountName') }}:</span>
                <span>{{ t('sellAssigned.accountNumber') }}:</span>
                <span>{{ t('sellAssigned.bankName') }}:</span>
                <span>{{ t('sellAssigned.branchCode') }}:</span>
              </div>
              <div class="column">
                <span>{{ trade.accountName }}</span>
                <span>{{ trade.accountNumber }}</span>
                <span>{{ trade.bankName }}</span>
                <span>{{ trade.branchCode }}</span>
              </div>
            </div>
          </div>

          <div
            v-if="!isDisputed"
            class="flex items-center justify-end gap-x-xs text-orange text-bold"
          >
            <q-icon :name="isExpired ? 'warning' : 'info'" size="1.2rem" />
            <span v-if="isExpired">
              {{ t('sellAssigned.sellWarning') }}
            </span>
            <template v-else>
              <span>{{ t('sellAssigned.remainingTime') }}</span>
              <countdown-timer
                :startAt="trade.updatedAt"
                :duration="PAYMENT_TIME_LIMIT"
                @countdown-finished="handleCountdownEnd"
              />
            </template>
          </div>
          <div
            v-else
            class="flex items-center justify-center gap-x-xs q-pa-sm rounded-borders"
            :class="themeStore.isDark ? 'bg-orange-3' : 'bg-orange-1'"
          >
            <q-icon name="info" color="negative" size="1.5rem" />
            <span class="text-negative text-center">{{
              t('sellAssigned.disputedMessage')
            }}</span>
          </div>

          <q-separator />

          <div class="column gap-y-sm">
            <q-btn color="accent" class="full-width" disable>
              <q-spinner-puff color="white" size="1em"> </q-spinner-puff>
              <span class="q-pl-sm">{{ t('sellAssigned.btnWaiting') }}</span>
            </q-btn>
            <q-btn
              outline
              color="negative"
              class="full-width"
              @click="handleShowDisputeModal"
              :disable="
                trade.disputeStatus !== TradeDisputeStatusEnum.None ||
                interval !== null
              "
            >
              <template
                v-if="trade.disputeStatus !== TradeDisputeStatusEnum.None"
              >
                <q-tooltip style="font-size: 0.875rem">
                  {{ t('sellAssigned.disputedMessage') }}
                </q-tooltip>
                <q-icon name="info" color="negative" size="1.5rem" />
              </template>
              <span class="q-ml-sm">
                {{
                  trade.disputeStatus === TradeDisputeStatusEnum.None
                    ? t('sellAssigned.btnDispute')
                    : t('sellAssigned.btnDisputed')
                }}
                <span v-if="interval">
                  {{ t('sellAssigned.availableIn') }}
                  {{ secondsToMinutes(secondsLeft) }}
                  {{ t('sellAssigned.minutes') }}
                </span>
              </span>
            </q-btn>
          </div>
        </div>
      </q-step>
      <q-step
        :name="3"
        prefix="3"
        :title="t('tradePage.confirm')"
        :done="step > 3"
      />
      <q-step
        :name="4"
        prefix="4"
        :title="t('tradePage.complete')"
        :done="step > 4"
      />
    </q-stepper>
  </q-card>
  <dispute-confirm-modal
    v-model="showDisputeModal"
    @error="handleDisputeError"
  />
</template>

<script setup lang="ts">
import { computed, inject, onUnmounted, Ref, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import { Trade } from '../api';
import { PAYMENT_TIME_LIMIT } from 'src/utils/constants';
import { TradeDisputeStatusEnum } from 'src/utils/enums';
import { secondsToMinutes } from 'src/utils/dateTool';
import { useThemeStore } from 'src/stores';
import { DigitTypes, thousandTool } from 'src/utils/NumberTool';
import CountdownTimer from 'src/components/CountdownTimer.vue';
import DisputeConfirmModal from './DisputeConfirmModal.vue';

const q = useQuasar();
const { t } = useI18n();
const themeStore = useThemeStore();

const step = ref(2);
const trade = inject('trade') as Ref<Trade>;
const showDisputeModal = ref<boolean>(false);
const secondsLeft = ref<number>(0);
const interval = ref<NodeJS.Timeout | null>(null);
const isExpired = ref<boolean>(false);

const isDisputed = computed(
  () => trade.value.disputeStatus === TradeDisputeStatusEnum.InDispute
);

const handleShowDisputeModal = () => {
  showDisputeModal.value = true;
};
const handleDisputeError = (value: number) => {
  secondsLeft.value = value;
  if (!interval.value)
    interval.value = setInterval(() => {
      if (secondsLeft.value > 0) secondsLeft.value -= 1;
      else if (interval.value) {
        clearInterval(interval.value);
        interval.value = null;
      }
    }, 1000);
};
const handleCountdownEnd = () => {
  isExpired.value = true;
};

onUnmounted(() => {
  if (interval.value) clearInterval(interval.value);
});
</script>

<style scoped>
.title {
  font-size: 1.5rem;
}
.sub-title {
  font-size: 1rem;
}
.vertical-seperator {
  width: 2px;
  height: 100;
  background-color: var(--border-color);
}
.body--dark .vertical-seperator {
  background-color: white;
}
</style>
