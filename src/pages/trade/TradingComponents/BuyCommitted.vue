<template>
  <q-card class="q-pt-sm no-shadow">
    <q-stepper
      v-model="step"
      alternative-labels
      :contracted="q.screen.lt.sm"
      header-class="no-border no-wrap"
      done-color="accent"
      active-color="accent"
    >
      <q-step
        :name="1"
        prefix="1"
        :title="t('tradePage.create')"
        :done="step > 1"
      />
      <q-step
        :name="2"
        prefix="2"
        :title="t('tradePage.pay')"
        :done="step > 2"
      />
      <q-step
        :name="3"
        prefix="3"
        :title="t('tradePage.confirm')"
        :done="step > 3"
      >
        <div class="column gap-y-md items-center">
          <span class="title text-bold">{{ t('buyCommitted.title') }}</span>
          <span class="text-grey">{{ t('buyCommitted.description') }}</span>

          <q-spinner-hourglass
            color="orange-14"
            size="8rem"
            :class="q.screen.gt.sm ? 'q-my-xl' : ''"
          />

          <span class="title text-bold text-info text-center">{{
            t('buyCommitted.waitingMessage')
          }}</span>

          <div class="column items-center">
            <span class="text-grey text-bold"
              >{{ t('buyCommitted.orderNumber') }}:</span
            >
            <span>{{ trade.tradeNo }}</span>
          </div>

          <div
            v-if="isDisputed"
            class="flex items-center justify-center gap-x-xs q-pa-sm rounded-borders full-width"
            :class="themeStore.isDark ? 'bg-orange-3' : 'bg-orange-1'"
          >
            <q-icon name="info" color="negative" size="1.5rem" />
            <span class="text-negative text-center">{{
              t('buyCommitted.disputeWarning')
            }}</span>
          </div>

          <q-separator
            class="full-width"
            :class="isDisputed ? 'q-mt-xl' : ''"
          />

          <div class="column gap-y-sm full-width">
            <q-btn
              color="accent"
              :to="{ name: 'home' }"
              :label="t('buyCommitted.btnGoHome')"
              class="full-width"
            />
            <payment-proof :tradeId="trade.id" />
          </div>
        </div>
      </q-step>
      <q-step
        :name="4"
        prefix="4"
        :title="t('tradePage.complete')"
        :done="step > 4"
      />
    </q-stepper>
  </q-card>
</template>

<script setup lang="ts">
import { inject, Ref, ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import { useThemeStore } from 'src/stores';
import { TradeDisputeStatusEnum } from 'src/utils/enums';
import { Trade } from '../api';
import PaymentProof from './PaymentProof.vue';

const q = useQuasar();
const { t } = useI18n();
const themeStore = useThemeStore();

const step = ref(3);
const trade = inject('trade') as Ref<Trade>;

const isDisputed = computed(
  () => trade.value.disputeStatus === TradeDisputeStatusEnum.InDispute
);
</script>

<style scoped>
.title {
  font-size: 1.5rem;
}
</style>
