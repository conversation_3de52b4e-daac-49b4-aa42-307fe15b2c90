<template>
  <q-card class="wrapper q-mx-auto q-pa-lg column items-center gap-y-md">
    <div class="column gap-y-md items-center">
      <q-icon name="cancel" class="text-negative text-h1" />
      <span class="title text-bold text-negative">{{
        t('cancelSuccess.title')
      }}</span>
    </div>

    <div class="column items-center gap-x-sm full-width">
      <span class="sub-title text-bold"
        >{{
          trade.type === TradeTypeEnum.Buy
            ? t('cancelSuccess.buying')
            : t('cancelSuccess.selling')
        }}
        {{ trade.cryptoSymbol }}</span
      >
      <div class="flex gap-x-xs text-bold">
        <span class="col text-right text-grey"
          >{{ t('cancelSuccess.amountTo') }}
          {{
            trade.type === TradeTypeEnum.Buy
              ? t('cancelSuccess.buy')
              : t('cancelSuccess.sell')
          }}:</span
        >
        <div class="flex gap-x-xs">
          <span>{{
            thousandTool(trade.cryptoAmount, trade.cryptoSymbol as DigitTypes)
          }}</span>
          <span>{{ trade.cryptoSymbol }}</span>
        </div>
      </div>
      <div class="flex gap-x-xs text-bold">
        <span class="text-grey"
          >{{ t('cancelSuccess.youWill') }}
          {{
            trade.type === TradeTypeEnum.Buy
              ? t('cancelSuccess.pay')
              : t('cancelSuccess.receive')
          }}:</span
        >
        <div class="col full-width flex gap-x-xs text-end">
          <span>{{
            thousandTool(trade.fiatAmount, trade.fiatCurrency as DigitTypes)
          }}</span>
          <span>{{ trade.fiatCurrency }}</span>
        </div>
      </div>
    </div>

    <q-separator class="full-width" />

    <q-btn
      :to="{ name: 'home' }"
      :label="t('cancelSuccess.btnGoHome')"
      color="accent"
      class="full-width"
    />
  </q-card>
</template>

<script setup lang="ts">
import { inject, Ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { Trade } from '../api';
import { TradeTypeEnum } from 'src/utils/enums';
import { DigitTypes, thousandTool } from 'src/utils/NumberTool';

const { t } = useI18n();
const trade = inject('trade') as Ref<Trade>;
</script>

<style scoped>
.wrapper {
  max-width: 50rem;
}
.title {
  font-size: 1.5rem;
}
.sub-title {
  font-size: 1rem;
}
</style>
