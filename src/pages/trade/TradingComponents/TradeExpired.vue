<template>
  <q-card class="wrapper q-mx-auto q-pa-lg column items-center gap-y-md">
    <div class="column gap-y-md items-center">
      <q-icon class="text-grey text-h1" name="cancel" />
      <span class="title text-bold text-grey">{{
        t('tradeExpired.title')
      }}</span>
    </div>

    <div class="flex items-center gap-x-xs text-orange">
      <q-icon name="info" size="1.2rem" />
      <span>{{ t('tradeExpired.message') }}</span>
    </div>

    <div class="column items-center gap-x-sm full-width">
      <span class="sub-title text-bold"
        >{{
          trade.type === TradeTypeEnum.Buy
            ? t('inPairing.buying')
            : t('inPairing.selling')
        }}
        {{ trade.cryptoSymbol }}</span
      >
      <div class="flex gap-x-xs text-bold">
        <span class="col text-right text-grey"
          >{{ t('inPairing.amountTo') }}
          {{
            trade.type === TradeTypeEnum.Buy
              ? t('inPairing.buy')
              : t('inPairing.sell')
          }}:</span
        >
        <div class="flex gap-x-xs">
          <span>{{
            thousandTool(trade.cryptoAmount, trade.cryptoSymbol as DigitTypes)
          }}</span>
          <span>{{ trade.cryptoSymbol }}</span>
        </div>
      </div>
      <div class="flex gap-x-xs text-bold">
        <span class="text-grey"
          >{{ t('inPairing.youWill') }}
          {{
            trade.type === TradeTypeEnum.Buy
              ? t('inPairing.pay')
              : t('inPairing.receive')
          }}:</span
        >
        <div class="col full-width flex gap-x-xs text-end">
          <span>{{
            thousandTool(trade.fiatAmount, trade.fiatCurrency as DigitTypes)
          }}</span>
          <span>{{ trade.fiatCurrency }}</span>
        </div>
      </div>
    </div>

    <q-separator class="full-width" />

    <q-btn
      color="accent"
      :to="{ name: 'home' }"
      :label="t('tradeExpired.btnGoHome')"
      class="full-width"
    />
  </q-card>
</template>

<script setup lang="ts">
import { Ref } from 'vue';
import { inject } from 'vue';
import { useI18n } from 'vue-i18n';
import { Trade } from '../api';
import { TradeTypeEnum } from 'src/utils/enums';
import { DigitTypes, thousandTool } from 'src/utils/NumberTool';

const { t } = useI18n();
const trade = inject('trade') as Ref<Trade>;
</script>

<style scoped>
.wrapper {
  max-width: 50rem;
}
.title {
  font-size: 1.5rem;
}
</style>
