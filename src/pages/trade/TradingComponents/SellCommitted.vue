<template>
  <q-card class="q-pt-sm no-shadow">
    <q-stepper
      v-model="step"
      alternative-labels
      :contracted="q.screen.lt.sm"
      header-class="no-border no-wrap"
      done-color="accent"
      active-color="accent"
    >
      <q-step
        :name="1"
        prefix="1"
        :title="t('tradePage.create')"
        :done="step > 1"
      />
      <q-step
        :name="2"
        prefix="2"
        :title="t('tradePage.pay')"
        :done="step > 2"
      />
      <q-step
        :name="3"
        prefix="3"
        :title="t('tradePage.confirm')"
        :done="step > 3"
      >
        <div class="column gap-y-md">
          <span class="title text-bold text-center">{{
            t('sellCommitted.title')
          }}</span>
          <span class="text-grey">{{ t('sellCommitted.instruction') }}</span>

          <div>
            <span
              :class="[
                'sub-title text-bold',
                themeStore.isDark ? 'text-blue' : 'text-info',
              ]"
              >{{ t('sellAssigned.orderSummary') }}</span
            >
            <div
              :class="[
                'column gap-y-sm q-pa-md rounded-borders items-center',
                themeStore.isDark ? 'bg-grey-9' : 'bg-grey-2',
              ]"
            >
              <div class="flex gap-x-xs">
                <span class="text-grey text-bold"
                  >{{ t('sellAssigned.orderNumber') }}:</span
                >
                <span>{{ trade.tradeNo }}</span>
              </div>
              <code class="flex gap-sm justify-between full-width">
                <div class="flex flex-1 items-center column gap-x-sm text-bold">
                  <span class="text-grey">{{ t('sellAssigned.selling') }}</span>
                  <div class="flex gap-x-xs">
                    <span>{{
                      thousandTool(
                        trade.cryptoAmount,
                        trade.cryptoSymbol as DigitTypes
                      )
                    }}</span>
                    <span>{{ trade.cryptoSymbol }}</span>
                  </div>
                </div>
                <div class="vertical-seperator"></div>
                <div class="flex flex-1 items-center column gap-x-sm text-bold">
                  <span class="text-grey">{{
                    t('sellAssigned.exchangeRate')
                  }}</span>
                  <div class="flex gap-x-xs">
                    <span>1</span>
                    <span>{{ trade.cryptoSymbol }}</span>
                    <span>=</span>
                    <span>{{ trade.rate }}</span>
                    <span>{{ trade.fiatCurrency }}</span>
                  </div>
                </div>
                <div class="vertical-seperator"></div>
                <div
                  :class="[
                    'flex flex-1 items-center column gap-x-sm text-bold',
                    themeStore.isDark ? 'text-blue' : 'text-info',
                  ]"
                >
                  <span>{{ t('sellAssigned.amountToReceive') }}</span>
                  <div class="flex gap-x-xs">
                    <span>{{
                      thousandTool(
                        trade.fiatAmount,
                        trade.fiatCurrency as DigitTypes
                      )
                    }}</span>
                    <span>{{ trade.fiatCurrency }}</span>
                  </div>
                </div>
              </code>
            </div>
          </div>

          <div>
            <span
              :class="[
                'sub-title text-bold',
                themeStore.isDark ? 'text-blue' : 'text-info',
              ]"
              >{{ t('sellCommitted.buyerInfo') }}</span
            >
            <div
              :class="[
                'flex gap-x-sm q-pa-md rounded-borders',
                themeStore.isDark ? 'bg-grey-9' : 'bg-grey-2',
              ]"
            >
              <div class="column text-grey text-bold">
                <span>{{ t('sellCommitted.buyerName') }}:</span>
                <span>{{ t('sellCommitted.transferAmount') }}:</span>
              </div>
              <div class="column">
                <span>{{ trade.payerBankAccountName }}</span>
                <span
                  >{{
                    thousandTool(
                      trade.fiatAmount,
                      trade.fiatCurrency as DigitTypes
                    )
                  }}
                  {{ trade.fiatCurrency }}</span
                >
              </div>
            </div>
          </div>

          <div>
            <span
              :class="[
                'sub-title text-bold',
                themeStore.isDark ? 'text-blue' : 'text-info',
              ]"
              >{{ t('sellCommitted.bankTransferDetails') }}</span
            >
            <div
              :class="[
                'flex gap-x-sm q-pa-md rounded-borders',
                themeStore.isDark ? 'bg-grey-9' : 'bg-grey-2',
              ]"
            >
              <div class="column text-grey text-bold">
                <span>{{ t('sellCommitted.accountName') }}:</span>
                <span>{{ t('sellCommitted.accountNumber') }}:</span>
                <span>{{ t('sellCommitted.bankName') }}:</span>
                <span>{{ t('sellCommitted.branchCode') }}:</span>
              </div>
              <div class="column">
                <span>{{ trade.accountName }}</span>
                <span>{{ trade.accountNumber }}</span>
                <span>{{ trade.bankName }}</span>
                <span>{{ trade.branchCode }}</span>
              </div>
            </div>
          </div>

          <q-separator />

          <div class="column gap-y-sm full-width">
            <q-btn
              color="accent"
              class="full-width"
              :label="t('sellCommitted.btnConfirm')"
              @click="handleShowConfirmModal"
            />
            <div class="btn-wrapper row gap-x-sm">
              <div class="col">
                <q-btn
                  outline
                  :disable="trade.disputeStatus !== TradeDisputeStatusEnum.None"
                  color="negative"
                  class="full-width"
                  @click="handleShowDisputeModal"
                >
                  <template
                    v-if="trade.disputeStatus !== TradeDisputeStatusEnum.None"
                  >
                    <q-tooltip style="font-size: 0.875rem">{{
                      t('sellCommitted.disputedMessage')
                    }}</q-tooltip>
                    <q-icon name="info" color="negative" size="1.5rem" />
                  </template>
                  <span class="q-ml-sm">
                    {{
                      trade.disputeStatus === TradeDisputeStatusEnum.None
                        ? t('sellCommitted.btnDispute')
                        : t('sellCommitted.btnDisputed')
                    }}
                  </span>
                </q-btn>
              </div>
              <div class="col" style="height: fit-content">
                <payment-proof :tradeId="trade.id" />
              </div>
            </div>
          </div>
        </div>
      </q-step>
      <q-step
        :name="4"
        prefix="4"
        :title="t('tradePage.complete')"
        :done="step > 4"
      />
    </q-stepper>
  </q-card>
  <sell-confirm-modal v-model="showConfirmModal" />
  <dispute-confirm-modal v-model="showDisputeModal" />
</template>

<script setup lang="ts">
import { inject, Ref, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import { Trade } from '../api';
import { useThemeStore } from 'src/stores';
import { TradeDisputeStatusEnum } from 'src/utils/enums';
import { DigitTypes, thousandTool } from 'src/utils/NumberTool';
import SellConfirmModal from './SellConfirmModal.vue';
import DisputeConfirmModal from './DisputeConfirmModal.vue';
import PaymentProof from './PaymentProof.vue';

const q = useQuasar();
const { t } = useI18n();
const themeStore = useThemeStore();

const step = ref(3);
const trade = inject('trade') as Ref<Trade>;
const showConfirmModal = ref<boolean>(false);
const showDisputeModal = ref<boolean>(false);

const handleShowConfirmModal = () => {
  showConfirmModal.value = true;
};
const handleShowDisputeModal = () => {
  showDisputeModal.value = true;
};
</script>

<style scoped>
.title {
  font-size: 1.5rem;
}
.sub-title {
  font-size: 1rem;
}
.vertical-seperator {
  width: 2px;
  height: 100;
  background-color: var(--border-color);
}
.body--dark .vertical-seperator {
  background-color: white;
}
.btn-wrapper {
  height: 2.25rem;
}
</style>
