<template>
  <q-card
    class="chat-box column no-wrap"
    :class="q.screen.lt.md ? 'smooth-transition' : ''"
    :style="{
      top: q.screen.lt.md
        ? showChat
          ? '20vh'
          : `calc(100% - ${CHAT_BOX_HEADER_HEIGHT})`
        : '0',
    }"
  >
    <header class="q-pa-md flex items-center justify-between">
      <span class="title text-bold">{{ t('chatBox.title') }}</span>
      <q-btn
        v-if="q.screen.lt.md"
        round
        size="sm"
        :icon="showChat ? 'expand_more' : 'expand_less'"
        @click="showChat = !showChat"
      />
    </header>

    <q-separator />

    <section
      ref="messagesContainer"
      class="messages q-pa-md flex-1 column gap-y-sm no-wrap"
    >
      <loading-component v-if="loadingMessages" class="full-height" />
      <template v-else>
        <message-item
          v-for="message in messages"
          :key="message.messageId"
          :message="message"
          @load="scrollToBottom"
        />
      </template>
    </section>

    <q-separator />

    <q-form
      class="input flex items-center gap-x-xs q-pa-sm no-wrap"
      @submit.prevent="handleSend"
    >
      <q-btn
        round
        flat
        size="md"
        icon="add_photo_alternate"
        color="accent"
        :disable="loadingMessages || uploadingImage"
        @click="handleSelectFile"
      />
      <input
        ref="fileInputRef"
        type="file"
        accept="image/*"
        class="hidden"
        @change="handleFileSelected"
      />

      <q-input
        dense
        rounded
        outlined
        color="accent"
        ref="messageInput"
        v-model="message"
        borderless
        stack-label
        :placeholder="t('chatBox.messagePlaceholder')"
        class="flex-1"
        :disable="(!showChat && q.screen.lt.md) || loadingMessages"
      />
      <q-btn
        type="submit"
        round
        flat
        size="md"
        icon="send"
        color="accent"
        :disable="loadingMessages"
      />
    </q-form>
  </q-card>
  <upload-preview
    v-model="showUploadPreview"
    :image="previewImage"
    :onUpload="handleUpload"
  />
</template>

<script setup lang="ts">
import { debounce, useQuasar } from 'quasar';
import { inject, nextTick, Ref, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { Message, Trade, useGetMessages, useSendChatImage } from '../api';
import { useChatStore } from 'src/stores';
import { CHAT_BOX_HEADER_HEIGHT } from 'src/utils/constants';
import { ChatMessageType } from 'src/utils/enums';
import LoadingComponent from 'src/components/LoadingComponent.vue';
import MessageItem from './MessageItem.vue';
import UploadPreview from './UploadPreview.vue';

const q = useQuasar();
const { t } = useI18n();
const chatStore = useChatStore();
const trade = inject('trade') as Ref<Trade>;
const { data, loading: loadingMessages } = useGetMessages({
  id: trade.value.id,
});
const { run: uploadImage, loading: uploadingImage } = useSendChatImage();

const showChat = ref(false);
const showUploadPreview = ref<boolean>(false);
const previewImage = ref<string>('');
const selectedFile = ref<File>();
const fileInputRef = ref<HTMLInputElement | null>(null);
const messageInput = ref();
const message = ref('');
const messages = ref<Array<Message>>([]);
const messagesContainer = ref<HTMLElement | null>(null);

const scrollToBottom = debounce(() => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
  });
}, 100);

const handleSelectFile = () => {
  fileInputRef.value?.click();
};
const handleFileSelected = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (!file) return;

  selectedFile.value = target.files?.[0];
  previewImage.value = URL.createObjectURL(file);
  showUploadPreview.value = true;
};
const handleUpload = (caption?: string) => {
  if (!selectedFile.value) return;

  uploadImage({
    tradeId: trade.value.id,
    file: selectedFile.value,
    caption,
  });
};
const handleSend = async () => {
  if (message.value.trim()) {
    await chatStore.sendMessage({
      tradeId: trade.value.id,
      payload: message.value,
      messageType: ChatMessageType.Text,
    });

    message.value = '';
  }

  nextTick(() => {
    messageInput.value?.focus?.();
  });
};

watch(
  () => data.value,
  () => {
    if (data.value) {
      messages.value = [...data.value];
      scrollToBottom();
    }
  }
);

watch(
  () => chatStore.message,
  (newMessage) => {
    if (
      newMessage &&
      !messages.value.find((m) => m.messageId === newMessage.messageId) &&
      trade.value.id === newMessage.tradeId
    ) {
      messages.value.push(newMessage);
      scrollToBottom();
    }
  }
);
</script>

<style scoped>
.chat-box {
  position: absolute;
  inset: 0;
  z-index: 1001;
  border-top-left-radius: 24px;
  overflow: hidden;
}

.smooth-transition {
  transition: top ease-in-out 0.5s;
}

.title {
  font-size: 1rem;
}

.messages {
  overflow-y: auto;
}

@media screen and (max-width: 1023px) {
  .chat-box {
    position: fixed;
    left: 0;
    right: 0;
  }
}
</style>
