<template>
  <q-btn
    outline
    color="accent"
    :label="
      paymentProof ? t('paymentProof.viewProof') : t('paymentProof.noProof')
    "
    :disable="!paymentProof"
    :loading="loadingPaymentProof"
    class="full-width"
    @click="handleViewProof"
  />
  <q-dialog maximized v-model="showImage">
    <div class="background flex items-center justify-center">
      <q-btn
        v-close-popup
        class="bg-white fixed-top-right"
        dense
        unelevated
        color="black"
        icon="close"
      />
      <q-img
        v-if="!loadingPaymentProof && paymentProof?.url"
        :src="paymentProof.url"
      />
    </div>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useGetPaymentProof } from '../api';

const { tradeId } = defineProps<{
  tradeId: number;
}>();

const { t } = useI18n();
const {
  data: paymentProof,
  loading: loadingPaymentProof,
  refresh,
} = useGetPaymentProof({
  id: tradeId,
});

const showImage = ref(false);

const handleViewProof = () => {
  showImage.value = true;
  refresh();
};
</script>

<style scoped>
.background {
  width: 100vw;
  min-height: 100vh;
  padding: 3.5vh 4vw;
}
</style>
