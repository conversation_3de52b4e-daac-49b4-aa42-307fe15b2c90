<template>
  <q-dialog v-model="model" persistent>
    <q-card class="wrapper column items-center gap-y-md q-pa-lg no-wrap">
      <span class="text-h5 text-bold">{{ t('uploadPreview.title') }}</span>
      <div>
        <img :src="image" alt="preview-image" class="image" />
      </div>
      <q-form @submit.prevent="handleUpload" class="column gap-y-md full-width">
        <q-input
          v-model="caption"
          filled
          stack-label
          autofocus
          :label="t('uploadPreview.captionLabel')"
          :placeholder="t('uploadPreview.captionPlaceholder')"
          class="full-width"
        />
        <div class="flex justify-end gap-x-md full-width">
          <q-btn flat :label="t('uploadPreview.cancel')" @click="handleClose" />
          <q-btn
            type="submit"
            color="accent"
            :label="t('uploadPreview.upload')"
          />
        </div>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';

const { modelValue, image, onUpload } = defineProps<{
  modelValue: boolean;
  image: string;
  onUpload: (caption?: string) => void;
}>();
const emit = defineEmits(['update:modelValue']);
const model = computed({
  get: () => modelValue,
  set: (val: boolean) => emit('update:modelValue', val),
});

const { t } = useI18n();

const caption = ref<string>('');

const handleClose = () => {
  emit('update:modelValue', false);
  caption.value = '';
};
const handleUpload = () => {
  onUpload(caption.value);

  handleClose();
};
</script>

<style scoped>
.wrapper {
  min-width: 32rem;
}
.image {
  max-width: 100%;
  max-height: 25rem;
  border-radius: 8px;
}
</style>
