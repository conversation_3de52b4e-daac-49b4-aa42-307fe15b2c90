import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

export type BuyPaymentConfirm = {
  id: number;
};

type BuyPaymentConfirmRes = unknown;

const useBuyPaymentConfirm = () => {
  const vueRequest = requestProvider<BuyPaymentConfirmRes, BuyPaymentConfirm>(
    (props: BuyPaymentConfirm) => {
      const request = axiosProvider
        .put(`/trade-orders/buy/${props.id}/payment-confirmed`)
        .then(({ data }) => data);

      return request;
    },
    {
      manual: true,
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
  return vueRequest;
};

export { useBuyPaymentConfirm };
