import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

export type CreateBuyProps = {
  usdtAmount: number;
  bankAccountName: string;
};

type CreateBuyRes = {
  id: number;
  orderNo: string;
};

const useCreateBuy = ({ ...useProps }: UseProps<CreateBuyRes>) => {
  const vueRequest = requestProvider<CreateBuyRes, CreateBuyProps>(
    (props: CreateBuyProps) => {
      const request = axiosProvider
        .post('/trade-orders/buy', props)
        .then(({ data }) => data);

      return request;
    },
    {
      ...useProps,
      manual: true,
      onSuccess: (res) => {
        if (useProps.onSuccess) useProps.onSuccess(res);
      },
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
  return vueRequest;
};

export { useCreateBuy };
