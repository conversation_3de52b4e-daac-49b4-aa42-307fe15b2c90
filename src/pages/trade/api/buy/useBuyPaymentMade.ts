import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

export type BuyPaymentMadeProps = {
  id: number;
};

type BuyPaymentMadeRes = unknown;

const useBuyPaymentMade = () => {
  const vueRequest = requestProvider<BuyPaymentMadeRes, BuyPaymentMadeProps>(
    (props: BuyPaymentMadeProps) => {
      const request = axiosProvider
        .put(`/trade-orders/buy/${props.id}/payment-made`)
        .then(({ data }) => data);

      return request;
    },
    {
      manual: true,
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
  return vueRequest;
};

export { useBuyPaymentMade };
