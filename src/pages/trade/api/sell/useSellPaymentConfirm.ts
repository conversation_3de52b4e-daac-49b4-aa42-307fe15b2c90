import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

export type SellPaymentConfirmProps = {
  id: number;
};

type SellPaymentConfirmRes = unknown;

const useSellPaymentConfirm = () => {
  const vueRequest = requestProvider<
    SellPaymentConfirmRes,
    SellPaymentConfirmProps
  >(
    (props: SellPaymentConfirmProps) => {
      const request = axiosProvider
        .put(`/trade-orders/sell/${props.id}/payment-confirmed`)
        .then(({ data }) => data);

      return request;
    },
    {
      manual: true,
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
  return vueRequest;
};

export { useSellPaymentConfirm };
