import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

export type DisputeProps = {
  id: number;
};

type DisputeRes = unknown;

const useDispute = ({ ...useProps }: UseProps) => {
  const vueRequest = requestProvider<DisputeRes, DisputeProps>(
    (props: DisputeProps) => {
      const request = axiosProvider
        .put(`/dispute/${props.id}/submit`)
        .then(({ data }) => data);

      return request;
    },
    {
      ...useProps,
      manual: true,
      onError: (res) => {
        if (useProps.onError) useProps.onError(res);
      },
    },
    {
      noErrorNotify: true,
      noFeedback: true,
      noTempData: true,
    }
  );
  return vueRequest;
};

export { useDispute };
