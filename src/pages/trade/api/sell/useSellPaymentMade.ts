import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

export type SellPaymentMadeProps = {
  id: number;
};

type SellPaymentMadeRes = unknown;

const useSellPaymentMade = () => {
  const vueRequest = requestProvider<SellPaymentMadeRes, SellPaymentMadeProps>(
    (props: SellPaymentMadeProps) => {
      const request = axiosProvider
        .put(`/trade-orders/sell/${props.id}/payment-made`)
        .then(({ data }) => data);

      return request;
    },
    {
      manual: true,
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
  return vueRequest;
};

export { useSellPaymentMade };
