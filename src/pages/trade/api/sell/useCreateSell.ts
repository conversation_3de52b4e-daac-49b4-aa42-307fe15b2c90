import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

export type CreateSellProps = {
  usdtAmount: number;
  bankName: string;
  payeeName: string;
  bankAccountNumber: string;
  branchCode: string;
};

type CreateSellRes = {
  id: number;
  orderNo: string;
};

const useCreateSell = ({ ...useProps }: UseProps<CreateSellRes>) => {
  const vueRequest = requestProvider<CreateSellRes, CreateSellProps>(
    (props: CreateSellProps) => {
      const request = axiosProvider
        .post('/trade-orders/sell', props)
        .then(({ data }) => data);

      return request;
    },
    {
      ...useProps,
      manual: true,
      onSuccess: (res) => {
        if (useProps.onSuccess) useProps.onSuccess(res);
      },
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
  return vueRequest;
};

export { useCreateSell };
