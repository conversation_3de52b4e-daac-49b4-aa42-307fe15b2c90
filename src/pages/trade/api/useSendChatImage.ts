import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

export type SendChatImageProps = {
  tradeId: number;
  file: File;
  caption?: string;
};

type SendChatImageRes = unknown;

const useSendChatImage = () => {
  const vueRequest = requestProvider<SendChatImageRes, SendChatImageProps>(
    (props: SendChatImageProps) => {
      const formData = new FormData();
      formData.append('TradeId', String(props.tradeId));
      formData.append('File', props.file);
      if (props.caption) {
        formData.append('Caption', props.caption);
      }

      const request = axiosProvider
        .post('/chat/upload-image', formData)
        .then(({ data }) => data);

      return request;
    },
    {
      manual: true,
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export { useSendChatImage };
