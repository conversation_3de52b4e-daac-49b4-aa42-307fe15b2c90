import { axiosProvider } from 'src/utils/axiosProvider';
import { ChatMessageType, SenderRole } from 'src/utils/enums';
import { requestProvider } from 'src/utils/requestProvider';

interface Message {
  tradeId: number;
  messageId: number;
  senderId: string;
  senderName: string;
  senderRole: SenderRole;
  messageType: ChatMessageType;
  payload: string;
  caption: string;
  sentAt: string;
}

type MessagesRes = Array<Message>;

interface MessagesProps {
  id: number;
}

const useGetMessages = (useProps: MessagesProps) => {
  const vueRequest = requestProvider<MessagesRes, MessagesProps>(
    (props) => {
      const request = axiosProvider
        .get(`/chat/${props.id}/messages`)
        .then(({ data }) => data);

      return request;
    },
    {
      defaultParams: [useProps],
      manual: false,
    },
    {
      noFeedback: true,
      noErrorNotify: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export { useGetMessages };
export type { Message };
