import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

export type SendPaymentProofProps = {
  tradeId: number;
  file: File;
};

type SendPaymentProofRes = {
  url: string;
};

const useSendPaymentProof = ({
  ...useProps
}: UseProps<SendPaymentProofRes>) => {
  const vueRequest = requestProvider<
    SendPaymentProofRes,
    SendPaymentProofProps
  >(
    (props: SendPaymentProofProps) => {
      const formData = new FormData();
      formData.append('TradeId', String(props.tradeId));
      formData.append('File', props.file);

      const request = axiosProvider
        .post('/trade-orders/payment-proof', formData)
        .then(({ data }) => data);

      return request;
    },
    {
      ...useProps,
      manual: true,
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export { useSendPaymentProof };
