import { CurrencyCode } from 'src/api';
import { axiosProvider } from 'src/utils/axiosProvider';
import {
  TradeDisputeStatusEnum,
  TradeModeEnum,
  TradeStatusEnum,
  TradeTypeEnum,
  WalletNetworkEnum,
} from 'src/utils/enums';
import { requestProvider } from 'src/utils/requestProvider';

interface Trade {
  id: number;
  fiatCurrency: CurrencyCode['code'];
  fiatAmount: number;
  network: WalletNetworkEnum;
  networkDesc: string;
  cryptoSymbol: string;
  cryptoAmount: number;
  rate: number;
  agentFeeAmount: number;
  userPaidAt: string;
  completedAt: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  agentId: string;
  disputeStatus: TradeDisputeStatusEnum;
  disputeStatusDesc: string;
  tradeNo: string;
  type: TradeTypeEnum;
  typeDesc: string;
  mode: TradeModeEnum;
  modeDesc: string;
  status: TradeStatusEnum;
  statusDesc: string;
  bankName: string;
  accountName: string;
  accountNumber: string;
  branchCode: string;
  payerBankAccountName: string;
}

interface TradesRes {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: Array<Trade>;
}

interface TradesProps {
  id?: number;
}

const useGetTradeById = () => {
  const vueRequest = requestProvider<TradesRes, TradesProps>(
    (props) => {
      const request = axiosProvider
        .get('/trade-orders/my', { params: props })
        .then(({ data }) => data);

      return request;
    },
    {
      manual: true,
    },
    {
      noFeedback: true,
      noErrorNotify: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export { useGetTradeById };
export type { Trade };
