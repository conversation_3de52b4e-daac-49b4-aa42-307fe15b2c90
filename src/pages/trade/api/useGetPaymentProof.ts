import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

interface PaymentProofRes {
  url: string;
}

interface PaymentProofProps {
  id: number;
}

const useGetPaymentProof = (useProps: PaymentProofProps) => {
  const vueRequest = requestProvider<PaymentProofRes, PaymentProofProps>(
    (props) => {
      const request = axiosProvider
        .get(`/trade-orders/${props.id}/payment-proof`)
        .then(({ data }) => data);

      return request;
    },
    {
      defaultParams: [useProps],
      manual: false,
    },
    {
      noFeedback: true,
      noErrorNotify: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export { useGetPaymentProof };
