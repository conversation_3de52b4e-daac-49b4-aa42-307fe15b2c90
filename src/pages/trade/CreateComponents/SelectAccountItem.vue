<template>
  <div class="wrapper flex items-center justify-between q-pb-sm">
    <div class="flex gap-x-sm">
      <div class="column">
        <span class="name text-bold"
          >{{ t('selectAccountItem.bankNameLabel') }}:</span
        >
        <span class="text-bold text-grey"
          >{{ t('selectAccountItem.accountNameLabel') }}:</span
        >
        <span class="text-bold text-grey"
          >{{ t('selectAccountItem.accountNumberLabel') }}:</span
        >
        <span class="text-bold text-grey"
          >{{ t('selectAccountItem.branchCodeLabel') }}:</span
        >
        <span class="text-bold text-grey"
          >{{ t('selectAccountItem.currencyLabel') }}:</span
        >
      </div>
      <div class="column">
        <span class="name text-bold">{{ account.bankName }}</span>
        <span class="text-grey">{{ account.accountName }}</span>
        <span class="text-grey">{{ account.accountNumber }}</span>
        <span class="text-grey">{{ account.branchCode }}</span>
        <span class="text-grey">{{ account.currency }}</span>
      </div>
    </div>

    <q-btn
      outline
      color="accent"
      :label="
        currentAccountId === account.id
          ? t('selectAccountItem.btnSelected')
          : t('selectAccountItem.btnSelect')
      "
      :disable="currentAccountId === account.id"
      @click="$emit('on-select-account', account)"
    />
  </div>
</template>

<script setup lang="ts">
import { inject } from 'vue';
import { useI18n } from 'vue-i18n';
import { BankAccount } from 'src/pages/accounts/api';

const { account } = defineProps<{
  account: BankAccount;
}>();

const { t } = useI18n();

const currentAccountId = inject('currentAccountId');
</script>

<style scoped>
.wrapper:not(:last-child) {
  border-bottom: 1px solid var(--border-color);
}
</style>
