<template>
  <q-form class="column q-mb-lg gap-y-md" @submit.prevent="handleCreate">
    <div class="title text-bold text-center">{{ t('sellForm.title') }}</div>

    <div class="column">
      <div class="flex gap-x-xs text-bold self-end">
        <span class="text-grey">{{ t('sellForm.availableBalance') }}:</span>
        <span class="text-info"
          >{{ thousandTool(walletsStore.availableAmount, DEFAULT_CRYPTO) }}
          {{ DEFAULT_CRYPTO }}</span
        >
      </div>
      <q-input
        filled
        stack-label
        :label="t('sellForm.usdtAmountLabel')"
        v-model="form.usdtAmount"
        :loading="loadingExchangeRates"
        :disable="loadingExchangeRates || !exchangeRates"
        @focus="
          () => {
            if (numberTool(form.usdtAmount) < 1) form.usdtAmount = '0';
          }
        "
        @update:model-value="
          (val: string | number | null) => {
            form.usdtAmount = thousandInput(val);
            form.price = thousandTool(numberTool(val) * numberTool(defaultCurrencyRate?.bidPrice), defaultCurrencyRate?.fiatCode as DigitTypes);
          }
        "
        :rules="[
          (val: string) => numberTool(val) > 0 || t('sellForm.usdtAmountError'),
          (val: string) => numberTool(val) <= walletsStore.availableAmount || t('sellForm.exceedsAvailableBalance'),
        ]"
        @blur="
          () => {
            if (!/^[0-9,.]+$/.test(form.usdtAmount)) form.usdtAmount = '0';
          }
        "
      >
        <template v-slot:append>
          <div class="text-grey text-subtitle2">
            {{ defaultCurrencyRate?.cryptoSymbol }}
          </div>
        </template>
      </q-input>
    </div>

    <div class="rate-wrapper row full-width">
      <div class="col-4"></div>
      <div class="col flex justify-center">
        <q-img
          src="src/assets/images/icon-repeat.png"
          style="width: 1.5rem; height: 1.5rem"
        />
      </div>
      <div
        v-if="!loadingExchangeRates && !exchangeRates"
        class="col-4 text-negative text-right"
      >
        {{ t('sellForm.noExchangeRate') }}
      </div>
      <div v-else class="col-4 text-right text-caption text-grey-7">
        <q-skeleton
          v-if="loadingExchangeRates"
          type="text"
          class="q-ml-auto"
          style="width: 6rem; height: 1.5rem"
        />
        <template v-else>
          1 {{ defaultCurrencyRate?.cryptoSymbol }}
          <span class="text-accent"
            >≈ {{ defaultCurrencyRate?.bidPrice }}
            {{ defaultCurrencyRate?.fiatCode }}
          </span>
        </template>
      </div>
    </div>

    <q-input
      filled
      stack-label
      :label="t('sellForm.fiatAmountLabel')"
      v-model="form.price"
      :loading="loadingExchangeRates"
      :disable="loadingExchangeRates || !exchangeRates"
      @update:model-value="
        (val: string | number | null) => {
          form.price = thousandInput(val);
          form.usdtAmount = thousandTool(
            numberTool(val) / numberTool(defaultCurrencyRate?.bidPrice),
            defaultCurrencyRate?.cryptoSymbol as DigitTypes
          );
        }
      "
      @focus="
        () => {
          if (numberTool(form.price) < 1) form.price = '0';
        }
      "
      :rules="[
        (val: string) => numberTool(val) > 0 || t('sellForm.fiatAmountError'),
      ]"
      @blur="
        () => {
          if (!/^[0-9,.]+$/.test(form.price)) form.price = '0';
        }
      "
    >
      <template v-slot:append>
        <div class="text-grey text-subtitle2">
          {{ defaultCurrencyRate?.fiatCode }}
        </div>
      </template>
    </q-input>

    <q-card class="q-pa-md">
      <div class="flex items-center justify-between">
        <div class="label text-bold">
          {{ t('sellForm.bankAccountInformation') }}
        </div>
        <q-btn
          flat
          color="accent"
          :label="t('sellForm.btnSelectBankAccount')"
          @click="showBankAccountsModal = true"
        />
      </div>
      <q-separator class="q-my-sm" />
      <div class="column gap-y-md">
        <q-input
          v-model="form.bankName"
          filled
          stack-label
          :label="t('sellForm.bankNameLabel')"
          :placeholder="t('sellForm.bankNamePlaceholder')"
          :loading="loadingDefaultBankAccount"
          :disable="loadingDefaultBankAccount"
          @update:model-value="unsetCurrentAccountId"
          :rules="[(val: string) => !!val || t('sellForm.bankNameError')]"
        />
        <q-input
          v-model="form.accountNumber"
          filled
          stack-label
          :label="t('sellForm.accountNumberLabel')"
          :placeholder="t('sellForm.accountNumberPlaceholder')"
          :loading="loadingDefaultBankAccount"
          :disable="loadingDefaultBankAccount"
          @update:model-value="unsetCurrentAccountId"
          :rules="[(val: string) => !!val || t('sellForm.accountNumberError')]"
        />
        <q-input
          v-model="form.accountName"
          filled
          stack-label
          :label="t('sellForm.accountNameLabel')"
          :placeholder="t('sellForm.accountNamePlaceholder')"
          :loading="loadingDefaultBankAccount"
          :disable="loadingDefaultBankAccount"
          @update:model-value="unsetCurrentAccountId"
          :rules="[(val: string) => !!val || t('sellForm.accountNameError')]"
        />
        <q-input
          v-model="form.branchCode"
          filled
          stack-label
          :label="t('sellForm.branchCodeLabel')"
          :placeholder="t('sellForm.branchCodePlaceholder')"
          :loading="loadingDefaultBankAccount"
          :disable="loadingDefaultBankAccount"
          @update:model-value="unsetCurrentAccountId"
          :rules="[(val: string) => !!val || t('sellForm.branchCodeError')]"
        />
      </div>
    </q-card>

    <div>
      <div class="label">
        {{ t('sellForm.orderInformation') }}
      </div>
      <div
        :class="[
          'q-mt-xs q-pa-lg text-bold',
          themeStore.isDark ? 'bg-primary' : 'bg-light-blue-1',
        ]"
      >
        <div class="flex items-center justify-between">
          <span>{{ t('sellForm.amountToSell') }}</span>
          <q-spinner v-if="loadingExchangeRates" color="grey" size="1.5rem" />
          <span v-else class="order-info-value"
            >{{ form.usdtAmount }} {{ defaultCurrencyRate?.cryptoSymbol }}</span
          >
        </div>
        <div class="flex items-center justify-between text-info">
          <span>{{ t('sellForm.amountToReceive') }}</span>
          <q-spinner v-if="loadingExchangeRates" color="grey" size="1.5rem" />
          <span v-else class="order-info-value"
            >{{ form.price }} {{ defaultCurrencyRate?.fiatCode }}</span
          >
        </div>
      </div>
    </div>

    <div class="text-center text-caption text-grey-6">
      {{ t('sellForm.rateDescription') }}
    </div>

    <agreement-checkbox v-model="form.agreement" ref="agreementRef" />

    <q-btn
      type="submit"
      color="accent"
      :label="t('sellForm.submit')"
      class="q-py-sm"
      :loading="loading"
    />
  </q-form>

  <select-account-modal
    v-model="showBankAccountsModal"
    @on-select-account="handleSelectAccount"
  />
  <confirm-dialog
    v-model="showConfirmDialog"
    type="warning"
    :title="t('sellForm.confirmTitle')"
    :message="t('sellForm.confirmMessage')"
    :on-confirm="handleConfirmCreate"
  />
</template>

<script setup lang="ts">
import { computed, provide, reactive, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useExchangeRates } from 'src/api';
import { useCreateSell } from '../api';
import { BankAccount, useGetDefaultBankAccount } from 'src/pages/accounts/api';
import { useThemeStore, useWalletsStore } from 'src/stores';
import { DEFAULT_CRYPTO, DEFAULT_CURRENCY } from 'src/utils/constants';
import {
  DigitTypes,
  numberTool,
  thousandInput,
  thousandTool,
} from 'src/utils/NumberTool';
import ConfirmDialog from 'src/components/ConfirmDialog.vue';
import SelectAccountModal from './SelectAccountModal.vue';
import AgreementCheckbox from './AgreementCheckbox.vue';

const { t } = useI18n();
const router = useRouter();
const themeStore = useThemeStore();
const walletsStore = useWalletsStore();
const { defaultBankAccount, loading: loadingDefaultBankAccount } =
  useGetDefaultBankAccount();
const { data: exchangeRates, loading: loadingExchangeRates } =
  useExchangeRates();
const { run: createSell, loading } = useCreateSell({
  onSuccess: (res) => {
    if (res) {
      router.replace({
        name: 'trade',
        query: { type: 'sell', action: 'trading', id: res.id },
      });
    }
  },
});

const defaultCurrencyRate = computed(() =>
  exchangeRates.value?.find(
    (exchangeRate) => exchangeRate.fiatCode === DEFAULT_CURRENCY
  )
);

const form = reactive({
  usdtAmount: '0',
  price: '0',
  bankName: '',
  accountNumber: '',
  accountName: '',
  branchCode: '',
  agreement: false,
});
const agreementRef = ref();
const showBankAccountsModal = ref<boolean>(false);
const showConfirmDialog = ref<boolean>(false);

const handleCreate = () => {
  if (!agreementRef.value.validate()) return;

  showConfirmDialog.value = true;
};
const handleConfirmCreate = () => {
  createSell({
    usdtAmount: numberTool(form.usdtAmount),
    bankName: form.bankName,
    payeeName: form.accountName,
    bankAccountNumber: form.accountNumber,
    branchCode: form.branchCode,
  });
};

const currentAccountId = ref<number | null>(null); // used to disable the select button in SelectAccountModal
provide('currentAccountId', currentAccountId);
const unsetCurrentAccountId = () => {
  currentAccountId.value = null;
};
const handleSelectAccount = (account: BankAccount) => {
  form.bankName = account.bankName;
  form.accountNumber = account.accountNumber;
  form.accountName = account.accountName;
  form.branchCode = account.branchCode;
  currentAccountId.value = account.id;
};
// auto-fill default bank account if exists
watch(
  () => defaultBankAccount.value,
  (defaultAccount) => {
    if (defaultAccount) {
      form.bankName = defaultAccount.bankName;
      form.accountNumber = defaultAccount.accountNumber;
      form.accountName = defaultAccount.accountName;
      form.branchCode = defaultAccount.branchCode;
      currentAccountId.value = defaultAccount.id;
    }
  },
  { immediate: true }
);
</script>

<style scoped>
.title {
  font-size: 1.5rem;
}
.label {
  font-size: 1rem;
}
.rate-wrapper {
  margin: -1.5rem 0 -0.5rem;
}
.order-info-value {
  font-size: 1rem;
}
</style>
