<template>
  <q-dialog v-model="model">
    <q-card class="wrapper column flex gap-y-md q-pa-md no-wrap">
      <header class="flex items-center justify-between">
        <span class="title text-bold">{{ t('selectAccountModal.title') }}</span>
        <q-btn flat round icon="close" color="grey" @click="handleClose" />
      </header>

      <select-account-header v-model="search" :loading="loadingBankAccounts" />

      <pagination-component
        v-model="currentPage"
        :page-size="pageSize"
        :total-pages="bankAccounts?.totalPages || 1"
        :total-count="bankAccounts?.totalCount"
        :loading="loadingBankAccounts"
        @update:modelValue="setPage"
        @update:pageSize="setPageSize"
      />

      <loading-component v-if="loadingBankAccounts" />
      <section
        v-else-if="bankAccounts?.items.length === 0"
        class="column items-center gap-y-sm"
      >
        <span class="text-grey text-bold">
          {{ t('selectAccountModal.emptyList') }}
        </span>
        <q-btn
          v-if="isEmptySearch()"
          :to="{ name: 'accounts' }"
          outline
          color="accent"
          :label="t('selectAccountModal.btnAdd')"
        />
      </section>
      <section v-else class="column gap-y-sm">
        <select-account-item
          v-for="bankAccount in bankAccounts?.items"
          :key="bankAccount.id"
          :account="bankAccount"
          @on-select-account="handleSelectAccount"
        />
      </section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  useBankAccounts,
  BankAccount,
  BankAccountsProps,
} from 'src/pages/accounts/api';
import usePagination from 'src/hooks/usePagination';
import LoadingComponent from 'src/components/LoadingComponent.vue';
import PaginationComponent from 'src/components/PaginationComponent.vue';
import SelectAccountHeader from './SelectAccountHeader.vue';
import SelectAccountItem from './SelectAccountItem.vue';

const { modelValue } = defineProps<{
  modelValue: boolean;
}>();
const emit = defineEmits(['update:modelValue', 'on-select-account']);

const { t } = useI18n();
const { currentPage, pageSize, setPage, setPageSize } = usePagination(1, 5);
const {
  data: bankAccounts,
  loading: loadingBankAccounts,
  run,
} = useBankAccounts({});

const search = reactive<BankAccountsProps>({
  bankName: '',
  accountName: '',
  accountNumber: '',
  currency: '',
  isDefault: null,
});

const model = computed({
  get: () => modelValue,
  set: (val: boolean) => emit('update:modelValue', val),
});

const isEmptySearch = () =>
  search.bankName === '' &&
  search.accountName === '' &&
  search.accountNumber === '' &&
  search.currency === '' &&
  search.isDefault === null;
const handleClose = () => {
  emit('update:modelValue', false);
};
const handleSelectAccount = (account: BankAccount) => {
  emit('on-select-account', account);
  handleClose();
};

watch(
  [currentPage, pageSize, search],
  () => {
    run({
      pageNumber: currentPage.value,
      pageSize: pageSize.value,
      bankName: search.bankName,
      accountName: search.accountName,
      accountNumber: search.accountNumber,
      currency: search.currency,
      isDefault: search.isDefault,
    });
  },
  {
    immediate: true,
  }
);
</script>

<style scoped>
.wrapper {
  width: 33.75rem;
  max-width: 100%;
}
.title {
  font-size: 1.5rem;
}
</style>
