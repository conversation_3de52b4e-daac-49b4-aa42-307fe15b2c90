<template>
  <div>
    <div
      class="flex items-center gap-x-xs"
      :style="q.screen.lt.sm ? 'font-size: 8.2px' : ''"
    >
      <q-checkbox
        v-model="localChecked"
        :label="t('agreementCheckbox.label')"
        @update:model-value="onCheckboxChange"
      />
      <div
        v-for="(agreement, index) in agreements"
        :key="agreement.value"
        class="flex items-center gap-x-xs"
      >
        <q-btn
          flat
          dense
          no-caps
          padding="0"
          tabindex="-1"
          class="text-accent text-bold"
          :style="q.screen.lt.sm ? 'font-size: 8.2px' : ''"
          @click="handleViewPdf(agreement.value)"
        >
          {{ agreement.label }}
        </q-btn>
        <span v-if="index !== agreements.length - 1">
          {{ t('agreementCheckbox.and') }}
        </span>
      </div>
    </div>

    <div v-if="error" class="flex items-center gap-x-xs text-negative">
      <q-icon name="error" size="1.5rem" />
      <span>{{ error }}</span>
    </div>

    <PdfViewer
      v-model:showPdf="showPdf"
      :content="selectedPdfUrl"
      :title="selectedPdfTitle"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import PdfViewer from 'src/pages/auth/components/PdfViewer.vue';
import PolicyCN from 'src/assets/pdf/k28DemoPolicyCN.pdf';
import PolicyEng from 'src/assets/pdf/k28DemoPolicyEng.pdf';
import TermsCN from 'src/assets/pdf/k28DemoTermsCN.pdf';
import TermsEng from 'src/assets/pdf/k28DemoTermsEng.pdf';
import { useQuasar } from 'quasar';

type AgreementType = 'policy' | 'terms';

const props = defineProps<{ modelValue: boolean }>();
const emit = defineEmits(['update:modelValue']);
const q = useQuasar();
const { locale, t } = useI18n();

const localChecked = ref(props.modelValue);
const error = ref('');

watch(
  () => props.modelValue,
  (val) => {
    localChecked.value = val;
  }
);

watch(localChecked, (val) => {
  emit('update:modelValue', val);
  if (val) error.value = '';
});

const pdfZhCN = { policy: PolicyCN, terms: TermsCN };
const pdfEnUS = { policy: PolicyEng, terms: TermsEng };
const pdfFiles = computed(() => {
  if (locale.value === 'zh-TW') return pdfZhCN;
  if (locale.value === 'en-US') return pdfEnUS;
  return pdfEnUS;
});

const agreements = computed((): { label: string; value: AgreementType }[] => [
  { label: t('agreementCheckbox.policy'), value: 'policy' },
  { label: t('agreementCheckbox.terms'), value: 'terms' },
]);

const selectedPdfUrl = ref('');
const selectedPdfTitle = ref('');
const showPdf = ref(false);

function handleViewPdf(value: 'policy' | 'terms') {
  selectedPdfUrl.value = pdfFiles.value[value];
  selectedPdfTitle.value =
    agreements.value.find((a) => a.value === value)?.label || '';
  showPdf.value = true;
}

function onCheckboxChange(val: boolean) {
  localChecked.value = val;
}

function validate() {
  if (!localChecked.value) {
    error.value = t('agreementCheckbox.error');
    return false;
  }
  error.value = '';
  return true;
}

defineExpose({ validate });
</script>
