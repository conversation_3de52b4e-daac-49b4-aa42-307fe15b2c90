<template>
  <section class="flex column gap-y-sm">
    <div class="row gap-sm">
      <q-input
        dense
        filled
        clearable
        debounce="500"
        v-model="search.bankName"
        :label="t('selectAccountHeader.bankNameLabel')"
        :disable="loading"
        :class="[q.screen.lt.sm ? 'full-width' : 'col']"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
      <q-input
        dense
        filled
        clearable
        debounce="500"
        v-model="search.accountName"
        :label="t('selectAccountHeader.accountNameLabel')"
        :disable="loading"
        :class="[q.screen.lt.sm ? 'full-width' : 'col']"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
    </div>

    <div class="row gap-sm">
      <q-input
        dense
        filled
        clearable
        debounce="500"
        v-model="search.accountNumber"
        :label="t('selectAccountHeader.accountNumberLabel')"
        :disable="loading"
        :class="[q.screen.lt.sm ? 'full-width' : 'col']"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
      <div class="col flex">
        <q-select
          dense
          filled
          clearable
          v-model="search.currency"
          :options="currencyOptions"
          :label="t('selectAccountHeader.currencyLabel')"
          :disable="loading || loadingCurrencyCodes"
          class="flex-1"
        />
        <q-checkbox
          toggle-order="tf"
          toggle-indeterminate
          color="accent"
          v-model="search.isDefault"
          :label="t('selectAccountHeader.isDefaultLabel')"
          :disable="loading"
        />
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { useCurrencyCodes } from 'src/api';
import { BankAccountsProps } from 'src/pages/accounts/api';

const { modelValue, loading } = defineProps<{
  modelValue: BankAccountsProps;
  loading: boolean;
}>();
const emit = defineEmits(['update:modelValue']);

const q = useQuasar();
const { t } = useI18n();
const { data: currencyCodes, loading: loadingCurrencyCodes } =
  useCurrencyCodes();

const search = computed({
  get: () => modelValue,
  set: (val: BankAccountsProps) => emit('update:modelValue', val),
});
const currencyOptions = computed(() =>
  currencyCodes.value?.map((currencyCode) => currencyCode.code)
);
</script>
