<template>
  <q-form class="column q-mb-lg gap-y-md" @submit.prevent="handleCreate">
    <div class="title text-bold text-center">{{ t('buyForm.title') }}</div>
    <q-input
      filled
      stack-label
      :label="t('buyForm.usdtAmountLabel')"
      v-model="form.usdtAmount"
      :loading="loadingExchangeRates"
      :disable="loadingExchangeRates || !exchangeRates"
      @focus="
        () => {
          if (numberTool(form.usdtAmount) < 1) form.usdtAmount = '0';
        }
      "
      @update:model-value="
          (val: string | number | null) => {
            form.usdtAmount = thousandInput(val);
            form.price = thousandTool(numberTool(val) * numberTool(defaultCurrencyRate?.askPrice), defaultCurrencyRate?.fiatCode as DigitTypes);
          }
        "
      :rules="[
          (val: string) => numberTool(val) > 0 || t('buyForm.usdtAmountError'),
        ]"
      @blur="
        () => {
          if (!/^[0-9,.]+$/.test(form.usdtAmount)) form.usdtAmount = '0';
        }
      "
      inputmode="numeric"
      lang="en"
    >
      <template v-slot:append>
        <div class="text-grey text-subtitle2">
          {{ defaultCurrencyRate?.cryptoSymbol }}
        </div>
      </template>
    </q-input>

    <div class="rate-wrapper row full-width">
      <div class="col-4"></div>
      <div class="col flex justify-center">
        <q-img
          src="src/assets/images/icon-repeat.png"
          style="width: 1.5rem; height: 1.5rem"
        />
      </div>
      <div
        v-if="!loadingExchangeRates && !exchangeRates"
        class="col-4 text-negative text-right"
      >
        {{ t('buyForm.noExchangeRate') }}
      </div>
      <div v-else class="col-4 text-right text-caption text-grey-7">
        <q-skeleton
          v-if="loadingExchangeRates"
          type="text"
          class="q-ml-auto"
          style="width: 6rem; height: 1.5rem"
        />
        <template v-else>
          1 {{ defaultCurrencyRate?.cryptoSymbol }}
          <span class="text-accent"
            >≈ {{ defaultCurrencyRate?.askPrice }}
            {{ defaultCurrencyRate?.fiatCode }}
          </span>
        </template>
      </div>
    </div>

    <q-input
      filled
      stack-label
      :label="t('buyForm.fiatAmountLabel')"
      v-model="form.price"
      :loading="loadingExchangeRates"
      :disable="loadingExchangeRates || !exchangeRates"
      @update:model-value="
        (val: string | number | null) => {
          form.price = thousandInput(val);
          form.usdtAmount = thousandTool(
            numberTool(val) / numberTool(defaultCurrencyRate?.askPrice),
            defaultCurrencyRate?.cryptoSymbol as DigitTypes
          );
        }
      "
      @focus="
        () => {
          if (numberTool(form.price) < 1) form.price = '0';
        }
      "
      :rules="[
        (val: string) => numberTool(val) > 0 || t('buyForm.fiatAmountError'),
      ]"
      @blur="
        () => {
          if (!/^[0-9,.]+$/.test(form.price)) form.price = '0';
        }
      "
    >
      <template v-slot:append>
        <div class="text-grey text-subtitle2">
          {{ defaultCurrencyRate?.fiatCode }}
        </div>
      </template>
    </q-input>

    <q-input
      v-model="form.bankAccountName"
      filled
      stack-label
      :label="t('buyForm.bankAccountNameLabel')"
      :placeholder="t('buyForm.bankAccountNamePlaceholder')"
      :rules="[
          (val: string | number | null) => !!val || t('buyForm.bankAccountNameError'),
        ]"
    />

    <div>
      <div class="label">
        {{ t('buyForm.orderInformation') }}
      </div>
      <div
        :class="[
          'q-mt-xs q-pa-lg text-bold rounded-borders',
          themeStore.isDark ? 'bg-primary' : 'bg-light-blue-1',
        ]"
      >
        <div class="flex items-center justify-between">
          <span>{{ t('buyForm.amountToBuy') }}</span>
          <q-spinner v-if="loadingExchangeRates" color="grey" size="1.5rem" />
          <span v-else class="order-info-value"
            >{{ form.usdtAmount }} {{ defaultCurrencyRate?.cryptoSymbol }}</span
          >
        </div>
        <div class="flex items-center justify-between text-info">
          <span>{{ t('buyForm.amountToPay') }}</span>
          <q-spinner v-if="loadingExchangeRates" color="grey" size="1.5rem" />
          <span v-else class="order-info-value"
            >{{ form.price }} {{ defaultCurrencyRate?.fiatCode }}</span
          >
        </div>
      </div>
    </div>

    <div class="text-center text-caption text-grey-6">
      {{ t('buyForm.rateDescription') }}
    </div>

    <agreement-checkbox v-model="form.agreement" ref="agreementRef" />

    <q-btn
      type="submit"
      color="accent"
      :label="t('buyForm.submit')"
      class="q-py-sm"
      :loading="loading"
    />
  </q-form>

  <confirm-dialog
    v-model="showConfirmDialog"
    type="warning"
    :title="t('buyForm.confirmTitle')"
    :message="t('buyForm.confirmMessage')"
    :on-confirm="handleConfirmCreate"
  />
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useCreateBuy } from '../api';
import { useExchangeRates } from 'src/api';
import { DEFAULT_CURRENCY } from 'src/utils/constants';
import {
  DigitTypes,
  numberTool,
  thousandInput,
  thousandTool,
} from 'src/utils/NumberTool';
import { useThemeStore } from 'src/stores';
import ConfirmDialog from 'src/components/ConfirmDialog.vue';
import AgreementCheckbox from './AgreementCheckbox.vue';

const { t } = useI18n();
const router = useRouter();
const themeStore = useThemeStore();
const { data: exchangeRates, loading: loadingExchangeRates } =
  useExchangeRates();
const { run: create, loading } = useCreateBuy({
  onSuccess: (res) => {
    if (res) {
      router.replace({
        name: 'trade',
        query: { type: 'buy', action: 'trading', id: res.id },
      });
    }
  },
});

const defaultCurrencyRate = computed(() =>
  exchangeRates.value?.find(
    (exchangeRate) => exchangeRate.fiatCode === DEFAULT_CURRENCY
  )
);

const form = reactive({
  usdtAmount: '0',
  price: '0',
  bankAccountName: '',
  agreement: false,
});
const agreementRef = ref();
const showConfirmDialog = ref<boolean>(false);

const handleCreate = () => {
  if (!agreementRef.value.validate()) return;

  showConfirmDialog.value = true;
};
const handleConfirmCreate = () => {
  create({
    usdtAmount: numberTool(form.usdtAmount),
    bankAccountName: form.bankAccountName,
  });
};
</script>

<style scoped>
.title {
  font-size: 1.5rem;
}
.label {
  font-size: 1rem;
}
.rate-wrapper {
  margin: -1.5rem 0 -0.5rem;
}
.order-info-value {
  font-size: 1rem;
}
</style>
