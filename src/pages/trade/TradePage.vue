<template>
  <q-page
    padding
    class="q-py-lg flex items-center column"
    :class="routeQuery.action === 'create' ? 'container' : ''"
  >
    <breadcrumb-component
      :label="t('tradePage.title')"
      class="self-start q-mb-lg"
    />

    <div v-if="routeQuery.action === 'create'" class="full-width">
      <error-not-found v-if="auth.isAgent" />
      <q-card :class="['full-width', q.screen.lt.sm ? 'q-pa-xs' : 'q-pa-lg']">
        <q-stepper
          v-model="step"
          alternative-labels
          :contracted="q.screen.lt.sm"
          header-class="no-border no-wrap"
          done-color="accent"
          active-color="accent"
        >
          <q-step
            :name="1"
            prefix="1"
            :title="t('tradePage.create')"
            color="accent"
            :done="step > 1"
          >
            <buy-form v-if="routeQuery.type === 'buy'" />
            <sell-form v-else-if="routeQuery.type === 'sell'" />
          </q-step>
          <q-step
            :name="2"
            prefix="2"
            :title="t('tradePage.pay')"
            :done="step > 2"
          ></q-step>
          <q-step
            :name="3"
            prefix="3"
            :title="t('tradePage.confirm')"
            :done="step > 3"
          ></q-step>
          <q-step
            :name="4"
            prefix="4"
            :title="t('tradePage.complete')"
            :done="step > 4"
          ></q-step>
        </q-stepper>
      </q-card>
    </div>
    <div v-else-if="routeQuery.action === 'trading'" class="full-width">
      <trading-layout :route-query="routeQuery" />
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { useQuasar } from 'quasar';
import { useAuthStore } from 'src/stores';
import ErrorNotFound from 'src/pages/ErrorNotFound.vue';
import BreadcrumbComponent from 'src/components/BreadcrumbComponent.vue';
import BuyForm from './CreateComponents/BuyForm.vue';
import SellForm from './CreateComponents/SellForm.vue';
import TradingLayout from './TradingComponents/TradingLayout.vue';

export type RouteQueryOptions = {
  action: 'create' | 'trading';
  type: 'buy' | 'sell';
  id: string;
};

const route = useRoute();
const q = useQuasar();
const { t } = useI18n();
const auth = useAuthStore();

const step = ref(1);

const routeQuery = computed(() => route.query as RouteQueryOptions);
</script>
