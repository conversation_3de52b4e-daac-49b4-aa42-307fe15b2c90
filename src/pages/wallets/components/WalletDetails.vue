<template>
  <q-card class="q-mb-md q-pa-lg column flex gap-y-md">
    <div
      v-for="(balance, index) in wallet.balances"
      :key="index"
      class="flex items-center justify-between"
    >
      <div class="flex items-center gap-x-sm">
        <q-img
          :src="`src/assets/images/${balance.symbol.toLowerCase()}.png`"
          class="icon"
        />
        <span class="symbol-label text-bold">{{
          balance.symbol.toUpperCase()
        }}</span>
      </div>
      <div class="column items-end gap-y-sm">
        <div class="flex items-center gap-x-xs">
          <span>{{ t('walletDetails.amount') }}:</span>
          <span class="text-bold text-green-6">{{
            thousandTool(balance.amount, DEFAULT_CRYPTO)
          }}</span>
        </div>
        <div class="flex items-center gap-x-xs">
          <span>{{ t('walletDetails.frozenAmount') }}:</span>
          <span class="text-bold text-negative">{{
            thousandTool(balance.frozenAmount, DEFAULT_CRYPTO)
          }}</span>
        </div>
        <div class="flex items-center gap-x-xs">
          <span>{{ t('walletDetails.totalAmount') }}:</span>
          <span class="text-bold text-accent">{{
            thousandTool(balance.totalAmount, DEFAULT_CRYPTO)
          }}</span>
        </div>
      </div>
    </div>
  </q-card>

  <q-separator />

  <div
    :class="[
      'warning flex no-wrap items-start q-pa-sm q-mt-md',
      themeStore.isDark ? 'bg-deep-orange-10' : 'bg-orange-1',
    ]"
  >
    <div style="color: orange; font-size: 24px" class="material-icons">
      warning
    </div>
    <div class="q-ml-sm">
      <div class="warning-title text-bold text-orange">
        {{ t('walletDetails.warningTitle') }}
      </div>
      <div>
        {{ t('walletDetails.warningMessage1') }}
        <br />
        {{ t('walletDetails.warningMessage2') }}
      </div>
    </div>
  </div>

  <div class="column flex items-center q-my-md gap-y-md">
    <div class="title text-bold">
      {{ walletName }} {{ t('walletDetails.title') }}
    </div>

    <q-img :src="qrCode" class="qr-code" />

    <div
      :class="[
        'text-caption q-pa-sm flex justify-between items-center rounded-borders overflow-auto hide-scrollbar',
        themeStore.isDark ? 'bg-grey-9' : 'bg-grey-2',
      ]"
    >
      {{ wallet.walletAddress }}
      <copy-button :value="wallet.walletAddress" />
    </div>
  </div>

  <q-separator />

  <div class="column q-mt-md gap-y-sm">
    <q-btn
      :to="{ name: 'home' }"
      color="accent"
      :label="t('walletDetails.homePage')"
    />
    <q-btn
      :to="{ name: 'history', query: { tab: 'deposit' } }"
      outline
      color="accent"
      :label="t('walletDetails.viewRecords')"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import QRCode from 'qrcode';
import { useThemeStore } from 'src/stores';
import { thousandTool } from 'src/utils/NumberTool';
import { walletNetworkOptions } from 'src/utils/options';
import CopyButton from 'src/components/CopyButton.vue';
import { Wallet } from '../api/useWallets';
import { DEFAULT_CRYPTO } from 'src/utils/constants';
const { wallet } = defineProps<{
  wallet: Wallet;
}>();

const { t } = useI18n();
const themeStore = useThemeStore();

const qrCode = ref('');

const walletName = computed(() =>
  t(
    'options.' +
      walletNetworkOptions.find((option) => option.value === wallet.network)
        ?.label || 'undefined'
  )
);

watch(
  () => wallet.walletAddress,
  async (value) => {
    if (value) {
      qrCode.value = await QRCode.toDataURL(value);
    }
  },
  {
    immediate: true,
  }
);
</script>

<style scoped>
.warning {
  border-radius: 4px;
}
.warning-title {
  font-size: 1rem;
}
.title {
  font-size: 1.25rem;
}
.symbol-label {
  font-size: 1rem;
}
.icon {
  width: 1.5rem;
  border-radius: 50%;
}
.qr-code {
  width: 16rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
}
</style>
