<template>
  <q-page padding class="container q-py-lg flex items-center column">
    <breadcrumb-component
      :label="t('walletsPage.title')"
      class="self-start q-mb-md"
    />

    <q-card class="full-width">
      <q-tabs
        v-model="tabs"
        align="justify"
        active-color="accent"
        indicator-color="accent"
      >
        <q-tab name="trc20">{{ t('options.trc20WalletNetworkEnum') }}</q-tab>
        <q-tab name="erc20">{{ t('options.erc20WalletNetworkEnum') }}</q-tab>
      </q-tabs>

      <q-separator />

      <q-tab-panels v-model="tabs" animated>
        <q-tab-panel name="trc20">
          <wallet-details v-if="trc20Wallet" :wallet="trc20Wallet" />
          <div v-else class="text-bold text-grey text-center">
            {{ t('walletsPage.walletUnavailable') }}
          </div>
        </q-tab-panel>

        <q-tab-panel name="erc20">
          <wallet-details v-if="erc20Wallet" :wallet="erc20Wallet" />
          <div v-else class="text-bold text-grey text-center">
            {{ t('walletsPage.walletUnavailable') }}
          </div>
        </q-tab-panel>
      </q-tab-panels>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { computed, ref, Ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { WalletNetworkEnum } from 'src/utils/enums';
import BreadcrumbComponent from 'src/components/BreadcrumbComponent.vue';
import WalletDetails from './components/WalletDetails.vue';
import { useWalletsStore } from 'src/stores';

const tabs: Ref<'trc20' | 'erc20'> = ref('trc20');

const { t } = useI18n();
const walletsStore = useWalletsStore();

const trc20Wallet = computed(() =>
  walletsStore.wallets.find((item) => item.network === WalletNetworkEnum.TRC20)
);
const erc20Wallet = computed(() =>
  walletsStore.wallets.find((item) => item.network === WalletNetworkEnum.ERC20)
);
</script>
