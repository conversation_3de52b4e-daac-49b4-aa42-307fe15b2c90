import { axiosProvider } from 'src/utils/axiosProvider';
import { WalletNetworkEnum } from 'src/utils/enums';
import { requestProvider } from 'src/utils/requestProvider';

interface Balance {
  symbol: string;
  amount: number;
  frozenAmount: number;
  totalAmount: number;
}
interface Wallet {
  network: WalletNetworkEnum;
  networkDesc: string;
  walletAddress: string;
  balances: Array<Balance>;
}

type WalletsRes = Array<Wallet>;

const useWallets = () => {
  const vueRequest = requestProvider<WalletsRes>(
    () => {
      const request = axiosProvider
        .get('/user/balance')
        .then(({ data }) => data);

      return request;
    },
    {
      manual: false,
    },
    {
      noFeedback: true,
      noErrorNotify: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export { useWallets };
export type { Wallet };
