<template>
  <q-card
    :class="[
      'wrapper full-width',
      q.screen.lt.sm ? 'q-px-md q-py-lg' : 'q-pa-lg',
    ]"
  >
    <section class="column">
      <span
        :class="[q.screen.lt.sm ? 'text-h6' : 'text-h5', 'text-bold q-mb-sm']"
        >{{ t('recentTransactionsSection.title') }}</span
      >

      <LoadingComponent v-if="loadingHistory" />
      <EmptyMaster
        v-else-if="!historyList || historyList?.items.length === 0"
        :message="t('recentTransactionsSection.emptyHistory')"
        icon="data_object"
      />
      <HistoryList v-else :historyList="historyList.items" />

      <q-btn
        outline
        rounded
        color="accent"
        icon-right="chevron_right"
        class="self-center q-mt-md q-py-sm"
        :to="{ name: 'history' }"
        :label="t('recentTransactionsSection.more')"
      />
    </section>
  </q-card>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { useHistory } from 'src/pages/history/api';
import EmptyMaster from 'src/components/EmptyMaster.vue';
import HistoryList from 'src/components/HistoryList.vue';
import LoadingComponent from 'src/components/LoadingComponent.vue';

const q = useQuasar();
const { t } = useI18n();
const {
  data: historyList,
  loading: loadingHistory,
  run: getHistoryList,
} = useHistory();

onMounted(() => {
  getHistoryList({
    pageNumber: 1,
    pageSize: 5,
  });
});
</script>

<style scoped>
.wrapper {
  border-radius: 24px;
}
</style>
