import { requestProvider } from 'src/utils/requestProvider';
import { AccecptProps, AccecptRes } from './useAcceptBuyOrder';
import { axiosProvider } from 'src/utils/axiosProvider';

export default ({ ...useProps }: UseProps) => {
  const vueRequest = requestProvider<AccecptRes, AccecptProps>(
    (id) => {
      const request = axiosProvider
        .put(`/trade-orders/sell/${id.id}/accept`)
        .then(({ data }) => data);
      return request;
    },
    { ...useProps, manual: true },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
  return vueRequest;
};
