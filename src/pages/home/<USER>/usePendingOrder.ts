import { Trade } from 'src/pages/trade/api';
import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';
import { OrderListProps, OrderListRes } from './useOrderList';

export type OrderPending = Trade;

type OrderPendingProps = OrderListProps;
type OrderPendingRes = OrderListRes;

const useOrderPending = (useProps: OrderListProps) => {
  const vueRequest = requestProvider<OrderListRes, OrderListProps>(
    (props) => {
      const request = axiosProvider
        .get('/trade-orders/pending', { params: props })
        .then(({ data }) => data);

      return request;
    },
    {
      defaultParams: [useProps],
      manual: false,
      // refreshOnWindowFocus: true,
      // pollingInterval: 1000 * 60 * 2,
      // staleTime: 0,
    },
    {
      noFeedback: true,
      noErrorNotify: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export default useOrderPending;
export type { OrderPendingProps, OrderPendingRes };
