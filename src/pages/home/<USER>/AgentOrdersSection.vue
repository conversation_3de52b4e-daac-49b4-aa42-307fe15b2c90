<template>
  <q-card
    :class="[
      'wrapper full-width',
      isOnDrawer ? 'no-shadow' : q.screen.lt.sm ? 'q-px-sm q-py-lg' : 'q-pa-lg',
    ]"
  >
    <div class="flex items-center justify-between">
      <span
        :class="[
          q.screen.lt.sm ? 'text-h6' : 'text-h5',
          isOnDrawer || q.screen.gt.xs ? '' : 'q-pl-sm',
          'text-bold',
        ]"
        >{{ t('agentOrdersSection.title') }}</span
      >
      <!-- sound fix -->
      <div>
        <q-toggle
          :model-value="soundStore.enabled"
          @update:model-value="
            (val: boolean) => (val ? soundStore.enable() : soundStore.disable())
          "
          checked-icon="volume_up"
          unchecked-icon="volume_off"
          size="lg"
          color="accent"
        >
          <q-tooltip anchor="bottom middle" self="bottom middle">
            {{ t('agentOrdersSection.sound') }}
          </q-tooltip>
        </q-toggle>

        <q-toggle
          disable
          v-model="autoAccept"
          checked-icon="check_circle"
          unchecked-icon="remove_circle_outline"
          size="lg"
          color="accent"
        >
          <q-tooltip anchor="bottom middle" self="bottom middle">
            {{ t('agentOrdersSection.autoAccept') }}
          </q-tooltip>
        </q-toggle>
      </div>
    </div>

    <q-tabs
      v-model="tabs"
      active-color="accent"
      indicator-color="accent"
      narrow-indicator
      align="left"
      class="text-gray"
    >
      <q-tab name="real-time" :label="t('agentOrdersSection.realTimeOrders')">
        <q-badge
          v-if="orderStore.getPendingCount > 0"
          color="red"
          :label="orderStore.getPendingCount"
          floating
        />
      </q-tab>
      <q-tab
        name="in-progress"
        :label="t('agentOrdersSection.inProgressOrders')"
      >
        <q-badge
          v-if="orderStore.getInProgressCount > 0"
          color="red"
          :label="orderStore.getInProgressCount"
          floating
        />
      </q-tab>
    </q-tabs>
    <q-separator />

    <q-tab-panels
      v-model="tabs"
      animated
      class="q-pt-sm"
      style="border-radius: 0"
    >
      <q-tab-panel name="real-time" class="q-pa-none">
        <LoadingComponent v-if="pendingLoad" />
        <EmptyMaster
          v-else-if="!pending || pending?.items.length === 0"
          :message="t('agentOrdersSection.empty')"
          icon="data_object"
        />
        <div v-else>
          <PaginationComponent
            v-model="currentPage"
            :page-size="pageSize"
            :total-pages="pending?.totalPages || 1"
            :total-count="pending?.totalCount || 0"
            @update:modelValue="setPage"
            @update:pageSize="setPageSize"
          />
          <RealTimePending :pending="pendingData" />
        </div>
      </q-tab-panel>
      <q-tab-panel name="in-progress" class="q-pa-none">
        <LoadingComponent v-if="inProgressLoad" />
        <EmptyMaster
          v-else-if="!inProgress || inProgress?.items.length === 0"
          :message="t('agentOrdersSection.empty')"
          icon="data_object"
        />
        <div v-else>
          <PaginationComponent
            v-model="currentPage"
            :page-size="pageSize"
            :total-pages="inProgress.totalPages"
            :total-count="inProgress.totalCount"
            @update:modelValue="setPage"
            @update:pageSize="setPageSize"
          />
          <InProgressOrders :inProgress="inProgressData" />
        </div>
      </q-tab-panel>
    </q-tab-panels>
  </q-card>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import { useOrderStore } from 'src/stores';
import { useSoundStore } from 'src/stores/sound';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import api from '../api';
import InProgressOrders from './InProgressOrders.vue';
import RealTimePending from './RealTimePending.vue';
import LoadingComponent from 'src/components/LoadingComponent.vue';
import EmptyMaster from 'src/components/EmptyMaster.vue';
import usePagination from 'src/hooks/usePagination';
import PaginationComponent from 'src/components/PaginationComponent.vue';

const soundStore = useSoundStore();
const q = useQuasar();
const { t } = useI18n();
const tabs = ref<'real-time' | 'in-progress'>('real-time');

const { isOnDrawer = false } = defineProps<{ isOnDrawer?: boolean }>();
const autoAccept = ref(false);
const orderStore = useOrderStore();
const { currentPage, pageSize, setPage, setPageSize } = usePagination();

// vue request
// pending Order
const {
  data: pending,
  loading: pendingLoad,
  run: getPending,
  refresh: rePending,
} = api.usePendingOrder({
  pageNumber: currentPage.value,
  pageSize: pageSize.value,
});

// inProgress Order
const {
  data: inProgress,
  loading: inProgressLoad,
  run: getInprogress,
  refresh: reInProgress,
} = api.useOrderList({
  pageNumber: currentPage.value,
  pageSize: pageSize.value,
  StatusGroup: 'Ongoing',
});

// watch store to update data
watch(
  () => [
    orderStore.pendingOrders,
    orderStore.inProgressOrders,
    orderStore.acceptedOrder,
    orderStore.doneOrder,
    orderStore.cancelledOrder,
    orderStore.expiredOrder,
  ],
  () => {
    rePending();
    reInProgress();
  },
  { deep: true }
);

// watch about page to update pagesize
watch([currentPage, pageSize], () => {
  const params = {
    pageNumber: currentPage.value,
    pageSize: pageSize.value,
  };
  getPending(params);
  getInprogress({
    ...params,
    StatusGroup: 'Ongoing',
  });
});

// following data to update UIs
const pendingData = computed(
  () =>
    pending.value || {
      items: [],
      totalCount: 0,
      totalPages: 1,
      currentPage: 1,
      pageSize: pageSize.value,
    }
);
const inProgressData = computed(() => {
  return (
    inProgress.value || {
      items: [],
      totalCount: 0,
      totalPages: 1,
      currentPage: 1,
      pageSize: pageSize.value,
    }
  );
});

watch(pendingData, (newVal) => {
  orderStore.setPendingCount(newVal.totalCount || 0);
});

watch(inProgressData, (newVal) => {
  orderStore.setInProgressCount(newVal.totalCount || 0);
});
</script>

<style scoped>
.wrapper {
  border-radius: 24px;
}
</style>
