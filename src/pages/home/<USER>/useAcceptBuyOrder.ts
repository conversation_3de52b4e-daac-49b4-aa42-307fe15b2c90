import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

export type AccecptProps = {
  id: number;
};

export type AccecptRes = unknown;

export default ({ ...useProps }: UseProps) => {
  const vueRequest = requestProvider<AccecptRes, AccecptProps>(
    (id) => {
      const request = axiosProvider
        .put(`/trade-orders/buy/${id.id}/accept`)
        .then(({ data }) => data);
      return request;
    },
    { ...useProps, manual: true },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
  return vueRequest;
};
