<template>
  <q-item
    clickable
    @click="handleGoToTrade"
    class="item-wrapper q-py-md column gap-y-xs"
  >
    <!-- Header -->
    <section class="flex items-start justify-between no-wrap">
      <!-- Type -->
      <div
        :class="[
          'flex items-center q-gutter-x-sm',
          isBuy ? 'text-info' : 'text-negative',
        ]"
      >
        <span class="text-h5 text-bold">{{
          isBuy ? t('realTimeWaitingOrder.buy') : t('realTimeWaitingOrder.sell')
        }}</span>
        <span
          class="text-white q-px-sm rounded-borders text-caption"
          :class="isBuy ? 'bg-info' : 'bg-negative'"
          >{{ order.cryptoSymbol }}/{{ order.fiatCurrency }}</span
        >
        <span v-if="devEnv" class="exchange-rate q-px-xs">{{ order.id }}</span>
      </div>

      <!-- Amount -->
      <div class="flex q-gutter-x-md justify-end no-wrap">
        <div class="column items-end text-bold">
          <span class="amount">{{
            thousandTool(order.fiatAmount, order.fiatCurrency as DigitTypes)
          }}</span>
          <span class="currency">{{ order.fiatCurrency }}</span>
        </div>

        <div
          :class="[
            'column items-end text-bold',
            isBuy ? 'text-info' : 'text-negative',
          ]"
        >
          <span class="amount">{{
            thousandTool(order.cryptoAmount, order.cryptoSymbol as DigitTypes)
          }}</span>
          <span class="currency">{{ order.cryptoSymbol }}</span>
        </div>
      </div>
    </section>

    <!-- Body -->
    <section class="flex justify-between">
      <!-- Left -->
      <div>
        <div class="flex gap-x-xs text-grey">
          <span>{{ t('realTimeWaitingOrder.orderNumber') }}:</span>
          <span class="text-bold text-grey">{{ order.tradeNo }}</span>
        </div>
        <div class="flex gap-x-xs text-grey">
          <span>{{ t('realTimeWaitingOrder.createdAt') }}:</span>
          <span class="text-bold">{{
            dayjs(order.createdAt).format(dateFormator.accurate)
          }}</span>
        </div>
      </div>

      <!-- Right -->
      <div class="column" :class="q.screen.lt.sm ? '' : 'items-end'">
        <div class="flex gap-x-xs text-grey">
          <span>{{ t('realTimeWaitingOrder.exchangeRate') }}:</span>
          <span class="text-bold text-grey"> {{ order.rate }}</span>
        </div>
        <div class="flex gap-x-xs text-grey">
          <span
            >{{
              isBuy
                ? t('realTimeWaitingOrder.seller')
                : t('realTimeWaitingOrder.buyer')
            }}:</span
          >
          <span v-if="isBuy" class="text-bold text-grey">{{
            order.accountName || '--'
          }}</span>
          <span v-else class="text-bold text-grey">{{
            order.payerBankAccountName || '--'
          }}</span>
        </div>
        <!-- Unread Message -->
        <div
          v-if="isTrading"
          class="unread-message flex gap-x-xs bg-accent q-px-sm rounded-borders text-white"
        >
          <span>{{ t('inProgressItem.unreadMessage') }}:</span>
          <span class="text-bold">{{ unreadMessage || 0 }}</span>
          <q-badge v-if="unreadMessage" floating rounded color="negative" />
        </div>
      </div>
    </section>

    <!-- Footer -->
    <section class="flex justify-between">
      <!-- Pending Timer -->
      <div v-if="isPending" class="flex gap-x-xs text-orange text-bold">
        <q-icon name="info" size="1.2rem" />
        <span>{{ t('realTimeWaitingOrder.expiresIn') }}:</span>
        <countdown-timer
          :startAt="order.updatedAt"
          :duration="PENDING_TIME_LIMIT"
        />
      </div>

      <!-- Dispute Warning -->
      <div v-if="isDisputed" class="flex gap-x-xs text-negative text-bold">
        <q-icon name="warning" size="1.2rem" />
        <span>{{ t('realTimeWaitingOrder.disputeWarning') }}</span>
      </div>
      <!-- Payment Timer -->
      <div v-else-if="isPaying" class="flex gap-x-xs text-orange text-bold">
        <q-icon :name="isExpired ? 'warning' : 'info'" size="1.2rem" />
        <span v-if="isExpired">
          {{
            isBuy
              ? t('realTimeWaitingOrder.payWarning')
              : t('realTimeWaitingOrder.sellWarning')
          }}
        </span>
        <template v-else>
          <span>{{ t('realTimeWaitingOrder.remainingTime') }}:</span>
          <countdown-timer
            :startAt="order.updatedAt"
            :duration="PAYMENT_TIME_LIMIT"
            @countdown-finished="handleCountdownEnd"
          />
        </template>
      </div>

      <!-- Status -->
      <TradeStatusLabel
        :status="order.status"
        :class="q.screen.lt.sm ? '' : 'q-ml-auto'"
      />
    </section>
  </q-item>
</template>

<script setup lang="ts">
import { computed, watch, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import dayjs from 'dayjs';
import { PENDING_TIME_LIMIT, PAYMENT_TIME_LIMIT } from 'src/utils/constants';
import {
  TradeTypeEnum,
  TradeStatusEnum,
  TradeDisputeStatusEnum,
} from 'src/utils/enums';
import { devEnv } from 'src/router/routes';
import { useChatStore } from 'src/stores';
import useSwapTradeType from 'src/hooks/useSwapTradeType';
import { DigitTypes, thousandTool } from 'src/utils/NumberTool';
import { dateFormator } from 'src/utils/dateTool';
import { OrderPending } from '../api/usePendingOrder';
import TradeStatusLabel from 'src/components/TradeStatusLabel.vue';
import CountdownTimer from 'src/components/CountdownTimer.vue';

const router = useRouter();
const q = useQuasar();
const { order } = defineProps<{
  order: OrderPending;
}>();

const { t } = useI18n();
const chatStore = useChatStore();

const unreadMessage = ref<number>(0);
const isExpired = ref<boolean>(false);

const isBuy = computed(
  () => useSwapTradeType(order.type) === TradeTypeEnum.Buy
);
const isPending = computed(() => order.status === TradeStatusEnum.Pending);
const isPaying = computed(
  () => order.status === TradeStatusEnum.BankAccountAssigned
);
const isTrading = computed(() =>
  [TradeStatusEnum.BankAccountAssigned, TradeStatusEnum.PaymentMade].includes(
    order.status
  )
);
const isDisputed = computed(
  () => order.disputeStatus === TradeDisputeStatusEnum.InDispute
);

const handleGoToTrade = () => {
  router.push({
    name: 'trade',
    query: {
      id: order.id,
      action: 'trading',
      type: isBuy.value ? 'buy' : 'sell',
    },
  });

  chatStore.readMessage(order.id);
};
const handleCountdownEnd = () => {
  isExpired.value = true;
};

watch(
  () => chatStore.unreadMessageCount[order.id],
  (newUnreadMessageCount) => {
    unreadMessage.value = newUnreadMessageCount;
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>

<style scoped>
.item-wrapper {
  border-radius: 6px;
  border: 1px solid var(--border-color);
  margin-top: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.exchange-rate {
  border: 1px solid currentColor;
  font-size: 0.75rem;
}

.amount {
  font-size: 1rem;
}

.currency {
  font-size: 0.75rem;
  margin-top: -0.25rem;
}

.status-tag {
  border-radius: 2px;
}

.unread-message {
  position: relative;
  width: fit-content;
}
</style>
