<template>
  <q-item
    clickable
    @click="handleGoTrading"
    class="wrapper q-py-md column gap-y-xs"
  >
    <section class="flex items-start justify-between no-wrap">
      <div
        :class="[
          'flex items-center gap-x-sm',
          isBuy ? 'text-info' : 'text-negative',
        ]"
      >
        <span class="text-h5 text-bold">{{
          isBuy ? t('inProgressItem.buy') : t('inProgressItem.sell')
        }}</span>
        <span
          class="text-white q-px-sm rounded-borders text-caption"
          :class="isBuy ? 'bg-info' : 'bg-negative'"
          >{{ order.cryptoSymbol }}/{{ order.fiatCurrency }}</span
        >
        <span v-if="devEnv">{{ order.id }}</span>
      </div>

      <div class="flex q-gutter-x-md">
        <div class="column items-end text-bold">
          <span class="amount">{{
            thousandTool(order.fiatAmount, order.fiatCurrency as DigitTypes)
          }}</span>
          <span class="currency">CNY</span>
        </div>
        <div
          :class="[
            'column items-end text-bold',
            order.type === 1 ? 'text-info' : 'text-negative',
          ]"
        >
          <span class="amount">{{
            thousandTool(order.cryptoAmount, order.cryptoSymbol as DigitTypes)
          }}</span>
          <span class="currency">{{ order.cryptoSymbol }}</span>
        </div>
      </div>
    </section>
    <section class="flex justify-between gap-x-lg">
      <div>
        <div class="flex gap-x-xs text-grey">
          <span>{{ t('inProgressItem.orderNumber') }}:</span>
          <span class="text-bold text-grey">{{ order.tradeNo }}</span>
        </div>
        <div class="flex justify-start gap-x-xs text-grey">
          <span>{{ t('inProgressItem.createdAt') }}:</span>
          <span class="text-bold">{{
            dayjs(order.createdAt).format(dateFormator.accurate)
          }}</span>
        </div>
      </div>

      <div class="column" :class="q.screen.lt.sm ? '' : 'items-end'">
        <div class="flex gap-x-xs text-grey">
          <span>{{ t('inProgressItem.exchangeRate') }}:</span>
          <span class="text-bold text-grey"> {{ order.rate }}</span>
        </div>
        <div class="flex gap-x-xs text-grey">
          <span
            >{{
              isBuy ? t('inProgressItem.seller') : t('inProgressItem.buyer')
            }}:</span
          >
          <span v-if="isBuy" class="text-bold text-grey">{{
            order.accountName || '--'
          }}</span>
          <span v-else class="text-bold text-grey">{{
            order.payerBankAccountName || '--'
          }}</span>
        </div>
        <div
          v-if="isTrading"
          class="unread-message flex gap-x-xs bg-accent q-px-sm rounded-borders text-white"
        >
          <span>{{ t('inProgressItem.unreadMessage') }}:</span>
          <span class="text-bold">{{ unreadMessage || 0 }}</span>
          <q-badge v-if="unreadMessage" floating rounded color="negative" />
        </div>
      </div>
    </section>

    <!-- Footer -->
    <section class="flex justify-between">
      <!-- Pending Timer -->
      <div v-if="isPending" class="flex gap-x-xs text-orange text-bold">
        <q-icon name="info" size="1.2rem" />
        <span>{{ t('inProgressItem.expiresIn') }}:</span>
        <countdown-timer
          :startAt="order.updatedAt"
          :duration="PENDING_TIME_LIMIT"
        />
      </div>

      <!-- Dispute Warning -->
      <div v-if="isDisputed" class="flex gap-x-xs text-negative text-bold">
        <q-icon name="warning" size="1.2rem" />
        <span>{{ t('inProgressItem.disputeWarning') }}</span>
      </div>
      <!-- Payment Timer -->
      <div v-else-if="isPaying" class="flex gap-x-xs text-orange text-bold">
        <q-icon :name="isExpired ? 'warning' : 'info'" size="1.2rem" />
        <span v-if="isExpired">
          {{
            isBuy
              ? t('inProgressItem.payWarning')
              : t('inProgressItem.sellWarning')
          }}
        </span>
        <template v-else>
          <span>{{ t('inProgressItem.remainingTime') }}:</span>
          <countdown-timer
            :startAt="order.updatedAt"
            :duration="PAYMENT_TIME_LIMIT"
            @countdown-finished="handleCountdownEnd"
          />
        </template>
      </div>

      <!-- Status -->
      <trade-status-label
        :status="order.status"
        :class="q.screen.gt.sm && !isAccepted ? 'q-ml-auto' : ''"
      />

      <div
        v-if="isAccepted"
        class="flex items-center justify-center gap-x-xs text-bold text-info"
      >
        <q-icon name="account_balance" size="1.25rem" />
        <span class="text-center">{{ t('inProgressItem.setBank') }}</span>
      </div>
    </section>
  </q-item>

  <SelectAccountModal
    v-model="openSetBank"
    @on-select-account="
      (account: BankAccount) =>
        order.type === 0
          ? handleSetBankBuy(account, order)
          : handleSetBankSell(account, order)
    "
  />
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import { BankAccount } from 'src/pages/accounts/api';
import SelectAccountModal from 'src/pages/trade/CreateComponents/SelectAccountModal.vue';
import { devEnv } from 'src/router/routes';
import {
  TradeTypeEnum,
  TradeStatusEnum,
  TradeDisputeStatusEnum,
} from 'src/utils/enums';
import { PAYMENT_TIME_LIMIT } from 'src/utils/constants';
import { PENDING_TIME_LIMIT } from 'src/utils/constants';
import useSwapTradeType from 'src/hooks/useSwapTradeType';
import { dateFormator } from 'src/utils/dateTool';
import { DigitTypes, thousandTool } from 'src/utils/NumberTool';
import { computed, provide, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import api from '../api';
import { OrderPending } from '../api/usePendingOrder';
import TradeStatusLabel from 'src/components/TradeStatusLabel.vue';
import { useChatStore } from 'src/stores';
import { useQuasar } from 'quasar';
import CountdownTimer from 'src/components/CountdownTimer.vue';

const { order } = defineProps<{
  order: OrderPending;
}>();

const q = useQuasar();
const { t } = useI18n();
const router = useRouter();
const chatStore = useChatStore();

const isExpired = ref<boolean>(false);
const unreadMessage = ref<number>(0);
const openSetBank = ref<boolean>(false);
const currentAccountId = ref<number | null>(null);
provide('currentAccountId', currentAccountId);

// vue request
const { run: setBankBuy } = api.useSetBankBuy({});
const { run: setBankSell } = api.useSetBankSell({});

const isBuy = computed(
  () => useSwapTradeType(order.type) === TradeTypeEnum.Buy
);
const isPending = computed(() => order.status === TradeStatusEnum.Pending);
const isAccepted = computed(() => order.status === TradeStatusEnum.Accepted);
const isPaying = computed(
  () => order.status === TradeStatusEnum.BankAccountAssigned
);
const isTrading = computed(() =>
  [TradeStatusEnum.BankAccountAssigned, TradeStatusEnum.PaymentMade].includes(
    order.status
  )
);
const isDisputed = computed(
  () => order.disputeStatus === TradeDisputeStatusEnum.InDispute
);

const handleSetBankBuy = (account: BankAccount, order: OrderPending) => {
  setBankBuy({
    orderId: order.id,
    bankId: account.id,
  });
  openSetBank.value = false;
};

const handleSetBankSell = (account: BankAccount, order: OrderPending) => {
  setBankSell({
    orderId: order.id,
    bankId: account.id,
  });
  openSetBank.value = false;
};

const handleGoTrading = () => {
  if (order.statusDesc === 'Accepted') return (openSetBank.value = true);

  router.push({
    name: 'trade',
    query: {
      id: order.id,
      action: 'trading',
      type: order?.typeDesc === 'Buy' ? 'sell' : 'buy',
    },
  });
  chatStore.readMessage(order.id);
};
const handleCountdownEnd = () => {
  isExpired.value = true;
};

watch(
  () => chatStore.unreadMessageCount[order.id],
  (newUnreadMessageCount) => {
    unreadMessage.value = newUnreadMessageCount;
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>

<style scoped>
.wrapper {
  border-radius: 6px;
  border: 1px solid var(--border-color);
  margin-top: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.amount {
  font-size: 1rem;
}

.currency {
  font-size: 0.75rem;
  margin-top: -0.25rem;
}

.unread-message {
  position: relative;
  width: fit-content;
}
</style>
