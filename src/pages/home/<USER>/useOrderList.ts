import { Trade } from 'src/pages/trade/api';
import { axiosProvider } from 'src/utils/axiosProvider';
import { TradeModeEnum, TradeStatusEnum, TradeTypeEnum } from 'src/utils/enums';
import { requestProvider } from 'src/utils/requestProvider';

type OrderListProps = {
  pageNumber?: number | null;
  pageSize?: number | null;
  orderBy?: 'Id' | null;
  StartTime?: string;
  EndTime?: string;
  orderByDescending?: boolean | null;
  StatusGroup?: 'Ongoing' | 'Completed' | 'Failed';
  mode?: TradeModeEnum;
  type?: TradeTypeEnum;
  Statuss?: TradeStatusEnum;
};

interface OrderListRes {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalCount: number;
  items: Array<Trade>;
}

const useOrderList = (useProps: OrderListProps) => {
  const vueRequest = requestProvider<OrderListRes, OrderListProps>(
    (props) => {
      const request = axiosProvider
        .get('/trade-orders/my', { params: props })
        .then(({ data }) => data);

      return request;
    },
    {
      defaultParams: [useProps],
      manual: false,
      // refreshOnWindowFocus: true,
      // pollingInterval: 1000 * 60 * 2,
      // staleTime: 0,
    },
    {
      noFeedback: true,
      noErrorNotify: true,
      noTempData: true,
    }
  );

  return vueRequest;
};

export default useOrderList;
export type { OrderListProps, OrderListRes };
