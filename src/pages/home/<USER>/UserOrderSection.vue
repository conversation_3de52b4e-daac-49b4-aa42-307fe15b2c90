<template>
  <q-card
    :class="[
      'wrapper full-width',
      isOnDrawer ? 'no-shadow' : q.screen.lt.sm ? 'q-px-sm q-py-lg' : 'q-pa-lg',
    ]"
  >
    <span
      :class="[
        'block q-mb-md text-bold',
        q.screen.lt.sm ? 'text-h6' : 'text-h5',
        isOnDrawer || q.screen.gt.xs ? '' : 'q-pl-sm',
      ]"
    >
      {{ t('userOrdersSection.title') }}
    </span>

    <LoadingComponent v-if="allLstLoad" />
    <EmptyMaster
      v-else-if="!myAllList || myAllList.items.length === 0"
      :message="t('agentOrdersSection.empty')"
      icon="data_object"
    />
    <div v-else>
      <div>
        <PaginationComponent
          v-model="currentPage"
          :page-size="pageSize"
          :total-pages="myAllLstData.totalPages"
          :total-count="myAllLstData.totalCount"
          @update:modelValue="setPage"
          @update:pageSize="setPageSize"
        />
        <RealTimeWaitingOrder
          v-for="o in myAllLstData.items"
          :key="o.id"
          :order="o"
        />
      </div>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import EmptyMaster from 'src/components/EmptyMaster.vue';
import LoadingComponent from 'src/components/LoadingComponent.vue';
import PaginationComponent from 'src/components/PaginationComponent.vue';
import usePagination from 'src/hooks/usePagination';
import { useI18n } from 'vue-i18n';
import api from '../api';
import RealTimeWaitingOrder from './RealTimeWaitingOrder.vue';
import { useOrderStore } from 'src/stores';
import { computed, watch } from 'vue';

const orderStore = useOrderStore();
const q = useQuasar();
const { t } = useI18n();
const { isOnDrawer = false } = defineProps<{ isOnDrawer?: boolean }>(); // this flag is used to change the heading padding when it is inside the MenuDrawer on mobile devices, to match the design
const { currentPage, pageSize, setPage, setPageSize } = usePagination();
const {
  data: myAllList,
  loading: allLstLoad,
  run,
  refresh: reAllList,
} = api.useOrderList({
  pageNumber: currentPage.value,
  pageSize: pageSize.value,
  StatusGroup: 'Ongoing',
});
watch(
  () => [orderStore.userWaitingOrders],

  () => {
    reAllList();
  }
);

const myAllLstData = computed(
  () =>
    myAllList.value || {
      items: [],
      totalCount: 0,
      totalPages: 1,
      currentPage: 1,
      pageSize: pageSize.value,
    }
);

watch([currentPage, pageSize], () =>
  run({
    pageNumber: currentPage.value,
    pageSize: pageSize.value,
    StatusGroup: 'Ongoing',
  })
);

watch(myAllLstData, (newVal) => {
  orderStore.setUserOrderCount(newVal.totalCount || 0);
});
</script>

<style scoped>
.wrapper {
  border-radius: 24px;
}
</style>
