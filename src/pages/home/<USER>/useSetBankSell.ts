import { SetBankProps, SetBankRes } from './useSetBankBuy';
import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

export default ({ ...useProps }: UseProps) => {
  const vueRequest = requestProvider<SetBankRes, SetBankProps>(
    (props) => {
      const request = axiosProvider
        .put(`/trade-orders/sell/${props.orderId}/bank-account/${props.bankId}`)
        .then(({ data }) => data);
      return request;
    },
    { ...useProps, manual: true },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
  return vueRequest;
};
