<template>
  <section class="column flex-center">
    <span class="text-h6 text-white text-bold" :class="isAgent ? '' : 'q-mb-md'"
      >{{ t('assetSection.welcome') }} {{ auth.loginRes?.phoneNumber }}</span
    >
    <AssetOverview v-if="!isAgent" />

    <div
      class="btn-wrapper"
      :class="isAgent ? 'q-mt-md' : 'q-mt-xl'"
      :style="{
        'min-width': q.screen.gt.xs ? '30rem' : '20rem',
      }"
    >
      <q-btn
        :to="
          !isAgent
            ? { name: 'trade', query: { type: 'buy', action: 'create' } }
            : ''
        "
        flat
        class="btn btn--left q-py-md"
        :class="themeStore.isDark ? 'bg-dark' : 'bg-white'"
        :style="{ 'pointer-events': auth.isAgent ? 'none' : 'auto' }"
      >
        <div class="flex-center column text-info">
          <LoadingComponent
            v-if="loadingExchangeRates"
            type="text"
            style="width: 3rem; height: 2rem"
            class="loadingBuy-rate"
          />
          <span v-else class="text-h5 text-bold">{{
            thousandTool(
              auth.isAgent
                ? defaultCurrencyRate?.bidPrice
                : defaultCurrencyRate?.askPrice,
              DEFAULT_CURRENCY
            )
          }}</span>
          <span>{{ t('assetSection.buy') }}</span>
        </div>
      </q-btn>
      <q-btn
        :to="
          !isAgent
            ? { name: 'trade', query: { type: 'sell', action: 'create' } }
            : ''
        "
        flat
        class="btn btn--right q-py-md"
        :class="themeStore.isDark ? 'bg-dark' : 'bg-white'"
        :style="{ 'pointer-events': auth.isAgent ? 'none' : 'auto' }"
      >
        <div class="flex-center column text-negative">
          <LoadingComponent
            v-if="loadingExchangeRates"
            type="rect"
            style="width: 3rem; height: 2rem"
            class="loadingSell-rate"
          />
          <span v-else class="text-h5 text-bold">{{
            thousandTool(
              auth.isAgent
                ? defaultCurrencyRate?.askPrice
                : defaultCurrencyRate?.bidPrice,
              DEFAULT_CURRENCY
            )
          }}</span>
          <span>{{ t('assetSection.sell') }}</span>
        </div>
      </q-btn>
    </div>
  </section>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';
import { useExchangeRates } from 'src/api';
import { useAuthStore, useThemeStore } from 'src/stores';
import { thousandTool } from 'src/utils/NumberTool';
import { DEFAULT_CURRENCY } from 'src/utils/constants';
import AssetOverview from 'src/components/AssetOverview.vue';
import LoadingComponent from 'src/components/LoadingComponent.vue';

const q = useQuasar();
const { t } = useI18n();
const auth = useAuthStore();
const themeStore = useThemeStore();

const isAgent = auth.isAgent;
const { data: exchangeRates, loading: loadingExchangeRates } =
  useExchangeRates();

const defaultCurrencyRate = computed(() =>
  exchangeRates.value?.find(
    (exchangeRate) => exchangeRate.fiatCode === DEFAULT_CURRENCY
  )
);
</script>

<style scoped>
.btn-wrapper {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.btn {
  box-shadow: inset 0 -3px 2px 1px rgba(41, 6, 61, 0.1);
  border-radius: 0;
}
.btn--left {
  border-top-left-radius: 56px;
  border-bottom-left-radius: 56px;
}
.btn--right {
  border-top-right-radius: 56px;
  border-bottom-right-radius: 56px;
}

.loadingSell-rate {
  color: red !important;
}
.loadingBuy-rate {
  color: blue !important;
}
</style>
