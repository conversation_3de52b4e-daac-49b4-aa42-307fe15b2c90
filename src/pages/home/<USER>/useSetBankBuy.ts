import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

export type SetBankProps = {
  orderId: number;
  bankId: number;
};

export type SetBankRes = unknown;

export default ({ ...useProps }: UseProps) => {
  const vueRequest = requestProvider<SetBankRes, SetBankProps>(
    (props) => {
      const request = axiosProvider
        .put(`/trade-orders/buy/${props.orderId}/bank-account/${props.bankId}`)
        .then(({ data }) => data);
      return request;
    },
    { ...useProps, manual: true },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
  return vueRequest;
};
