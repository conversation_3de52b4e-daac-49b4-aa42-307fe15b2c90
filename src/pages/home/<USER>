<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="bg"></div>
  <q-page
    padding
    class="container q-py-lg flex items-center column q-gutter-y-xl"
  >
    <asset-section />
    <agent-orders-section v-if="auth.isAgent" />
    <UserOrderSection v-else />
    <recent-transactions-section />
  </q-page>
</template>

<script setup lang="ts">
import { useAuthStore } from 'src/stores/useAuthStore';
import AgentOrdersSection from './components/AgentOrdersSection.vue';
import AssetSection from './components/AssetSection.vue';
import RecentTransactionsSection from './components/RecentTransactionsSection.vue';
import UserOrderSection from './components/UserOrderSection.vue';

const auth = useAuthStore();
</script>
