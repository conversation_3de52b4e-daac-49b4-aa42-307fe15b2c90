<template>
  <div class="add_mail_container">
    <div></div>
    <q-form>
      <div class="addM-tit">Email</div>
      <q-input
        outlined
        autocomplete="off"
        v-model="email"
        disable
        :rules="[
          (val: string) =>
            !!(val && val.toString().trim()) || 'addMailFormStep.enterMail',
          (val: string) =>
            /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) ||
            'addMailFormStep.must_is_mail',
        ]"
        lazy-rules
        inputmode="email"
        placeholder="t('addMailFormStep.enterMail')"
        class="addM-input"
      />

      <q-btn
        class="add-vfy"
        unelevated
        type="submit"
        label="t('addMailFormStep.vfy_mail')"
      />
    </q-form>
  </div>
</template>

<script setup lang="ts">
import { devEnv } from 'src/router/routes';
import { useAuthStore } from 'src/stores';
import { ref } from 'vue';

// setup funcs
const auth = useAuthStore();
const email = ref<string | null>(devEnv ? auth.loginRes?.email ?? null : '');
</script>

<style scoped>
.add_mail_container {
  font-family: Inter;
  margin-top: 2.3rem;
}
.addM-tit {
  font-weight: 400;
  font-size: 1rem;
}

.add_mail_container .add-vfy {
  width: 100%;
  height: 53px;
  border-radius: 7px;
  background: #7d3ffa;
  transition: background-color 0.3s ease, transform 0.2s ease,
    box-shadow 0.3s ease;
  color: white;
}

.add_mail_container .add-vfy:hover {
  background-color: #5e1ed4;
  transform: scale(1.05);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.add_mail_container .back_login {
  font-weight: 400;
  font-size: 1rem;
  line-height: 1;
  letter-spacing: 0;
  text-align: center;
  margin-top: 1.5625rem;
}

.add_mail_container #link_back {
  color: #0256f0;
}
.addM-input {
  margin-bottom: 0.5rem;
  margin-top: 0.7rem;
}
.addM-input:deep(.q-field__control) {
  background-color: rgba(234, 234, 234, 1);
}
body.isDark .addM-input:deep(.q-field__control) {
  background: rgba(43, 39, 68, 1);
}

.addM-input:deep(input::placeholder) {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0%;
}
</style>
