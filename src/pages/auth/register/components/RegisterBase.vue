<template>
  <q-form autocomplete="off" @submit="handleBaseSubmit" class="q-gutter-y-sm">
    <!-- country code & phone  -->
    <div class="row">
      <div class="col-auto">
        <!-- Country Code -->
        <div class="input-label">{{ t('registerBase.country_code') }}</div>
        <q-select
          v-model="code"
          class="q-mr-xs countryCode"
          outlined
          :options="countryCodeOpts"
          :rules="[(val: number) => !!val || t('registerBase.select')]"
          :displayValue="code ? `+${code}` : t('registerBase.select')"
          option-value="value"
          option-label="label"
          emit-value
          map-options
        />
      </div>
      <!-- Phone Num  -->
      <div class="col">
        <div class="input-label">{{ t('registerBase.phone_num') }}</div>
        <q-input
          lazy-rules
          :rules="[
            (val: string) => !!val || t('registerBase.enter_phone'),
            (val: string) =>
              val.length === mask.length || t('registerBase.mask_length'),
          ]"
          v-model="phone"
          :mask="mask"
          outlined
          aria-autocomplete="none"
          :placeholder="t('registerBase.enter_phone')"
        />
      </div>
    </div>
    <!-- Email  -->
    <div>
      <div class="input-label">
        {{ t('registerBase.email') }}
      </div>
      <q-input
        class="custom-input"
        outlined
        autocomplete="off"
        v-model="email"
        :rules="[
          (val: string) =>
            !!(val && val.toString().trim()) || t('registerBase.enterMail'),
          (val: string) =>
            /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) ||
            t('registerBase.mailFormat'),
        ]"
        lazy-rules
        inputmode="email"
        :placeholder="t('registerBase.enterMail')"
      />
    </div>

    <!-- New password -->
    <div>
      <div class="input-label">
        {{ t('registerBase.pwd') }}
      </div>
      <q-input
        class="custom-input"
        outlined
        ref="inputs"
        v-model="pwdSigup"
        :type="isVisibleSetting ? 'text' : 'password'"
        :rules="passwordRules"
        lazy-rules
        autocomplete="off"
        :placeholder="t('registerBase.enter_pwd')"
      >
        <template v-slot:append>
          <q-icon
            :name="isVisibleSetting ? 'visibility' : 'visibility_off'"
            class="cursor-pointer"
            @click="() => (isVisibleSetting = !isVisibleSetting)"
          />
        </template>
      </q-input>
    </div>

    <!-- confirm password -->
    <div>
      <div class="input-label">{{ t('registerBase.confirm_pwd') }}</div>
      <q-input
        class="custom-input"
        outlined
        v-model="confirmPwd"
        :type="isVisibleSetting ? 'text' : 'password'"
        :rules="confirmPasswordRules"
        lazy-rules
        autocomplete="off"
        :placeholder="t('registerBase.enter_pwd')"
      >
        <template v-slot:append>
          <q-icon
            :name="isVisibleSetting ? 'visibility' : 'visibility_off'"
            class="cursor-pointer"
            @click="() => (isVisibleSetting = !isVisibleSetting)"
          />
        </template>
      </q-input>
    </div>
    <q-btn
      class="addBasic"
      type="submmit"
      color="primary"
      :label="t('registerBase.submit')"
      :loading="loading"
    />
  </q-form>
</template>

<script setup lang="ts">
import hooks from 'src/hooks';
import { forage } from 'src/utils/foragePkg';
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import api from '../api';

interface BaseDt {
  phone: string;
  email: string;
  code: number | undefined;
  basePwd: string;
}

const { t } = useI18n();
const code = ref<number>();
const countryCodeOpts = hooks.useCountryCodeOpts();
const mask = hooks.usePhoneMask(code);
const phone = ref('');
const email = ref('');
const pwdSigup = ref('');
const confirmPwd = ref('');
const isVisibleSetting = ref(false);
const emit = defineEmits(['addedBase']);

onMounted(async () => {
  const canEdit = await forage<BaseDt>().getItem('edit');
  if (canEdit) {
    code.value = canEdit.code;
    phone.value = canEdit.phone;
    email.value = canEdit.email;
    pwdSigup.value = canEdit.basePwd;
    confirmPwd.value = canEdit.basePwd;
  }
});
// vue-request

const { run: vfy, loading } = api.useMailVfy({
  onSuccess: async () => {
    await forage<string>().setItem('baseIF', email.value);
    await forage<BaseDt>().setItem('edit', {
      phone: phone.value,
      email: email.value,
      code: pickCode.value?.value,
      basePwd: confirmPwd.value,
    });
    emit('addedBase');
  },
});
// some rules
const passwordRules = [
  (val: string) => !!val || t('registerBase.enter_pwd'),
  (val: string) =>
    /^(?=.*[A-Z])(?=.*\d)[A-Za-z\d]{8,}$/.test(val) ||
    t('registerBase.pwd_rules'),
];

const confirmPasswordRules = [
  (val: string) => !!val || t('registerBase.enter_pwd'),
  (val: string) => val === pwdSigup.value || t('registerBase.notMatch_pw'),
];

// Computed property to find the country based on code
const pickCode = computed(() => {
  return countryCodeOpts.value?.find((item) => item.value === code.value);
});

const handleBaseSubmit = async () => {
  vfy({
    email: email.value,
    phoneRegionCode: pickCode.value?.dialCode,
    phoneNumber: phone.value,
    password: confirmPwd.value,
  });
};
</script>

<style scoped>
.countryCode {
  min-width: 160px;
}

.input-label {
  margin-bottom: 6px;
}

.addBasic {
  width: 100%;
  height: 53px;
  border-radius: 7px;
  background: #7d3ffa;
  transition: background-color 0.3s ease, transform 0.2s ease,
    box-shadow 0.3s ease;
  color: white;
  margin-top: 17px;
}
.addBasic:hover {
  background-color: #5e1ed4;
  transform: scale(1.05);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}
</style>
