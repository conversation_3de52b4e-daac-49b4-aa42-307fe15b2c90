<template>
  <q-form @submit="handleOtpSend" class="q-gutter-md code-vfy-container">
    <div class="mail_addr flex items-center">
      <div class="mail_code">{{ mailCode }}</div>
      <q-btn class="text-btn shine-btn" @click="infoEdit" flat>
        <span class="btn-label">
          <span class="text-base">{{ t('sendCodeVfy.edit') }}</span>
          <span class="text-shine">{{ t('sendCodeVfy.edit') }}</span>
        </span>
      </q-btn>
    </div>

    <div class="row q-gutter-x-md q-gutter-y-sm justify-start q-mb-md">
      <q-input
        v-for="(_, index) in otp"
        :key="index"
        ref="inputs"
        v-model="otp[index]"
        mask="X"
        maxlength="1"
        class="otp-input"
        @keyup="handleKeyup(index, $event)"
        type="password"
      />
    </div>

    <q-btn
      type="submit"
      :disable="isDisableSend"
      :label="t('sendCodeVfy.vfy_code')"
      class="send-code-btn"
      :loading="loading"
    />

    <div class="flex justify-end items-center">
      <div>{{ t('sendCodeVfy.notYet_otp') }}</div>
      <q-btn
        flat
        color="blue"
        :label="resendLabel"
        @click="handleResend"
        :disable="resendDisabled"
      />
    </div>
  </q-form>
</template>

<script setup lang="ts">
import { forage } from 'src/utils/foragePkg';
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import api from '../api';
import { devEnv } from 'src/router/routes';

interface BaseDt {
  phone: string;
  email: string;
  code: number | undefined;
  basePwd: string;
}

const { t } = useI18n();
const otp = ref(Array(6).fill(''));
const inputs = ref<HTMLInputElement[]>([]);
const emit = defineEmits(['done', 'edit']);
const mailCode = ref<string>();
const timeOutResend = devEnv ? 40 * 1000 : 180 * 1000;
const resendDisabled = computed(() => timer.value > 0);
const timer = ref(0);
let intervalId: ReturnType<typeof setInterval> | null = null;

// vue request
const { run: codeVfy, loading } = api.useCodeSend({
  onSuccess: async () => {
    const done = await forage<boolean>().setItem('regisDone', true);
    await forage<BaseDt>().removeItem('edit');
    emit('done', done);
  },
});
const { run: reCode } = api.useCodeReSend({});

const resendLabel = computed(() => {
  return resendDisabled.value
    ? `${t('sendCodeVfy.countIn')} ${timer.value}${t('sendCodeVfy.s')}`
    : t('sendCodeVfy.resend');
});
const countToResend = () => {
  timer.value = timeOutResend / 1000;

  if (intervalId !== null) {
    clearInterval(intervalId);
    intervalId = null;
  }

  intervalId = setInterval(() => {
    if (timer.value > 0) {
      timer.value--;
    } else {
      if (intervalId !== null) {
        clearInterval(intervalId);
        intervalId = null;
      }
    }
  }, 1000);
};

onMounted(async () => {
  const mailCheck = await forage<string>().getItem('baseIF');
  inputs.value[0]?.focus();
  mailCode.value = mailCheck;
  countToResend();
});

const isDisableSend = computed(() => {
  return otp.value.some((char) => char === '');
});

const handleKeyup = (index: number, e: KeyboardEvent) => {
  const key = e.key;
  const value = otp.value[index];

  if (key === 'Backspace') {
    if (!value && index > 0) {
      inputs.value[index - 1]?.focus();
    }
  } else if (key.match(/[0-9]/) && index < 5) {
    // Only number
    inputs.value[index + 1]?.focus();
  }
};

const handleOtpSend = async () => {
  const email = await forage<string>().getItem('baseIF');
  const verificationCode = otp.value.join('');

  if (!/^\d{6}$/.test(verificationCode) || !email) {
    return;
  }

  codeVfy({
    email,
    verificationCode,
  });
};

const handleResend = async () => {
  const email = await forage<string>().getItem('baseIF');
  reCode({ email });
  countToResend();
};

const infoEdit = async () => {
  await forage<string>().removeItem('baseIF');
  emit('edit');
};
</script>

<style scoped>
.code-vfy-container {
  font-family: Inter;
}

.otp-input:deep(.q-field__control) {
  width: 64px;
  height: 53px;
  align-items: center;
  border-radius: 7px;
  color: rgb(47, 89, 197);
  border: 1px solid rgba(120, 144, 180, 0.5);
}

body.isDark .otp-input:deep(.q-field__control) {
  background: rgba(43, 39, 68, 1);
}

body.isDark .otp-input:deep(.q-field__control:hover:before) {
  border-color: rgb(144, 138, 243);
}

body.isDark .otp-input:deep(.q-field__control:before) {
  border-color: rgb(97, 79, 145);
}

.otp-input:deep(.q-field__native) {
  text-align: center;
  font-size: 25px;
  line-height: 53px;
}
body.isDark .otp-input:deep(.q-field__native) {
  color: white;
}

.shine-btn {
  position: relative;
  padding: 0 0 0 6px;
  background: transparent;
  box-shadow: none;
  min-height: unset;
  overflow: visible;
}

.btn-label {
  position: relative;
  display: inline-block;
  font-weight: 600;
  font-size: 16px;
  color: rgba(5, 84, 230, 0.766);
}

.text-shine {
  position: absolute;
  top: 0;
  left: 0;
  white-space: nowrap;
  overflow: hidden;
  width: 0;
  color: #007bff;
  transition: width 0.6s ease;
}

.shine-btn:hover .text-shine {
  width: 100%;
}

.mail_code {
  margin-bottom: 3px;
}
.mail_addr .mail_code {
  text-align: start;
  font-weight: 400;
  font-size: 20px;
  line-height: 100%;
}
.send-code-btn {
  margin-bottom: 8px;
  width: 100%;
  height: 53px;
  border-radius: 7px;
  background: #7d3ffa;
  transition: background-color 0.3s ease, transform 0.2s ease,
    box-shadow 0.3s ease;
  color: white;
}
.send-code-btn:hover {
  background-color: #5e1ed4;
  transform: scale(1.05);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}
.send-code-btn:disabled {
  background-color: #554d4d;
  cursor: not-allowed;
}

@media (max-width: 600px) {
  .otp-input:deep(.q-field__control) {
    width: 54px;
    height: 45px;
  }

  .otp-input:deep(.q-field__native) {
    font-size: 20px;
  }
}

@media (max-width: 423px) {
  .otp-input:deep(.q-field__control) {
    width: 45px;
    height: 45px;
  }

  .otp-input:deep(.q-field__native) {
    font-size: 18px;
  }
}
</style>
