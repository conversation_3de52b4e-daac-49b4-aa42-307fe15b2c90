<template>
  <div class="done_container">
    <div>
      <q-card-section>
        <q-img class="done_icon" :src="DoneIcon" />
      </q-card-section>
    </div>
    <div>
      <q-card-section>
        <div class="done-tit">{{ t('registerDone.success') }}</div>
        <div class="show_mail">
          Email: <span class="done-mail"> {{ email }} </span>
        </div>
      </q-card-section>
      <div class="back_countdown">
        <span>{{ timeToLogin }}</span> {{ t('registerDone.count_text') }}
      </div>
      <q-card-section>
        <div class="flex items-center justify-center">
          <div class="forget-again">
            {{ t('registerDone.addMore') }}
          </div>
          <q-btn
            class="text-btn"
            :label="t('registerDone.help_this')"
            flat
            @click="() => emit('redirect')"
          />
        </div>
        <div>
          <q-btn
            class="back_login"
            :label="t('registerDone.back_login')"
            @click="backLogin"
          />
        </div>
      </q-card-section>
    </div>
  </div>
</template>

<script setup lang="ts">
import DoneIcon from 'src/assets/images/Done_icon.png';
import { forage } from 'src/utils/foragePkg';
import { onBeforeUnmount, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

// setup funcs

const { t } = useI18n();
const email = ref<string>();
const router = useRouter();
const emit = defineEmits<{
  (e: 'redirect'): void;
  (e: 'countdownSignUp'): void;
}>();

onMounted(async () => {
  const checkResult = await forage<string>().getItem('baseIF');
  email.value = checkResult;
});

const timeToLogin = ref(10);

// countdown

onMounted(() => {
  const timer = setInterval(() => {
    timeToLogin.value--;
    if (timeToLogin.value === 0) {
      emit('countdownSignUp');
      clearInterval(timer);
      router.push('/auth/login');
    }
  }, 1000);

  onBeforeUnmount(() => {
    clearInterval(timer);
  });
});

const backLogin = () => {
  emit('redirect');
  router.push('/auth/login');
};
</script>

<style scoped>
.done_container {
  font-family: Inter;
  display: flex;
  align-items: center;
  justify-content: center;
}
.done_container .done-card {
  padding: 20px 0;
  text-align: center;
}
.show_mail {
  font-size: 18px;
  margin-top: 16px;
}
.done_container .done-tit {
  font-weight: 500;
  font-size: 30px;
  line-height: 100%;
  letter-spacing: 0%;
}
:deep(.q-dark) {
  background: rgb(59, 47, 92);
}
.done_icon {
  width: 200px;
  height: 200px;
}

.back_countdown {
  font-weight: 400;
  font-size: 16.5px;
  line-height: 100%;
  color: rgba(146, 146, 146, 1);
  text-align: center;
}
.back_countdown span {
  color: black;
  font-weight: 700;
  font-size: 20px;
}
.forget-again {
  font-weight: 400;
  font-size: 15px;
}
.text-btn {
  font-weight: 400;
  font-size: 15px;
  line-height: 100%;
  text-align: center;
  color: #0256f0;
  padding: 0 0;
  padding-left: 6px;
}
.done-mail {
  font-weight: 700;
  font-size: 21.5px;
  line-height: 100%;
  letter-spacing: 0%;
  color: rgb(51, 135, 51);
}

.back_login {
  margin-top: 14px;
  width: 100%;
  height: 43px;
  border-radius: 7px;
  background: rgba(125, 63, 250, 1);
  color: rgba(255, 255, 255, 1);
}

@media (max-width: 599px) {
  .done_container {
    margin-top: 5rem;
  }
}
</style>
