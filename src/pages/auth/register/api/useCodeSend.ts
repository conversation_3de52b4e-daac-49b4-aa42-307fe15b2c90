import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

type CodeSendProps = {
  email: string;
  verificationCode: string;
};

/* eslint-disable-next-line @typescript-eslint/ban-types */
type CodeSendRes = {};

export const useCodeSend = ({ ...useProps }: UseProps) => {
  return requestProvider<CodeSendRes, CodeSendProps>(
    (props) => {
      const request = axiosProvider
        .post('/auth/verify-email', props)
        .then(({ data }) => data);
      return request;
    },
    {
      manual: true,
      ...useProps,
      onSuccess: async (res) => {
        if (useProps.onSuccess) useProps.onSuccess(res);
      },
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
};
