import hooks from 'src/hooks';
import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

type MailVfyProps = {
  email: string;
  phoneRegionCode: string | undefined;
  phoneNumber: string;
  password: string;
};

/* eslint-disable-next-line @typescript-eslint/ban-types */
type VfyMailRes = {};

export const useMailVfy = ({ ...useProps }: UseProps) => {
  return requestProvider<VfyMailRes, MailVfyProps>(
    (props) => {
      const request = axiosProvider
        .post('/auth/register', props)
        .then(({ data }) => data);
      return request;
    },
    {
      manual: true,
      ...useProps,
      onSuccess: (res) => {
        if (useProps.onSuccess) useProps.onSuccess(res);
        hooks.useSuccessNotify('OTP sent!! Please check your email');
      },
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
};
