import hooks from 'src/hooks';
import { axiosProvider } from 'src/utils/axiosProvider';
import { requestProvider } from 'src/utils/requestProvider';

type ReSendCodeProps = {
  email: string;
};

/* eslint-disable-next-line @typescript-eslint/ban-types */
type ResendCodeRes = {};

export const useCodeReSend = ({ ...useProps }: UseProps) => {
  return requestProvider<ResendCodeRes, ReSendCodeProps>(
    (props) => {
      const request = axiosProvider
        .post('/auth/resend-code', props)
        .then(({ data }) => data);
      return request;
    },
    {
      manual: true,
      ...useProps,
      onSuccess: async (res) => {
        if (useProps.onSuccess) useProps.onSuccess(res);
        hooks.useSuccessNotify('OTP resent! Please check your email');
      },
    },
    {
      noFeedback: true,
      noTempData: true,
    }
  );
};
