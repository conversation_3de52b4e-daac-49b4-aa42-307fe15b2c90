<template>
  <div class="register_container">
    <q-card>
      <q-card-section class="flex items-center q-gutter-x-sm">
        <q-img
          loading="eager"
          no-spinner
          no-transition
          fetchpriority="high"
          src="/images/logo.png"
          class="k28Register-logo"
        />
        <div class="register_tit">
          {{ t('registerPage.register_tit') }}
        </div>
      </q-card-section>
      <q-card-section
        v-if="(!nextCode && !done) || (!nextCode && !done && editNow)"
      >
        <RegisterBase @added-base="getInfoBase" />
      </q-card-section>
      <q-card-section v-if="nextCode && !done && editNow">
        <div style="display: grid; place-items: center">
          <div class="q-gutter-y-md">
            <div class="flex items-center">
              <q-icon name="info" class="warning-icon" />
              <div>
                {{ t('registerPage.warning') }}
              </div>
            </div>
            <SendCodeVfy @done="toFinish" @edit="edit" />
          </div>
        </div>
      </q-card-section>
      <q-card-section
        v-if="nextCode && done && !editNow"
        class="flex items-center justify-center"
      >
        <RegisterDone
          @redirect="handleRedirect"
          @countdown-sign-up="handleRedirect"
        />
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { forage } from 'src/utils/foragePkg';
import { onMounted, ref } from 'vue';
import RegisterBase from './components/RegisterBase.vue';
import RegisterDone from './components/RegisterDone.vue';
import SendCodeVfy from './components/SendCodeVfy.vue';
import { useI18n } from 'vue-i18n';

interface BaseDt {
  phone: string;
  email: string;
  code: number | undefined;
  basePwd: string;
}

const { t } = useI18n();
const nextCode = ref<boolean>(false);
const done = ref<boolean>(false);
const editNow = ref<BaseDt | undefined>();

onMounted(async () => {
  const checkCode = await forage<string>().getItem('baseIF');
  const checkDone = await forage<boolean>().getItem('regisDone');
  const checkEdit = await forage<BaseDt>().getItem('edit');
  nextCode.value = !!checkCode;
  done.value = !!checkDone;
  editNow.value = checkEdit;
});

// Next To Sendcode
const getInfoBase = async () => {
  nextCode.value = !!(await forage<string>().getItem('baseIF'));
  const canEdit = await forage<BaseDt>().getItem('edit');
  editNow.value = canEdit;
};
// Finish Da Register
const toFinish = async () => {
  const result = await forage<boolean>().getItem('regisDone');
  const noEdit = await forage<BaseDt>().getItem('edit');
  done.value = result;
  editNow.value = noEdit;
};

// redirect
const handleRedirect = async () => {
  await forage<string>().removeItem('baseIF');
  await forage<boolean>().removeItem('regisDone');
  await forage<BaseDt>().removeItem('edit');
  nextCode.value = false;
  done.value = false;
  editNow.value = undefined;
};

// edit info base
const edit = async () => {
  nextCode.value = false;
  const canEdit = await forage<BaseDt>().getItem('edit');
  editNow.value = canEdit;
};
</script>

<style scoped>
.register_container {
  max-width: 650px;
  margin: 0 auto;
  margin-top: 7.5rem;
  padding: 0 10px;
}

.register_container .q-card {
  padding: 10px 10px;
}

.register_tit {
  font-family: Roboto;
  font-weight: 700;
  font-size: 1.875rem;
  line-height: 100%;
}

.warning-icon {
  font-size: 1.375rem;
  margin-right: 0.34375rem;
}

body .isDark .register_tit {
  font-family: Inter;
}

.k28Register-logo {
  width: 1.875rem;
  height: 2.5rem;
}
</style>
