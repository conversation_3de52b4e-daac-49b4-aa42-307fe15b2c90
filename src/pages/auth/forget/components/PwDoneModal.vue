<template>
  <div class="done_container">
    <q-card class="done-card">
      <q-card-section>
        <q-img class="done_icon" :src="DoneIcon" />
      </q-card-section>
      <q-card-section>
        <div class="done-tit">
          {{ t('pwDoneModal.done_tit') }}
        </div>
        <div class="show_mail">
          Email: <span class="done-mail">{{ addedMail }}</span>
        </div>
      </q-card-section>
      <div class="back_countdown">
        <span>{{ timeToLogin }}</span> {{ t('pwDoneModal.count_text') }}
      </div>
      <q-card-section>
        <div class="flex items-center justify-center">
          <div class="forget-again">
            {{ t('pwDoneModal.forget_again') }}
          </div>
          <q-btn
            class="text-btn"
            :label="t('pwDoneModal.help_this')"
            flat
            @click="remakePass"
          />
        </div>
        <div>
          <q-btn
            class="back_login"
            :label="t('pwDoneModal.back_login')"
            @click="goLogin"
          />
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { storageHelper } from 'src/utils/foragePkg';
import { onBeforeUnmount, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import DoneIcon from 'src/assets/images/Done_icon.png';
import { useI18n } from 'vue-i18n';

// setup funcs
const { t } = useI18n();
const router = useRouter();
const props = defineProps<{ addedMail: string }>();
const emit = defineEmits<{
  (e: 'remake'): void;
  (e: 'reLogin'): void;
  (e: 'countdown'): void;
}>();
const timeToLogin = ref(10);

// countdown

onMounted(() => {
  const timer = setInterval(() => {
    timeToLogin.value--;
    if (timeToLogin.value === 0) {
      emit('countdown');
      clearInterval(timer);
      storageHelper<string>('mail_vfy').remove();
      router.push('/auth/login');
    }
  }, 1000);

  onBeforeUnmount(() => {
    clearInterval(timer);
  });
});
const remakePass = () => {
  if (props.addedMail) {
    emit('remake');
    storageHelper<string>('mail_vfy').remove();
  }
};

const goLogin = () => {
  emit('reLogin');
  storageHelper<string>('mail_vfy').remove();
  router.push('/auth/login');
};
</script>

<style scoped>
.done_container {
  max-width: 450px;
  font-family: Inter;
  margin-top: 13rem;
}
.done_container .done-card {
  padding: 20px 0;
  text-align: center;
}
.show_mail {
  font-size: 18px;
  margin-top: 16px;
}
.done_container .done-card .done-tit {
  font-weight: 500;
  font-size: 32px;
  line-height: 100%;
  letter-spacing: 0%;
}
:deep(.q-dark) {
  background: rgb(59, 47, 92);
}
.done_icon {
  width: 100px;
  height: 100px;
}

.back_countdown {
  font-weight: 400;
  font-size: 16.5px;
  line-height: 100%;
  color: rgba(146, 146, 146, 1);
}
.back_countdown span {
  color: black;
  font-weight: 700;
  font-size: 20px;
}
.forget-again {
  font-weight: 400;
  font-size: 15px;
}
.text-btn {
  font-weight: 400;
  font-size: 15px;
  line-height: 100%;
  text-align: center;
  color: #0256f0;
  padding: 0 0;
  padding-left: 6px;
}
.done-mail {
  font-weight: 700;
  font-size: 21.5px;
  line-height: 100%;
  letter-spacing: 0%;
  color: rgb(51, 135, 51);
}

.back_login {
  margin-top: 14px;
  width: 120px;
  height: 43px;
  border-radius: 7px;
  background: rgba(125, 63, 250, 1);
  color: rgba(255, 255, 255, 1);
}

@media (max-width: 599px) {
  .done_container {
    margin-top: 5rem;
  }
}
</style>
