<template>
  <div v-if="warnings" class="warn-container">
    <div
      v-if="step === 'addMail' || step === 'newPw'"
      class="flex items-center"
    >
      <q-icon name="info" class="warning-icon" />
      <div class="warn-content">
        {{ warnings }}
      </div>
    </div>
    <div v-else-if="step === 'sendCode'" class="flex items-center">
      <q-icon name="info" class="warning-icon" />
      <div class="warn-content">
        {{ warnings }} <span class="mail_to">{{ sendTo }}</span>
      </div>
      <q-btn class="text-btn shine-btn" @click="handleBack" flat>
        <span class="btn-label">
          <span class="text-base">{{ t('forgetWarings.edit_mail') }}</span>
          <span class="text-shine">{{ t('forgetWarings.edit_mail') }}</span>
        </span>
      </q-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { storageHelper } from 'src/utils/foragePkg';
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const currentMail = ref<string | null>();
const emit = defineEmits<{
  (e: 'currMail', mail: string | null): void;
}>();

const props = defineProps<{
  warnings: string | undefined;
  step: string;
  sendTo: string | null;
}>();
watch(
  () => props.sendTo,
  (currM) => {
    currentMail.value = currM;
  }
);

// back addMail
const handleBack = () => {
  if (currentMail.value) {
    if (currentMail.value) {
      emit('currMail', currentMail.value);
      storageHelper<string>('mail_vfy').remove();
    }
  }
};
</script>

<style scoped>
.warn-container {
  margin-top: 0.67rem;
  font-family: Inter;
  text-align: center;
}
.warning-icon {
  font-size: 1.375rem;
  margin-right: 0.34375rem;
}
@media (max-width: 768px) {
  .warning-icon {
    font-size: 1rem;
  }
}
.warn-content {
  font-weight: 400;
  font-size: 14px;
}

.mail_to {
  font-weight: 700;
  font-size: 21.5px;
  line-height: 100%;
  letter-spacing: 0%;
}

.shine-btn {
  position: relative;
  padding: 0 0 0 6px;
  background: transparent;
  box-shadow: none;
  min-height: unset;
  overflow: visible;
}

.btn-label {
  position: relative;
  display: inline-block;
  font-weight: 600;
  font-size: 16px;
  color: rgba(5, 84, 230, 0.766);
}

.text-shine {
  position: absolute;
  top: 0;
  left: 0;
  white-space: nowrap;
  overflow: hidden;
  width: 0;
  color: #007bff;
  transition: width 0.6s ease;
}

.shine-btn:hover .text-shine {
  width: 100%;
}
</style>
