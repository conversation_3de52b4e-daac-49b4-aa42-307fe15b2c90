<template>
  <div class="newPw-container">
    <q-form @submit="handleSet">
      <!-- New password -->
      <div class="pwAndConfirm-label">
        {{ t('newPwFormStep.pw_label') }}
      </div>
      <q-input
        class="custom-input"
        outlined
        ref="inputs"
        v-model="newPwd"
        :type="isVisibleSetting ? 'text' : 'password'"
        :rules="passwordRules"
        lazy-rules
        autocomplete="off"
        :placeholder="t('newPwFormStep.enterPw')"
      >
        <template v-slot:append>
          <q-icon
            :name="isVisibleSetting ? 'visibility' : 'visibility_off'"
            class="cursor-pointer"
            @click="() => (isVisibleSetting = !isVisibleSetting)"
          />
        </template>
      </q-input>

      <!-- confirm password -->
      <div class="pwAndConfirm-label">
        {{ t('newPwFormStep.pw_confirm_label') }}
      </div>
      <q-input
        class="custom-input"
        outlined
        v-model="confirmPwd"
        :type="isVisibleSetting ? 'text' : 'password'"
        :rules="confirmPasswordRules"
        lazy-rules
        autocomplete="off"
        :placeholder="t('newPwFormStep.enterPw')"
      >
        <template v-slot:append>
          <q-icon
            :name="isVisibleSetting ? 'visibility' : 'visibility_off'"
            class="cursor-pointer"
            @click="() => (isVisibleSetting = !isVisibleSetting)"
          />
        </template>
      </q-input>

      <q-btn
        type="submit"
        :label="t('newPwFormStep.pw_submit')"
        :loading="setting"
        class="setNew_pw"
      />
    </q-form>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import api from '../api';
import { useI18n } from 'vue-i18n';
import { QInput } from 'quasar';

const { t } = useI18n();
const inputs = ref<QInput>();
const props = defineProps<{ token: string }>();
const emit = defineEmits(['show-notify']);
// Refs & state
const isVisibleSetting = ref(false);
const newPwd = ref('');
const confirmPwd = ref('');

onMounted(() => {
  inputs.value?.focus?.();
});

// Validation rules
const passwordRules = [
  (val: string) => !!val || t('newPwFormStep.enterPw'),
  (val: string) =>
    /^(?=.*[A-Z])(?=.*\d)[A-Za-z\d]{8,}$/.test(val) ||
    t('newPwFormStep.pw_rule'),
];

const confirmPasswordRules = [
  (val: string) => !!val || t('newPwFormStep.enterPw'),
  (val: string) => val === newPwd.value || t('newPwFormStep.not_match_pw'),
];

// Submit handler
const { run: set, loading: setting } = api.useSetPw({
  onSuccess: () => emit('show-notify'),
});

const handleSet = () => {
  set({
    token: props.token,
    new_password: confirmPwd?.value,
  });
};
</script>

<style scoped>
.newPw-container {
  font-family: Inter;
  margin-top: 25.5px;
}
.pwAndConfirm-label {
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0%;
}
.custom-input {
  margin-top: 10px;
  margin-bottom: 16px;
}
.custom-input:deep(.q-field__control) {
  background-color: rgba(234, 234, 234, 1);
}
body.isDark .custom-input:deep(.q-field__control) {
  background: rgba(43, 39, 68, 1);
}

.custom-input:deep(input::placeholder) {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0%;
}
.setNew_pw {
  margin-top: 16px;
  width: 100%;
  height: 53px;
  border-radius: 7px;
  background: #7d3ffa;
  transition: background-color 0.3s ease, transform 0.2s ease,
    box-shadow 0.3s ease;
  color: white;
}
.setNew_pw:hover {
  background-color: #5e1ed4;
  transform: scale(1.05);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}
</style>
