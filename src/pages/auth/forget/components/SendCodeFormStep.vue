<template>
  <div class="send-code-container items-center q-mt-lg">
    <div class="code-vfy-tit">
      {{ t('sendCodeFormStep.code_vfy_tit') }}
    </div>

    <div class="row q-gutter-md justify-start q-mb-xl">
      <q-input
        v-for="(_, index) in otp"
        :key="index"
        ref="inputs"
        v-model="otp[index]"
        mask="X"
        maxlength="1"
        class="otp-input"
        @keyup="handleKeyup(index, $event)"
        type="password"
      />
    </div>

    <q-btn
      :disable="isDisableSend"
      :label="t('sendCodeFormStep.next_step_but')"
      class="send-code-btn"
      :loading="sending"
      @click="handleSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import hooks from 'src/hooks';
import { computed, onMounted, ref } from 'vue';
import api from '../api';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const otp = ref(Array(6).fill(''));
const inputs = ref<HTMLInputElement[]>([]);

const emit = defineEmits<{
  (e: 'tokenGot', token: string): void;
}>();

onMounted(() => {
  inputs.value[0]?.focus();
});

const { run: codeSend, loading: sending } = api.useSendCode({
  onSuccess: (res) => {
    const token = res?.token;
    if (token) {
      emit('tokenGot', token);
      hooks.useSuccessNotify('Successfully send code');
    }
  },
});

const isDisableSend = computed(() => {
  return otp.value.some((char) => char === '');
});

const handleKeyup = (index: number, e: KeyboardEvent) => {
  const key = e.key;
  const value = otp.value[index];

  if (key === 'Backspace') {
    if (!value && index > 0) {
      inputs.value[index - 1]?.focus();
    }
  } else if (key.match(/[0-9]/) && index < 5) {
    // Only number
    inputs.value[index + 1]?.focus();
  }
};

const handleSubmit = () => {
  const otpSend = otp.value.join('');
  if (!/^\d{6}$/.test(otpSend)) {
    return;
  }
  const optNum = Number(otpSend);
  codeSend({ CodeVerify: optNum });
};
</script>

<style scoped>
.send-code-container {
  font-family: Inter;
}

.code-vfy-tit {
  text-align: start;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  margin-bottom: 12px;
}

.otp-input:deep(.q-field__control) {
  width: 64px;
  height: 53px;
  align-items: center;
  border-radius: 7px;
  background: #eaeaea;
  color: rgb(47, 89, 197);
}

body.isDark .otp-input:deep(.q-field__control) {
  background: rgba(43, 39, 68, 1);
}

body.isDark .otp-input:deep(.q-field__control:hover:before) {
  border-color: rgb(144, 138, 243);
}

body.isDark .otp-input:deep(.q-field__control:before) {
  border-color: rgb(97, 79, 145);
}

.otp-input:deep(.q-field__native) {
  text-align: center;
  font-size: 25px;
  line-height: 53px;
  color: black;
}
body.isDark .otp-input:deep(.q-field__native) {
  color: white;
}

.send-code-btn {
  width: 100%;
  height: 53px;
  border-radius: 7px;
  background: #7d3ffa;
  transition: background-color 0.3s ease, transform 0.2s ease,
    box-shadow 0.3s ease;
  color: white;
}
.send-code-btn:hover {
  background-color: #5e1ed4;
  transform: scale(1.05);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}
.send-code-btn:disabled {
  background-color: #554d4d;
  cursor: not-allowed;
}

@media (max-width: 600px) {
  .otp-input:deep(.q-field__control) {
    width: 54px;
    height: 45px;
  }

  .otp-input:deep(.q-field__native) {
    font-size: 20px;
  }
}

@media (max-width: 423px) {
  .otp-input:deep(.q-field__control) {
    width: 45px;
    height: 45px;
  }

  .otp-input:deep(.q-field__native) {
    font-size: 18px;
  }
}
</style>
