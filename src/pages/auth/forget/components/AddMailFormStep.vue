<template>
  <div class="add_mail_container">
    <q-form @submit="handleSend">
      <div class="addM-tit">
        {{ t('addMailFormStep.addM_tit') }}
      </div>
      <q-input
        outlined
        autocomplete="off"
        v-model="email"
        :rules="[
          (val: string) =>
            !!(val && val.toString().trim()) || t('addMailFormStep.enterMail'),
          (val: string) =>
            /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) ||
            t('addMailFormStep.must_is_mail'),
        ]"
        lazy-rules
        inputmode="email"
        :placeholder="t('addMailFormStep.enterMail')"
        class="addM-input"
      />

      <q-btn
        class="add-vfy"
        unelevated
        type="submit"
        :label="t('addMailFormStep.vfy_mail')"
        :loading="checking"
      />
    </q-form>

    <div class="back_login">
      <router-link id="link_back" to="/auth/login">
        <q-btn dense unelevated> {{ t('addMailFormStep.back_login') }} </q-btn>
      </router-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import hooks from 'src/hooks';
import { devEnv } from 'src/router/routes';
import { storageHelper } from 'src/utils/foragePkg';
import { computed, ref } from 'vue';
import api from '../api';
import { useI18n } from 'vue-i18n';

// setup funcs
const { t } = useI18n();
const emit = defineEmits(['mail-added']);
const props = defineProps<{
  letFillMail: string | null;
}>();
const handleSend = () => {
  if (mailVfy.value)
    send({
      Email: mailVfy.value,
    });
};
const { run: send, loading: checking } = api.useVfyMail({
  onSuccess: () => {
    storageHelper<string | null>('mail_vfy').setItem(email.value);
    emit('mail-added');
    hooks.useSuccessNotify('Send Successflly! Please check your Email');
  },
});

const email = ref<string | null>(
  devEnv ? props.letFillMail ?? '<EMAIL>' : props.letFillMail ?? ''
);

const mailVfy = computed(() => email.value);
</script>

<style scoped>
.add_mail_container {
  font-family: Inter;
  margin-top: 2.3rem;
}
.addM-tit {
  font-weight: 400;
  font-size: 1rem;
}

.add_mail_container .add-vfy {
  width: 100%;
  height: 53px;
  border-radius: 7px;
  background: #7d3ffa;
  transition: background-color 0.3s ease, transform 0.2s ease,
    box-shadow 0.3s ease;
  color: white;
}

.add_mail_container .add-vfy:hover {
  background-color: #5e1ed4;
  transform: scale(1.05);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.add_mail_container .back_login {
  font-weight: 400;
  font-size: 1rem;
  line-height: 1;
  letter-spacing: 0;
  text-align: center;
  margin-top: 1.5625rem;
}

.add_mail_container #link_back {
  color: #0256f0;
}
.addM-input {
  margin-bottom: 0.5rem;
  margin-top: 0.7rem;
}
.addM-input:deep(.q-field__control) {
  background-color: rgba(234, 234, 234, 1);
}
body.isDark .addM-input:deep(.q-field__control) {
  background: rgba(43, 39, 68, 1);
}

.addM-input:deep(input::placeholder) {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0%;
}
</style>
