<template>
  <div class="forget_container">
    <div class="forget_tit" v-if="forgetStep !== 'done'">
      {{ t('forgetPage.forget_tit') }}
    </div>
    <ForgetWarnings
      v-if="forgetStep !== 'done'"
      :warnings="forgetWarnings[forgetStep]"
      :step="forgetStep"
      :sendTo="addedMail || ''"
      @currMail="resetMail"
    />
    <AddMailFormStep
      v-if="forgetStep === 'addMail'"
      @mail-added="addDone"
      :letFillMail="backMail"
    />
    <SendCodeFormStep
      v-if="forgetStep === 'sendCode'"
      @tokenGot="token = $event"
    />
    <NewPwFormStep
      v-if="forgetStep === 'newPw'"
      :token="token || ''"
      @show-notify="showNotify"
    />
    <PwDoneModal
      v-if="forgetStep === 'done'"
      :addedMail="addedMail || ''"
      @remake="resetAll"
      @reLogin="resetAll"
      @countdown="resetAll"
    />
  </div>
</template>

<script setup lang="ts">
import { storageHelper } from 'src/utils/foragePkg';
import { computed, onMounted, ref } from 'vue';
import SendCodeFormStep from './components/SendCodeFormStep.vue';
import AddMailFormStep from './components/AddMailFormStep.vue';
import { useI18n } from 'vue-i18n';
import NewPwFormStep from './components/NewPwFormStep.vue';
import PwDoneModal from './components/PwDoneModal.vue';
import ForgetWarnings from './components/ForgetWarnings.vue';

// setup funcs
const { t } = useI18n();
const token = ref<string | null>(null);
const addedMail = ref<string | null>(null);
const isCheckCode = ref<boolean | null>(false);
const backMail = ref<string | null>(null);
const nfyShow = ref<boolean>(false);

const forgetWarnings = computed<Record<string, string>>(() => ({
  addMail: t('forgetPage.addMail'),
  sendCode: t('forgetPage.sendCode'),
  newPw: t('forgetPage.newPw'),
  done: 'User set ok // 重設密碼成功', // Prompt hidden;'done' controls display!!
}));

const forgetStep = computed(() => {
  const addM = addedMail.value;
  const tk = token.value;
  const nfy = nfyShow.value;
  const back = backMail.value;

  if (addM && tk && nfy) return 'done';
  if (addM && tk && !nfy) return 'newPw';
  if (addM && !tk && !back) return 'sendCode';
  return 'addMail';
});

onMounted(() => {
  addedMail.value = storageHelper<string | null>('mail_vfy').getItem();
  isCheckCode.value = storageHelper<boolean>('isCodeChecked').getItem();
});

const addDone = () => {
  addedMail.value = storageHelper<string | null>('mail_vfy').getItem();
  backMail.value = null;
};
const resetMail = (usedMail: string | null) => {
  backMail.value = usedMail;
  addedMail.value = null;
  token.value = null;
};

const resetAll = () => {
  backMail.value = null;
  addedMail.value = null;
  token.value = null;
  nfyShow.value = false;
};

const showNotify = () => {
  nfyShow.value = true;
};
</script>

<style scoped>
.forget_container {
  max-width: 500px;
  margin: 0 auto;
  margin-top: 7.5rem;
  padding: 0 10px;
}

.forget_tit {
  text-align: start;
  font-family: Roboto;
  font-weight: 700;
  font-size: 1.875rem;
  line-height: 100%;
}

body.isDark .forget_tit {
  font-family: Inter;
}

@media (max-width: 599px) {
  .forget_container {
    margin-top: 4rem;
  }
}
</style>
