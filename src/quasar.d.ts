/// <reference types="@quasar/app-vite" />
interface VirgilRes<DATA> {
  code: number;
  msg: string;
  data: DATA;
}

type UseProps<DATA = unknown, Params = unknown> = {
  onSuccess?: (res?: DATA, params?: [Params]) => void;
  onError?: (error?: Error) => void;
  onAfter?: (args?: [Params]) => void;
  mockAccs?: { email: string; pwd: string; label: string; color: string }[];
};

type RoleK28 = 'agent' | 'user';
